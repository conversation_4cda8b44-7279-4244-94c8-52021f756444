@echo off
REM Set JAVA_HOME to JDK 8
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_202
set PATH=%JAVA_HOME%\bin;%PATH%

REM Check if encryption password is provided
if "%1"=="" (
    echo Usage: start-app.bat [encryption-password] [profile]
    echo Example: start-app.bat mypassword stage
    exit /b 1
)

REM Set default profile if not provided
set PROFILE=%2
if "%PROFILE%"=="" set PROFILE=stage

echo Starting CIAM Backend with JDK 8...
echo Profile: %PROFILE%
echo Java Version:
java -version

REM Start the application
mvn spring-boot:run "-Dspring-boot.run.profiles=%PROFILE%" "-Djasypt.encryptor.password=%1"
