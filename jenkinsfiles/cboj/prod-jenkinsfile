pipeline {
    agent any
    tools {
        maven 'Maven'
        jdk 'Java'
    }

    stages {
        stage('Init') {
            steps {
                script {
                    jarVersion = env['Version']
                    nexusSettings = [
                            'nexusUrl'        : 'nexusnonprod.capitalbank.jo:8443',
                            'sourceRepo'      : 'UAT',
                            'targetRepo'      : 'Production',
                            'groupId'         : '4be2e19f-42da-4571-80fd-19077a31ce58',
                            'artifactId'      : 'ciam-backend'
                    ]
                    deploySettings = [
                            'jenkinsCreds'    : 'ssh-ciam-prod',
                            'hosts'           : ['************', '************'],
                            'destTempFolder'  : '/home/<USER>/tmp',
                            'destTargetFolder': '/home/<USER>/ciam-cboj',
                            'destFile'        : 'ciam-backend.jar',
                            'serviceName'     : 'ciam-backend',
                            'deployInterval'  : 15
                    ]
                }
            }
        }

        stage('Get Jar from Nexus') {
            steps {
                script {
                    def fileName = "${nexusSettings['artifactId']}-${jarVersion}"
                    withCredentials([usernameColonPassword(credentialsId: 'Nexus', variable: 'NEXUS_CREDENTIALS')]) {
                        sh """curl -u ${NEXUS_CREDENTIALS} -o ${nexusSettings['artifactId']}.jar -k 'https://${nexusSettings['nexusUrl']}/repository/openbanking-releases/${nexusSettings['sourceRepo']}/cboj/${nexusSettings['groupId']}/${nexusSettings['artifactId']}/${jarVersion}/${fileName}.jar'
                          curl -u ${NEXUS_CREDENTIALS} -o pom.xml -k 'https://${nexusSettings['nexusUrl']}/repository/openbanking-releases/${nexusSettings['sourceRepo']}/cboj/${nexusSettings['groupId']}/${nexusSettings['artifactId']}/${jarVersion}/${fileName}.pom'"""
                    }
                }
            }
        }

      /*  stage('Republish to Nexus') {
            steps {
                script {
                    nexusArtifactUploader(
                            nexusVersion: 'nexus3',
                            protocol: 'https',
                            nexusUrl: nexusSettings['nexusUrl'],
                            groupId: "${nexusSettings['targetRepo']}/cboj/${nexusSettings['groupId']}",
                            version: jarVersion,
                            repository: 'openbanking-releases',
                            credentialsId: 'Nexus',
                            artifacts: [
                                    [
                                            artifactId: nexusSettings['artifactId'],
                                            classifier: '',
                                            file      : "${nexusSettings['artifactId']}.jar",
                                            type      : 'jar'
                                    ],
                                    [
                                            artifactId: nexusSettings['artifactId'],
                                            classifier: '',
                                            file      : 'pom.xml',
                                            type      : 'pom'
                                    ]
                            ]
                    );
                }
            }
        } */

        stage('Deploy To Prod') {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(
                            credentialsId: deploySettings['jenkinsCreds'],
                            keyFileVariable: 'identity',
                            passphraseVariable: '',
                            usernameVariable: 'userName')]) {
                        deploySettings['hosts'].eachWithIndex { host, i ->
                            echo "Deploying to host ${host}"

                            sh "scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i " + identity +
                                    " ${nexusSettings['artifactId']}.jar ${userName}@${host}:${deploySettings['destTempFolder']}/${deploySettings['destFile']}"
                            sh "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i " + identity +
                                    " ${userName}@${host} 'sudo mv ${deploySettings['destTempFolder']}/${deploySettings['destFile']} ${deploySettings['destTargetFolder']}/${deploySettings['destFile']}; sudo systemctl restart ${deploySettings['serviceName']}'"

                            echo "Waiting ${deploySettings['deployInterval']} seconds to start CIAM..."
                            sleep(time: deploySettings['deployInterval'], unit: "SECONDS")
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
    }
}
