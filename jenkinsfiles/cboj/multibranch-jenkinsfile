pipeline {
    agent any
    tools {
        maven 'Maven'
        jdk 'Java'
    }

    stages {
        stage('Init') {
            steps {
                script {
                    branch2env = [
                            'master': 'uat',
                            'staging': 'staging'
                    ]
                    environments = [
                            'staging': [
                                    'jenkinsCreds'    : 'ssh-ciam-staging',
                                    'hosts'           : ['hq-sso-stg01.capitalbank.jo', 'hq-sso-stg02.capitalbank.jo'],
                                    'destTempFolder'  : '/home/<USER>/tmp',
                                    'destTargetFolder': '/home/<USER>/ciam-cboj',
                                    'destFile'        : 'ciam-backend.jar',
                                    'serviceName'     : 'ciam-backend',
                                    'securityChecks'  : true,
                                    'deployInterval'  : 15
                            ],
                            'uat': [
                                    'jenkinsCreds'    : 'ssh-ciam-staging',
                                    'hosts'           : ['************', '************'],
                                    'destTempFolder'  : '/home/<USER>/tmp',
                                    'destTargetFolder': '/home/<USER>/ciam-cboj',
                                    'destFile'        : 'ciam-backend.jar',
                                    'serviceName'     : 'ciam-backend',
                                    'securityChecks'  : true,
                                    'jarPublication'  : 'UAT',
                                    'deployInterval'  : 15
                            ]
                    ]
                    targetEnv = environments[branch2env[env.BRANCH_NAME]]
                }
            }
        }

        stage('Build') {
            steps {
                sh 'mvn clean package'
            }
        }

        stage('Fortify Clean') {
            when {
                expression {
                    return targetEnv['securityChecks']
                }
            }
            steps {
                fortifyClean addJVMOptions: '', buildID: 'ciam-backend', logFile: '', maxHeap: ''
            }
        }

        stage('Fortify Translate') {
            when {
                expression {
                    return targetEnv['securityChecks']
                }
            }
            steps {
                fortifyTranslate addJVMOptions: '', buildID: 'ciam-backend', excludeList: '', logFile: '', maxHeap: '', projectScanType: fortifyMaven3(
                        mavenInstallationName: '(Default)',
                        mavenOptions: '-DskipTests=true')
            }
        }

        stage('Fortify Remote Scan') {
            when {
                expression {
                    return targetEnv['securityChecks']
                }
            }
            steps {
                fortifyRemoteScan buildID: 'ciam-backend' , uploadSSC: [appName: 'ciam-backend', appVersion: 'staging']
            }
        }

        stage('Nexus Security SCA Scan') {
            when {
                expression {
                    return targetEnv['securityChecks']
                }
            }
            steps {
                nexusPolicyEvaluation(
                        iqApplication: 'ciam-backend_staging',
                        iqInstanceId: 'ciam-backend_staging',
                        iqScanPatterns: [[scanPattern: '**/*.*']],
                        iqStage: 'build',
                        iqOrganization: '8f2edc4b5c88432eb3759db8f3f19c11'
                )
            }
        }

        stage("Publish To Nexus") {
            when {
                expression {
                    return targetEnv['jarPublication']
                }
            }
            steps {
                script {
                    def pom = readMavenPom(file: 'pom.xml')
                    def jarArchive = findFiles(glob: "target/*.jar")[0]
                    def appMinorVersion = pom.version.substring(0, pom.version.lastIndexOf('.'))
                    def appVersion = "${appMinorVersion}.${BUILD_NUMBER}"
                    nexusArtifactUploader(
                            nexusVersion: 'nexus3',
                            protocol: 'https',
                            nexusUrl: 'nexusnonprod.capitalbank.jo:8443',
                            groupId: "${targetEnv['jarPublication']}/cboj/4be2e19f-42da-4571-80fd-19077a31ce58",
                                version: appVersion,
                                repository: 'openbanking-releases',
                                credentialsId: 'Nexus',
                                artifacts: [
                                        [
                                                artifactId: pom.artifactId,
                                                classifier: '',
                                                file: jarArchive.path,
                                                type: 'jar'
                                        ],
                                        [
                                                artifactId: pom.artifactId,
                                                classifier: '',
                                                file: 'pom.xml',
                                                type: 'pom'
                                        ]
                                ]
                        );
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    def jarArchive = findFiles(glob: "target/*.jar")[0]

                    withCredentials([sshUserPrivateKey(
                            credentialsId: targetEnv['jenkinsCreds'],
                            keyFileVariable: 'identity',
                            passphraseVariable: '',
                            usernameVariable: 'userName')]) {
                        targetEnv['hosts'].eachWithIndex { host, i ->
                            echo "Deploying to host ${host}"

                            sh "scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i " + identity +
                                    " ${jarArchive.path} ${userName}@${host}:${targetEnv['destTempFolder']}/${targetEnv['destFile']}"
                            sh "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i " + identity +
                                    " ${userName}@${host} 'sudo mv ${targetEnv['destTempFolder']}/${targetEnv['destFile']} ${targetEnv['destTargetFolder']}/${targetEnv['destFile']}; sudo systemctl restart ${targetEnv['serviceName']}'"

                            echo "Waiting ${targetEnv['deployInterval']} seconds to start CIAM..."
                            sleep(time: targetEnv['deployInterval'], unit:"SECONDS")
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
    }
}
