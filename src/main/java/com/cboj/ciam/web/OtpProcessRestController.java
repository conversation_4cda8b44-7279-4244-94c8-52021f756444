package com.cboj.ciam.web;

import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.auth.AuthenticationOtpService;
import com.cboj.ciam.web.model.OtpModel;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Controller for OTP (One-Time Password) processes.
 */
@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
public class OtpProcessRestController {

  private final AuthenticationOtpService authenticationOtpService;

  /**
   * Resends OTP request.
   *
   * @param consentRef The consent external identifier.
   * @param request    The HttpServletRequest object.
   * @return Request's reference number.
   */
  @PostMapping(path = RestConstants.Paths.WEB.LOGIN_REQUEST_OTP, produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<OtpModel> resendOtp(@RequestParam @NotNull UUID consentRef,
                                               HttpServletRequest request,
                                               HttpServletResponse response) {
    return authenticationOtpService.requestOtp(consentRef, request)
        .thenApply(session -> {
          CsrfToken token = (CsrfToken) session.getAttribute(SessionConstants.CSRF_TOKEN_ATTR);

          response.setHeader(token.getHeaderName(), token.getToken());
          return OtpModel.builder()
              .otpRenewTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_RENEW_TIME))
              .otpRequestAttemptsLeft((Integer) session.getAttribute(SessionConstants.REQUEST_ATTEMPT))
              .otpSubmitAttemptsLeft((Integer) session.getAttribute(SessionConstants.SUBMIT_ATTEMPT))
              .build();
        });
  }
}
