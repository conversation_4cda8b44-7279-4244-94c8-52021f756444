package com.cboj.ciam.web.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Data for OTP process")
public class OtpModel extends BaseModel {

  @Schema(description = "OTP number")
  private String otp;

  @Schema(description = "OTP expiration time")
  private Long otpExpirationTime;

  @Schema(description = "OTP renew time")
  private Long otpRenewTime;

  @Schema(description = "Validation OTP error")
  protected Boolean validationOtpError;

  @Schema(description = "Renewal OTP error")
  protected Boolean renewalOtpError;

  @Schema(description = "Submit OTP error")
  protected Boolean submitOtpError;

  @Schema(description = "Customer eligibility error")
  protected Boolean eligibleError;

  @Schema(description = "Request OTP attempts left")
  protected Integer otpRequestAttemptsLeft;

  @Schema(description = "Submit OTP attempts left")
  protected Integer otpSubmitAttemptsLeft;

  @Override
  public String toString() {
    try {
      return new ObjectMapper().writeValueAsString(this);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Error processing JSON", e);
    }
  }
}
