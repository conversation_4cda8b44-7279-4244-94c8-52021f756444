package com.cboj.ciam.web.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * Represents the data transfer object for user credentials.
 */
@Getter
@Setter
@SuperBuilder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Data for login user")
public class LoginModel extends BaseModel {

  @NotNull(message = "User name not specified")
  @Schema(description = "User name")
  private String userName;

  @NotNull(message = "Password not specified")
  @Schema(description = "Password")
  private String password;

  @Schema(description = "User name key")
  private String userNameKey;

  @Schema(description = "Password key")
  private String passwordKey;

  @Schema(description = "Response type")
  private String response_type;

  @Schema(description = "Client ID")
  private String client_id;

  @Schema(description = "Redirect URI")
  private String redirect_uri;

  @Schema(description = "Scope")
  private String scope;

  @Schema(description = "State")
  private String state;

  @Schema(description = "Error message")
  private Boolean error;

  @Schema(description = "Consent expiration or invalid error")
  private Boolean consentError;

  @Schema(description = "Error message key")
  private String errorMessageKey;

}
