package com.cboj.ciam.web.model;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.ais.AisPermissionDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Represents the data transfer object for user consent.
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Data for consent")
public class AisConsentModel extends ConsentModel {

  @Schema(description = "Consent permissions")
  private List<AisPermissionDto> permissions;

  @Schema(description = "User accounts")
  private List<ConsentAccountDto> accounts;

}
