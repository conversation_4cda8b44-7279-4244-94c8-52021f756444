package com.cboj.ciam.web.model;

import com.cboj.ciam.api.ConsentAccountDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Represents the data transfer object for user consent.
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Data for consent")
public class PisConsentOutputModel extends ConsentModel {

  @Schema(description = "Payment type")
  private String paymentType;

  @Schema(description = "User accounts")
  private List<ConsentAccountDto> accounts;

}
