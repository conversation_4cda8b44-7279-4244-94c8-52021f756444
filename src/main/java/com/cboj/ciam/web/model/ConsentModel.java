package com.cboj.ciam.web.model;

import com.cboj.ciam.web.ConsentAction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;


@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class ConsentModel extends BaseModel {

  @Schema(description = "User full name")
  private String customerName;

  @Schema(description = "Client name")
  private String clientName;

  @Schema(description = "Consent Action (consent/reject)")
  private ConsentAction action;

  @Schema(description = "Error message")
  protected Boolean error;
}
