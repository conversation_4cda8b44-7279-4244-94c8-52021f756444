package com.cboj.ciam.web.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CafConsentInputModel extends ConsentModel {

  @Schema(description = "User account reference")
  private String accounts;

  @Schema(description = "User account reference")
  private String accountRef;

}
