package com.cboj.ciam.web;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * Provides a method to validate if two URIs are equivalent based on the scheme, host, and port.
 */
@Slf4j
public class UriValidator {

  /**
   * Validates if two URIs are equivalent based on the scheme, host, and port.
   *
   * @param original   the original URI to be validated
   * @param comparable the comparable URI to be validated against
   * @return true if the URIs are equivalent, false otherwise
   */
  public static boolean validate(String original, String comparable) {
    if (StringUtils.isEmpty(original) || StringUtils.isEmpty(comparable)) {
      return false;
    }

    URI comparableUri;
    URI originalUri;
    try {
      comparableUri = new URI(comparable);
      originalUri = new URI(original);
    } catch (URISyntaxException e) {
      log.error(e.getLocalizedMessage(), e);
      return false;
    }

    if (originalUri.isAbsolute()) {
      if (!originalUri.getScheme().equals(comparableUri.getScheme())) {
        return false;
      }
    } else {
      if (comparableUri.isAbsolute()) {
        return false;
      }
    }

    if (!originalUri.getHost().equals(comparableUri.getHost())) {
      return false;
    } else {
      return originalUri.getPort() == comparableUri.getPort();
    }
  }
}
