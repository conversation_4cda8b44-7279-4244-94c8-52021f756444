package com.cboj.ciam.web;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.auth.AuthenticationService;
import com.cboj.ciam.service.auth.ais.AisAuthorizationService;
import com.cboj.ciam.service.auth.caf.CafAuthorizationService;
import com.cboj.ciam.service.auth.pis.PisAuthorizationService;
import com.cboj.ciam.service.data.SessionService;
import com.cboj.ciam.web.model.AisConsentModel;
import com.cboj.ciam.web.model.CafConsentInputModel;
import com.cboj.ciam.web.model.PisConsentInputModel;
import com.cboj.ciam.web.model.LoginModel;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Controller handles the authorization process for a user.
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@Hidden
public class AuthorizationController {

  private final AuthenticationService authenticationService;
  private final AisAuthorizationService aisAuthorizationService;
  private final PisAuthorizationService pisAuthorizationService;
  private final CafAuthorizationService cafAuthorizationService;
  private final SessionService sessionService;

  @Value("${ciam.encryption.enable}")
  private boolean encryptionEnabled;

  @Value("${ciam.base.url}")
  private String baseUri;

  /**
   * Login user. Returns the login page.
   *
   * @param response_type Response type
   * @param client_id     Client ID
   * @param redirect_uri  TPP redirect URI
   * @param scope         TPP client scope
   * @param state         Request state
   * @param consentRef    Consent external identifier
   * @param model         The model object to hold data
   * @return Login page
   */
  @GetMapping(path = RestConstants.Paths.WEB.LOGIN, produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<String> login(
      @RequestParam @NotNull String response_type,
      @RequestParam @NotNull String client_id,
      @RequestParam @NotNull String redirect_uri,
      @RequestParam @Nullable String scope,
      @RequestParam @Nullable String state,
      @RequestParam @Nullable String error,
      @RequestParam @Nullable String consentError,
      @RequestParam @NotNull UUID consentRef,
      Model model) {
    return authenticationService.loginForm(response_type, client_id, redirect_uri, scope, state, consentRef)
        .handle((loginModel, ex) -> {
          if (ex != null) {
            log.error(ex.getLocalizedMessage(), ex);
            loginModel = LoginModel.builder().consentError(true).build();
          } else {
            if (Boolean.parseBoolean(error)) {
              loginModel.setError(true);
            }
            if (Boolean.parseBoolean(consentError)) {
              loginModel.setConsentError(true);
            }
          }
          model.addAttribute(RestConstants.LoginPage.DATA_ATTR, loginModel);
          model.addAttribute(RestConstants.LoginPage.ENCRYPTION_ENABLED, encryptionEnabled);
          return RestConstants.Templates.LOGIN_PAGE;
        });
  }

  /**
   * Processes user consents for authorization.
   *
   * @param consentModel The UserConsentDto object containing the consent details.
   * @param request      The HTTP request object.
   * @return The response entity contains the redirect URL with an authCode if consent is approved, or the redirect URL if consent is
   * declined.
   */
  @PostMapping(path = RestConstants.Paths.WEB.AIS_UPDATE_CONSENT, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public CompletableFuture<ResponseEntity<Object>> processAisConsents(
      @ModelAttribute(RestConstants.LoginPage.DATA_ATTR) @NotNull AisConsentModel consentModel, HttpServletRequest request) {
    return CompletableFuture.supplyAsync(() -> {
          HttpSession session = request.getSession(false);
          if (session == null) {
            throw new UnauthorizedException("No session found");
          }

          return session;
        })
        .thenCompose(session -> {
          if (consentModel.getAction().equals(ConsentAction.CONSENT)) {
            return aisAuthorizationService.approveConsents(consentModel)
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.AUTH_URI));
          } else {
            return aisAuthorizationService.declineConsents(consentModel.getConsentRef())
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.REDIRECT_URI));
          }
        })
        .thenApply(redirectUri -> {
          HttpSession session = request.getSession(false);
          sessionService.deleteSession(session);
          return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, redirectUri).build();
        });
  }

  /**
   * Processes user consents for authorization.
   *
   * @param consentModel The UserConsentDto object containing the consent details.
   * @param request      The HTTP request object.
   * @return The response entity contains the redirect URL with an authCode if consent is approved, or the redirect URL if consent is
   * declined.
   */
  @PostMapping(path = RestConstants.Paths.WEB.PIS_UPDATE_CONSENT, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public CompletableFuture<ResponseEntity<Object>> processPisConsents(
      @ModelAttribute(RestConstants.LoginPage.DATA_ATTR) @NotNull PisConsentInputModel consentModel, HttpServletRequest request) {
    return CompletableFuture.supplyAsync(() -> {
          HttpSession session = request.getSession(false);
          if (session == null) {
            throw new UnauthorizedException("No session found");
          }

          return session;
        })
        .thenCompose(session -> {
          if (consentModel.getAction().equals(ConsentAction.CONSENT)) {
            String accountRef = StringUtils.isEmpty(consentModel.getAccounts()) ? consentModel.getAccountRef() : consentModel.getAccounts();
            return pisAuthorizationService.approveConsents(consentModel.getConsentRef(), accountRef)
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.AUTH_URI));
          } else {
            return pisAuthorizationService.declineConsents(consentModel.getConsentRef())
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.REDIRECT_URI));
          }
        })
        .thenApply(redirectUri -> {
          HttpSession session = request.getSession(false);
          sessionService.deleteSession(session);
          return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, redirectUri).build();
        });
  }

  /**
   * Processes user consents for authorization.
   *
   * @param consentModel The UserConsentDto object containing the consent details.
   * @param request      The HTTP request object.
   * @return The response entity contains the redirect URL with an authCode if consent is approved, or the redirect URL if consent is
   * declined.
   */
  @PostMapping(path = RestConstants.Paths.WEB.CAF_UPDATE_CONSENT, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public CompletableFuture<ResponseEntity<Object>> processCafConsents(
      @ModelAttribute(RestConstants.LoginPage.DATA_ATTR) @NotNull CafConsentInputModel consentModel, HttpServletRequest request) {
    return CompletableFuture.supplyAsync(() -> {
          HttpSession session = request.getSession(false);
          if (session == null) {
            throw new UnauthorizedException("No session found");
          }

          return session;
        })
        .thenCompose(session -> {
          if (consentModel.getAction().equals(ConsentAction.CONSENT)) {
            String accountRef = StringUtils.isEmpty(consentModel.getAccounts()) ? consentModel.getAccountRef() : consentModel.getAccounts();
            return cafAuthorizationService.approveConsents(consentModel.getConsentRef(), accountRef)
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.AUTH_URI));
          } else {
            return cafAuthorizationService.declineConsents(consentModel.getConsentRef())
                .thenApply(unused -> (String) session.getAttribute(SessionConstants.REDIRECT_URI));
          }
        })
        .thenApply(redirectUri -> {
          HttpSession session = request.getSession(false);
          sessionService.deleteSession(session);
          return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, redirectUri).build();
        });
  }

  /**
   * Changes the locale of the user and returns the corresponding template to be displayed.
   *
   * @return The template name to be displayed.
   */
  @PostMapping(path = RestConstants.Paths.WEB.SET_LOCALE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public String changeLocale(HttpServletRequest request, Model model) {
    if (checkRefererUri(request)) {
      return "redirect:" + request.getHeader(HttpHeaders.REFERER);
    }

    //If referer URI checking failed
    model.addAttribute(RestConstants.LoginPage.DATA_ATTR, LoginModel.builder().consentError(true).build());
    return RestConstants.Templates.LOGIN_PAGE;
  }

  /**
   * Checks if the referer URI from the given HttpServletRequest matches specific criteria.
   *
   * @param request The HttpServletRequest object containing the HTTP request information.
   * @return true if the referer URI matches the criteria, false otherwise.
   */
  private boolean checkRefererUri(HttpServletRequest request) {
    String originalUri = ServletUriComponentsBuilder
        .fromRequestUri(request)
        .replacePath(null)
        .build()
        .toString();

    String refererUri = request.getHeader(HttpHeaders.REFERER);

    URI referer = UriComponentsBuilder.fromUriString(refererUri).build().toUri();

    return referer.getPath().equals(RestConstants.Paths.WEB.LOGIN)
        && (UriValidator.validate(baseUri, refererUri) || UriValidator.validate(originalUri, refererUri));
  }

}
