package com.cboj.ciam.web;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Controller
@RequiredArgsConstructor
public class TransactionInfoController {

  private final TransactionInfoService transactionInfoService;

  /**
   * Resends OTP request.
   *
   * @param consentRef The consent external identifier.
   * @param request    The HttpServletRequest object.
   * @return Request's reference number.
   */
  @PostMapping(path = RestConstants.Paths.WEB.PIS_TRANSACTION_INFO, produces = MediaType.TEXT_PLAIN_VALUE)
  public CompletableFuture<String> getTransactionInfo(@RequestParam @NotNull String consentRef,
                                                      @RequestParam String accountRef,
                                                      Model model,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
    return transactionInfoService.getTransactionInfo(consentRef, accountRef)
        .thenApply(transactionInfoDto -> {
          if (transactionInfoDto == null) {
            throw new NotFoundException("TransactionInfo not found");
          }

          CsrfToken token = (CsrfToken) request.getSession(false).getAttribute(SessionConstants.CSRF_TOKEN_ATTR);
          response.setHeader(token.getHeaderName(), token.getToken());

          model.addAttribute("consent", transactionInfoDto);
          return "consent_common :: pis-payment-details";
        });
  }
}
