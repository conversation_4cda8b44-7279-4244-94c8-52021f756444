package com.cboj.ciam.web;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.ErrorCodes;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.LoginProcessException;
import com.cboj.ciam.service.auth.AccountOwnershipException;
import com.cboj.ciam.service.auth.AuthenticationOtpService;
import com.cboj.ciam.service.auth.AuthenticationService;
import com.cboj.ciam.service.auth.EligibilityValidationException;
import com.cboj.ciam.service.mulesoft.OtpRequestException;
import com.cboj.ciam.service.mulesoft.OtpSendingException;
import com.cboj.ciam.web.model.LoginModel;
import com.cboj.ciam.web.model.OtpModel;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.service.auth.ConsentException;

/**
 * Controller for OTP (One-Time Password) processes.
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@Hidden
public class OtpProcessController {

  @Value("${ciam.base.url}")
  private String baseUri;

  private final AuthenticationService authenticationService;
  private final AuthenticationOtpService authenticationOtpService;

  @Autowired
  private MessageSource messageSource;

  /**
   * Logs in a user with the provided login data.
   *
   * @param loginData The user consent data used for login.
   * @param model     The model object used for adding login data or error data.
   * @param request   The HttpServletRequest object.
   * @return Result of the login process. If successful, it returns the consent page template name. If there is an error, it redirects to
   * the login page with the error information.
   */
  @PostMapping(path = RestConstants.Paths.WEB.LOGIN_OTP, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public CompletableFuture<String> login(
      @ModelAttribute(RestConstants.LoginPage.DATA_ATTR) @NotNull LoginModel loginData,
      Model model, HttpServletRequest request) {
    return authenticationService.login(loginData, request)
        .handle((otpModel, ex) -> {
          if (ex != null) {
            log.error(ex.getLocalizedMessage(), ex);

            if (ex.getCause() instanceof AccountOwnershipException) {
              model.addAttribute(RestConstants.ErrorPage.ACCOUNT_OWNERSHIP_EX, true);
              return RestConstants.Templates.ERROR_PAGE;
            }

            if (checkRefererUri(request)) {
              return processRedirectUri(ex, request.getHeader(HttpHeaders.REFERER));
            }

            //check consent errors
            Throwable rootCause = ex;
            while (rootCause.getCause() != null && rootCause != rootCause.getCause()) {
              rootCause = rootCause.getCause();
            }
            if (rootCause instanceof ConsentException) {
              ConsentException consentEx = (ConsentException) rootCause;

              LoginModel.LoginModelBuilder builder = LoginModel.builder();
              builder.consentError(true);
              builder.errorMessageKey(consentEx.getMessageKey());

              model.addAttribute(RestConstants.LoginPage.DATA_ATTR, builder.build());
              return RestConstants.Templates.LOGIN_PAGE;
            }
            if (rootCause instanceof UnauthorizedException) {
              UnauthorizedException unauthorizedException = (UnauthorizedException) rootCause;

              LoginModel.LoginModelBuilder builder = LoginModel.builder();
              builder.consentError(true);
              builder.errorMessageKey(unauthorizedException.getMessageKey());

              model.addAttribute(RestConstants.LoginPage.DATA_ATTR, builder.build());
              return RestConstants.Templates.LOGIN_PAGE;
            }


            //If referer URI checking failed
            model.addAttribute(RestConstants.LoginPage.DATA_ATTR, LoginModel.builder().consentError(true).build());

            return RestConstants.Templates.LOGIN_PAGE;
          }
          model.addAttribute(RestConstants.LoginPage.DATA_ATTR, otpModel);
          return RestConstants.Templates.OTP_PAGE;
        });
  }

  /**
   * Processes the OTP request and returns the OTP page template.
   *
   * @param consentRef The consent external identifier
   * @param model      The model to be used for rendering the OTP page
   * @param request    The HttpServletRequest object
   * @return The OTP page template as a String
   */
  @GetMapping(path = RestConstants.Paths.WEB.LOGIN_OTP)
  public String processOtp(@RequestParam @NotNull UUID consentRef, Model model, HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    OtpModel otpModel = OtpModel.builder()
        .consentRef(consentRef)
        .otpExpirationTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_EXPIRATION_TIME))
        .otpRenewTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_RENEW_TIME))
        .build();

    model.addAttribute(RestConstants.LoginPage.DATA_ATTR, otpModel);
    return RestConstants.Templates.OTP_PAGE;
  }

  /**
   * Validates the OTP (One-Time Password) provided by the user.
   *
   * @param otpModel The OTP (One-Time Password) data object containing the user's input.
   * @param request  The HttpServletRequest object.
   * @param model    The Spring MVC model object to be used for adding attributes.
   * @return Result of the OTP validation.
   */
  @PostMapping(path = RestConstants.Paths.WEB.LOGIN_SUBMIT_OTP,
      consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public CompletableFuture<String> validateOtp(@ModelAttribute(RestConstants.LoginPage.DATA_ATTR) @NotNull OtpModel otpModel,
                                               HttpServletRequest request, Model model) {
    return authenticationOtpService.validateOtp(otpModel, request)
        .handle((consentDto, ex) -> {
          HttpSession session = request.getSession(false);
          if (ex != null) {
            if (ex.getCause() instanceof OtpSendingException) {
              otpModel.setSubmitOtpError(true);
            } else if (ex.getCause() instanceof OtpRequestException) {
              otpModel.setRenewalOtpError(true);
            } else if (ex.getCause() instanceof EligibilityValidationException) {
              otpModel.setEligibleError(true);
            } else {
              otpModel.setValidationOtpError(true);
            }

            otpModel.setOtpRenewTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_RENEW_TIME));
            otpModel.setOtpExpirationTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_EXPIRATION_TIME));
            otpModel.setOtpRequestAttemptsLeft((Integer) session.getAttribute(SessionConstants.REQUEST_ATTEMPT));
            otpModel.setOtpSubmitAttemptsLeft((Integer) session.getAttribute(SessionConstants.SUBMIT_ATTEMPT));
            model.addAttribute(RestConstants.LoginPage.DATA_ATTR, otpModel);
            return RestConstants.Templates.OTP_PAGE;
          }
          model.addAttribute(RestConstants.LoginPage.DATA_ATTR, consentDto);

          session.setAttribute(SessionConstants.SUCCESSFUL_OTP_ATTR, true);

          String page;
          switch ((String) session.getAttribute(SessionConstants.CURRENT_SCOPE_ATTR)) {
            case SsoConstants.Scope.ACCOUNTS:
              page = RestConstants.Templates.CONSENT_PAGE_AIS;
              break;
            case SsoConstants.Scope.PAYMENTS:
              page = RestConstants.Templates.CONSENT_PAGE_PIS;
              break;
            case SsoConstants.Scope.FUNDS_CONFIRMATIONS:
              page = RestConstants.Templates.CONSENT_PAGE_CAF;
              break;
            default:
              page = RestConstants.Templates.ERROR_PAGE;
          }

          return page;
        });
  }

  /**
   * Performs the failed OTP operation.
   *
   * @param consentRef the consent external identifier to be used
   * @return URL of the failed OTP operation.
   */
  @PostMapping(path = RestConstants.Paths.WEB.LOGIN_FAILED_OTP)
  public CompletableFuture<ResponseEntity<String>> failedOtp(@RequestParam @NotNull UUID consentRef, HttpServletRequest request) {
    return authenticationOtpService.failedOtp(consentRef, request)
        .handle((url, ex) -> {
          HttpSession session = request.getSession(false);
          if (ex != null) {
            log.error(ex.getLocalizedMessage(), ex);

            if (StringUtils.isEmpty(url)) {
              url =  (String) session.getAttribute(SessionConstants.REDIRECT_URI);
            }
          }

          session.invalidate();

          return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, url).build();
        });
  }

  /**
   * Process the redirect URI based on the given exception and login URI.
   *
   * @param ex       The Throwable object representing the exception.
   * @param loginUri The login URI to be processed.
   * @return The processed redirect URI.
   */
  private String processRedirectUri(Throwable ex, String loginUri) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(loginUri);
    if (ex.getCause() instanceof LoginProcessException) {
      builder.replaceQueryParam(RestConstants.LoginPage.ERROR);
      builder.queryParam(RestConstants.LoginPage.CONSENT_ERROR, Boolean.TRUE.toString());
    } else {
      builder.replaceQueryParam(RestConstants.LoginPage.CONSENT_ERROR);
      builder.queryParam(RestConstants.LoginPage.ERROR, Boolean.TRUE.toString());
    }
    return "redirect:" + builder.build();
  }

  /**
   * Checks if the Referer URI of a given HttpServletRequest is valid.
   *
   * @param request The HttpServletRequest object from which the Referer URI will be obtained.
   * @return True if the Referer URI is valid, false otherwise.
   */
  private boolean checkRefererUri(HttpServletRequest request) {
    String originalUri = ServletUriComponentsBuilder
        .fromRequestUri(request)
        .replacePath(null)
        .build()
        .toString();

    String refererUri = request.getHeader(HttpHeaders.REFERER);

    return UriValidator.validate(baseUri, refererUri) && UriValidator.validate(originalUri, refererUri);
  }
}
