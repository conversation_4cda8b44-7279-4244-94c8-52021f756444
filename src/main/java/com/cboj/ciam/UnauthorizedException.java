package com.cboj.ciam;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * Exception indicating that the request was unauthorized.
 */

@Getter
public class UnauthorizedException extends ApplicationHttpStatusCodeException {

  private final String messageKey;

  public UnauthorizedException(String message) {
    super(HttpStatus.UNAUTHORIZED, message);
    messageKey = null;
  }

  public UnauthorizedException(String message, String messageKey) {
    super(HttpStatus.UNAUTHORIZED, message);
    this.messageKey = messageKey;
  }
}
