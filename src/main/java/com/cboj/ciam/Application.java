package com.cboj.ciam;

import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.consts.SecurityConstants;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@OpenAPIDefinition(
    info = @Info(
        title = "CIAM Backend Service",
        description = "A set of operations to implement different aspects of consent management."),
    tags = {
        @Tag(name = "Open Banking API", description = "Public Open Banking API operations to let Third Pary Providers work with consents."),
        @Tag(name = "MuleSoft Consent API", description = "An internal API for Open Banking MuleSoft applications to work with consents."),
        @Tag(name = "Mobile Backend API",
            description = "An internal API for the Mobile Backend application to pass the mobile-based consent flow."),
        @Tag(name = "Administration API", description = "An internal API for various administration purposes.")

    })
@SecuritySchemes({
    @SecurityScheme(
        name = SecurityConstants.ADMIN_API_BEARER_TOKEN, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(clientCredentials = @OAuthFlow(tokenUrl = "${keycloak.uri.token-uri}", scopes = {
            @OAuthScope(name = SsoConstants.Scope.ADMIN_API)
        }))
    ),
    @SecurityScheme(
        name = SecurityConstants.MOBILE_API_BEARER_TOKEN, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(clientCredentials = @OAuthFlow(tokenUrl = "${keycloak.uri.token-uri}", scopes = {
            @OAuthScope(name = SsoConstants.Scope.MOBILE_API)
        }))
    ),
    @SecurityScheme(
        name = SecurityConstants.MULE_API_BEARER_TOKEN, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(clientCredentials = @OAuthFlow(tokenUrl = "${keycloak.uri.token-uri}", scopes = {
            @OAuthScope(name = SsoConstants.Scope.MULE_API)
        }))
    ),
    @SecurityScheme(
        name = SecurityConstants.OAUTH_CLIENT_CRED_SCHEME, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(clientCredentials = @OAuthFlow(tokenUrl = "/oauth2/token", scopes = {
            @OAuthScope(name = SsoConstants.Scope.ACCOUNTS), @OAuthScope(name = SsoConstants.Scope.PAYMENTS),
            @OAuthScope(name = SsoConstants.Scope.FUNDS_CONFIRMATIONS)
        }))
    ),
    @SecurityScheme(
        name = SecurityConstants.OAUTH_CLIENT_CRED_SCHEME, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(clientCredentials = @OAuthFlow(tokenUrl = "/oauth2/token", scopes = {
            @OAuthScope(name = SsoConstants.Scope.ACCOUNTS), @OAuthScope(name = SsoConstants.Scope.PAYMENTS),
            @OAuthScope(name = SsoConstants.Scope.FUNDS_CONFIRMATIONS)
        }))
    ),
    @SecurityScheme(
        name = SecurityConstants.OAUTH_AUTH_CODE_SCHEME, type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(authorizationCode = @OAuthFlow(tokenUrl = "/oauth2/token", authorizationUrl = "/web/login", scopes = {
            @OAuthScope(name = SsoConstants.Scope.ACCOUNTS), @OAuthScope(name = SsoConstants.Scope.PAYMENTS),
            @OAuthScope(name = SsoConstants.Scope.FUNDS_CONFIRMATIONS)
        }))
    )
})
@Slf4j
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}
