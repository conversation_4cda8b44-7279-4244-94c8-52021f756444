package com.cboj.ciam.jpa.caf;

import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.DebtorBound;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "caf_consent")
public class CafConsent extends Consent implements DebtorBound {

  @Column(name = "debtor_acc_ref")
  private String debtorAccRef;

  @Column(name = "expiry_date")
  private LocalDateTime expiryDate;

  @Column(name = "debtor_acc_scheme")
  private AccountScheme debtorAccScheme;
}
