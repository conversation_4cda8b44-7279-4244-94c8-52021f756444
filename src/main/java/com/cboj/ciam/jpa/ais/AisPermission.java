package com.cboj.ciam.jpa.ais;

import com.cboj.ciam.jpa.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "ais_permission")
public class AisPermission extends BaseEntity {

  @Column(name = "create_date")
  private LocalDateTime createDate;

  @Column(name = "update_date")
  private LocalDateTime updateDate;

  @Column(name = "handle")
  private String handle;

  @Column(name = "description_en")
  private String descriptionEn;

  @Column(name = "description_ar")
  private String descriptionAr;

  @Column(name = "active")
  private Boolean active;

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(
      name = "ais_consent_permission",
      joinColumns = @JoinColumn(name = "permission_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "consent_id", referencedColumnName = "id"))
  @ToString.Exclude
  @JsonIgnore
  private Set<AisConsent> consents = new LinkedHashSet<>();

}
