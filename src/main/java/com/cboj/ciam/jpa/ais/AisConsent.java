package com.cboj.ciam.jpa.ais;

import com.cboj.ciam.jpa.Consent;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "ais_consent")
public class AisConsent extends Consent {

  @Column(name = "expiry_date")
  private LocalDateTime expiryDate;

  @Column(name = "transaction_from_date")
  private LocalDateTime transactionFromDate;

  @Column(name = "transaction_to_date")
  private LocalDateTime transactionToDate;

  @OneToMany(
      mappedBy = "consent",
      fetch = FetchType.LAZY,
      cascade = CascadeType.ALL)
  @Fetch(value = FetchMode.SELECT)
  protected Set<AisConsentAccount> accounts;

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(
      name = "ais_consent_permission",
      joinColumns = @JoinColumn(name = "consent_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "permission_id", referencedColumnName = "id"))
  @ToString.Exclude
  protected Set<AisPermission> permissions;

}
