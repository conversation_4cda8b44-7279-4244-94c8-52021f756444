package com.cboj.ciam.jpa.ais;

import com.cboj.ciam.jpa.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = "ais_consent_account")
public class AisConsentAccount extends BaseEntity {

  @Column(name = "create_date")
  private LocalDateTime createDate;

  @Column(name = "update_date")
  private LocalDateTime updateDate;

  @Column(name = "account_ref")
  private String accountRef;

  @Column(name = "account_number")
  private String accountNumber;

  @Column(name = "currency")
  private String currency;

  @Column(name = "category")
  private String category;

  @Column(name = "consent_id")
  private Long consentId;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  @JoinColumn(name = "consent_id", insertable = false, updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Fetch(value = FetchMode.SELECT)
  @JsonIgnore
  private AisConsent consent;
}
