package com.cboj.ciam.jpa;

import com.cboj.ciam.NotFoundException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * Account identification scheme.
 */
@Getter
public enum AccountScheme {

  ACCOUNT_NUMBER("JO.OB.BankAccountNumber"),
  IBAN("JO.OB.IBAN"),
  ALIAS("JO.OB.CliqAlias"),
  MOBILE("JO.OB.MobileNumber");

  @JsonValue
  private final String value;

  AccountScheme(String value) {
    this.value = value;
  }

  public static AccountScheme getFromValue(String value) {
    return Arrays.stream(values()).filter(c -> c.value.equals(value)).findFirst()
        .orElseThrow(() -> new NotFoundException(String.format("AccountScheme not found for value {%s}", value)));
  }
}
