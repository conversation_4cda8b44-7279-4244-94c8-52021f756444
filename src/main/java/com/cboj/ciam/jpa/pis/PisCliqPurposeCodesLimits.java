package com.cboj.ciam.jpa.pis;

import com.cboj.ciam.jpa.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "pis_cliq_purpose_codes_limits")
public class PisCliqPurposeCodesLimits extends BaseEntity {

  @Column(name = "create_date")
  private LocalDateTime createDate;

  @Column(name = "update_date")
  private LocalDateTime updateDate;

  @Column(name = "payment_category_code")
  private String paymentCategoryCode;

  @Column(name = "limit")
  private Integer limit;

  @Column(name = "payment_purpose_code")
  private String paymentPurposeCode;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;
}
