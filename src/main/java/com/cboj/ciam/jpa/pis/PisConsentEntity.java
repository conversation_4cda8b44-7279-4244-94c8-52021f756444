package com.cboj.ciam.jpa.pis;

import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.DebtorBound;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "pis_consent")
public class PisConsentEntity extends Consent implements DebtorBound {

  @Column(name = "instruction_identification")
  private String instructionIdentification;

  @Column(name = "endtoend_identification")
  private String endToEndIdentification;

  @Column(name = "amount")
  private Double amount;

  @Column(name = "creditor_acc_ref")
  private String creditorAccRef;

  @Column(name = "debtor_acc_ref")
  private String debtorAccRef;

  @Column(name = "payment_purpose_code")
  private String paymentPurposeCode;

  @Column(name = "payment_purpose_desc")
  private String paymentPurposeDesc;

  @Column(name = "payment_reason")
  private String paymentReason;

  @Column(name = "payment_ref")
  private String paymentRef;

  @Column(name = "creditor_name_en")
  private String creditorNameEn;

  @Column(name = "creditor_name_ar")
  private String creditorNameAr;

  @Column(name = "creditor_address_line1")
  private String creditorAddressLine1;

  @Column(name = "creditor_address_line2")
  private String creditorAddressLine2;

  @Column(name = "creditor_address_line3")
  private String creditorAddressLine3;

  @Column(name = "creditor_city")
  private String creditorCity;

  @Column(name = "creditor_state")
  private String creditorState;

  @Column(name = "creditor_postcode")
  private String creditorPostcode;

  @Column(name = "creditor_country_code")
  private String creditorCountryCode;

  @Column(name = "creditor_country_name")
  private String creditorCountryName;

  @Column(name = "charge_type")
  private String chargeType;

  @Column(name = "currency")
  @Enumerated(EnumType.STRING)
  private PaymentCurrency currency;

  @Column(name = "local_instrument")
  @Enumerated(EnumType.STRING)
  private LocalInstrument localInstrument;

  @Column(name = "payment_type")
  @Enumerated(EnumType.STRING)
  private PaymentType paymentType;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  protected ConsentStatus status;

  @Column(name = "creditor_acc_scheme")
  @Enumerated(EnumType.STRING)
  private AccountScheme creditorAccScheme;

  @Column(name = "debtor_acc_scheme")
  @Enumerated(EnumType.STRING)
  private AccountScheme debtorAccScheme;

  @Column(name = "agent_identification")
  private String agentIdentification;

  @Column(name = "agent_schema")
  private String agentSchema;

  @Column(name = "creditor_currency")
  private String creditorCurrency;

  @Column(name = "beneficiary_first_name")
  private String beneficiaryFirstName;

  @Column(name = "beneficiary_middle_name")
  private String beneficiaryMiddleName;

  @Column(name = "beneficiary_last_name")
  private String beneficiaryLastName;

  @Column(name = "beneficiary_bic_code")
  private String beneficiaryBicCode;

  @Column(name = "creditor_secondary_scheme_name")
  private String creditorSecondaryIdentificationSchemeName;

  @Column(name = "creditor_secondary_identification")
  private String creditorSecondaryIdentification;

}
