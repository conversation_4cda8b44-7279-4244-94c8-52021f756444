package com.cboj.ciam.jpa.pis;

import com.cboj.ciam.jpa.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "pis_paymentpurpose_codes")
public class PaymentPurposeEntity extends BaseEntity {

  @Column(name = "create_date")
  private LocalDateTime createDate;

  @Column(name = "update_date")
  private LocalDateTime updateDate;

  @Column(name = "payment_category_code")
  private String paymentCategoryCode;

  @Column(name = "description_payment_category", columnDefinition = "TEXT")
  private String descriptionPaymentCategory;

  @Column(name = "payment_purpose_code")
  private String paymentPurposeCode;

  @Column(name = "description_payment_purpose", columnDefinition = "TEXT")
  private String descriptionPaymentPurpose;
}
