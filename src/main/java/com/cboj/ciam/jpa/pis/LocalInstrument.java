package com.cboj.ciam.jpa.pis;

import com.cboj.ciam.NotFoundException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * The domestic payment method
 */
@Getter
public enum LocalInstrument {

  INTERNAL("Internal"),
  DOMESTIC_CLIQ("DomesticCliq"),
  DOMESTIC("Domestic");

  @JsonValue
  private final String value;

  LocalInstrument(String value) {
    this.value = value;
  }

  public static LocalInstrument getFromValue(String value) {
    return Arrays.stream(values()).filter(c -> c.value.equals(value)).findFirst()
        .orElseThrow(() -> new NotFoundException(String.format("LocalInstrument not found for value {%s}", value)));
  }
}
