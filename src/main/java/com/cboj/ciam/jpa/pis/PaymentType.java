package com.cboj.ciam.jpa.pis;

import com.cboj.ciam.NotFoundException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * The type of the payment.
 */
@Getter
public enum PaymentType {

  SINGLE("Single");

  @JsonValue
  private final String value;

  PaymentType(String value) {
    this.value = value;
  }

  public static PaymentType getFromValue(String value) {
    return Arrays.stream(values()).filter(c -> c.value.equals(value)).findFirst()
        .orElseThrow(() -> new NotFoundException(String.format("PaymentType not found for value {%s}", value)));
  }
}
