package com.cboj.ciam.jpa;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@MappedSuperclass
public abstract class Consent extends BaseEntity {

  @Column(name = "create_date")
  protected LocalDateTime createDate;

  @Column(name = "update_date")
  protected LocalDateTime updateDate;

  @Column(name = "consent_ref")
  protected UUID consentRef;

  @Column(name = "auth_code")
  protected String authCode;

  @Column(name = "redirect_url")
  protected String redirectUrl;

  @Column(name = "refresh_token")
  protected String refreshToken;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  protected ConsentStatus status;

  @Column(name = "client_app_id")
  protected String clientAppId;

  @Column(name = "client_app_name")
  protected String clientAppName;

  @Column(name = "customer_ref")
  protected String customerRef;

  @Column(name = "sso_customer_ref")
  protected String ssoCustomerRef;
}
