package com.cboj.ciam.jpa;

import com.cboj.ciam.NotFoundException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * The ConsentStatus enum represents the possible consent statuses:
 * NEW, ACTIVE, REVOKED, REJECT, and EXPIRED.
 */
@Getter
public enum ConsentStatus {

  NEW("New"),
  ACTIVE("Active"),
  REVOKED("Revoked"),
  REJECTED("Rejected"),
  EXPIRED("Expired"),
  CONSUMED("Consumed");

  @JsonValue
  private final String value;

  ConsentStatus(String value) {
    this.value = value;
  }

  public static ConsentStatus getFromValue(String value) {
    return Arrays.stream(values()).filter(c -> c.value.equals(value)).findFirst()
        .orElseThrow(() -> new NotFoundException(String.format("ConsentStatus not found for value {%s}", value)));
  }

}
