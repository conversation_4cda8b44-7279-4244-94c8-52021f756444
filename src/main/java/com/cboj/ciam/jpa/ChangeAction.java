package com.cboj.ciam.jpa;

import com.cboj.ciam.NotFoundException;

import java.util.Arrays;

/**
 * Enumeration representing different change actions.
 */
public enum ChangeAction {

  CREATE("create"),
  UPDATE("update");

  private final String value;

  ChangeAction(String value) {
    this.value = value;
  }

  public static ChangeAction getFromValue(String value) {
    return Arrays.stream(values()).filter(c -> c.value.equals(value)).findFirst()
        .orElseThrow(() -> new NotFoundException(String.format("ChangeAction not found for value {%s}", value)));
  }
}
