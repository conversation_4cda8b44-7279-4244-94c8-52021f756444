package com.cboj.ciam.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * The DataRepository interface extends the JpaRepository and JpaSpecificationExecutor interfaces.
 * It is a generic interface that can handle any type of BaseEntity.
 *
 * @param <T> the type of the BaseEntity
 */
public interface DataRepository<T extends BaseEntity> extends JpaRepository<T, Long>, JpaSpecificationExecutor<T> {
}
