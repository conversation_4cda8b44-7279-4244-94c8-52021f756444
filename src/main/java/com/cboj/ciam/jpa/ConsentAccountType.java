package com.cboj.ciam.jpa;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "ais_consent_account_type")
public class ConsentAccountType extends BaseEntity {

  @Column(name = "create_date")
  private LocalDateTime createDate;

  @Column(name = "update_date")
  private LocalDateTime updateDate;

  @Column(name = "gb_code")
  private String gbCode;

  @Column(name = "account_class")
  private String accountClass;

  @Column(name = "description_en")
  private String descriptionEn;

  @Column(name = "description_ar")
  private String descriptionAr;
}
