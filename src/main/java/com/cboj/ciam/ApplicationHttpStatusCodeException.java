package com.cboj.ciam;

import lombok.Getter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientResponseException;

import java.nio.charset.Charset;

@Getter
public class ApplicationHttpStatusCodeException extends RestClientResponseException {
  private static final long serialVersionUID = 5696804447651587810L;

  private final HttpStatus statusCode;

  /**
   * Construct a new instance with an {@link HttpStatus} and status text.
   *
   * @param statusCode the status code
   * @param statusText the status text
   */
  protected ApplicationHttpStatusCodeException(HttpStatus statusCode, String statusText) {
    this(statusCode, statusText, null, null, null);
  }

  /**
   * Construct instance with an {@link HttpStatus}, status text, content, and a response charset.
   *
   * @param statusCode      the status code
   * @param statusText      the status text
   * @param responseHeaders the response headers, may be {@code null}
   * @param responseBody    the response body content, may be {@code null}
   * @param responseCharset the response body charset, may be {@code null}
   */
  protected ApplicationHttpStatusCodeException(HttpStatus statusCode,
                                               String statusText,
                                               @Nullable HttpHeaders responseHeaders,
                                               @Nullable byte[] responseBody,
                                               @Nullable Charset responseCharset) {

    this(getMessage(statusCode, statusText),
        statusCode, statusText, responseHeaders, responseBody, responseCharset);
  }

  /**
   * Construct instance with an {@link HttpStatus}, status text, content, and a response charset.
   *
   * @param message         the exception message
   * @param statusCode      the status code
   * @param statusText      the status text
   * @param responseHeaders the response headers, may be {@code null}
   * @param responseBody    the response body content, may be {@code null}
   * @param responseCharset the response body charset, may be {@code null}
   */
  protected ApplicationHttpStatusCodeException(String message,
                                               HttpStatus statusCode,
                                               String statusText,
                                               @Nullable HttpHeaders responseHeaders,
                                               @Nullable byte[] responseBody,
                                               @Nullable Charset responseCharset) {

    super(message, statusCode.value(), statusText, responseHeaders, responseBody, responseCharset);
    this.statusCode = statusCode;
  }

  private static String getMessage(HttpStatus statusCode, String statusText) {
    if (!StringUtils.hasLength(statusText)) {
      statusText = statusCode.getReasonPhrase();
    }
    return statusText;
  }

}
