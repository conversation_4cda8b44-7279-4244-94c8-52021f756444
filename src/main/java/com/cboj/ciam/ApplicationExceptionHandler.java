package com.cboj.ciam;

import com.cboj.ciam.api.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.client.HttpClientErrorException;

import javax.validation.ValidationException;
import java.util.List;

@Slf4j
@ControllerAdvice
public class ApplicationExceptionHandler {

  private final ObjectMapper mapper = new ObjectMapper();

  @SuppressWarnings("unchecked")
  @ExceptionHandler(OAuthException.class)
  protected ResponseEntity<Object> handleOAuthExceptions(OAuthException ex) {
    String message = ex.getMessage();
    OAuthErrorDto oAuthErrorDto;
    try {
      if (message != null && message.indexOf("[") > 0) {
        String messageData = message.substring(message.indexOf("["));
        if (messageData.contains("{") && messageData.contains("}")) {
          oAuthErrorDto = (OAuthErrorDto) mapper.readValue(messageData, List.class)
              .stream().map(error -> mapper.convertValue(error, OAuthErrorDto.class))
              .findFirst().orElse(OAuthErrorDto.defaultInstance);
        } else {
          oAuthErrorDto = new OAuthErrorDto("authorization exception", messageData);
        }
      } else if (message != null && message.contains("{") && message.contains("}")) {
        String nm = message.substring(message.indexOf("{"), message.indexOf("}") + 1);
        oAuthErrorDto = mapper.readValue(nm, OAuthErrorDto.class);
      } else {
        oAuthErrorDto = new OAuthErrorDto("authorization exception", message);
      }
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
      oAuthErrorDto = OAuthErrorDto.defaultInstance;
    }

    if (oAuthErrorDto.getStatus() == null) {
      oAuthErrorDto.setStatus(OAuthErrorDto.Status.getFromValue(oAuthErrorDto.getError()));
    }
    return ResponseEntity.status(ex.getStatusCode())
        .body(new OpenBankingErrorDto(oAuthErrorDto.getStatus().getHttpStatus(), oAuthErrorDto.getError_description()));
  }

  @ExceptionHandler(InternalApiException.class)
  protected ResponseEntity<Object> handleInternalApiExceptions(InternalApiException ex) {
    String message = ex.getCause() == null ? ex.getLocalizedMessage() : ex.getCause().getLocalizedMessage();
    InternalApiErrorDto response = new InternalApiErrorDto(ex.getStatusCode(), message);
    return ResponseEntity.status(ex.getStatusCode()).body(response);
  }

  @ExceptionHandler(OpenBankingException.class)
  protected ResponseEntity<Object> handleOpenBankingExceptions(OpenBankingException ex) {
    String message = ex.getCause() == null ? ex.getLocalizedMessage() : ex.getCause().getLocalizedMessage();
    OpenBankingErrorDto response = new OpenBankingErrorDto(ex.getStatusCode(), message);
    return ResponseEntity.status(ex.getStatusCode()).body(response);
  }

  @ExceptionHandler(HttpClientErrorException.class)
  public ResponseEntity<Object> handleClientExceptions(HttpClientErrorException ex) {
    String message = ex.getCause() == null ? ex.getLocalizedMessage() : ex.getCause().getLocalizedMessage();
    log.error(message, ex);
    OpenBankingErrorDto response = new OpenBankingErrorDto(HttpStatus.BAD_REQUEST, message);
    return ResponseEntity.status(ex.getStatusCode()).body(response);
  }

  /**
   * Handles method argument validation exceptions and returns a ResponseEntity containing a message.
   *
   * @param ex The MethodArgumentNotValidException to handle.
   * @return A ResponseEntity with HTTP status code of 400 (BAD_REQUEST) and a body containing the error message.
   */
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Object> handleBindExceptions(MethodArgumentNotValidException ex) {
    StringBuilder sb = new StringBuilder();
    ex.getBindingResult().getAllErrors().forEach((error) -> {
      String fieldName = ((FieldError) error).getField();
      String errorMessage = error.getDefaultMessage();

      if (sb.length() > 0) {
        sb.append("; ");
      }

      sb.append("(").append(fieldName)
          .append(") -> ")
          .append(errorMessage);
    });
    String message = sb.toString();
    log.error(message, ex);
    OpenBankingErrorDto response = new OpenBankingErrorDto(HttpStatus.BAD_REQUEST, message);
    return ResponseEntity.badRequest().body(response);
  }

  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(ValidationException.class)
  public ResponseEntity<Object> handleValidationExceptions(ValidationException ex) {
    String message = ex.getCause() == null ? ex.getLocalizedMessage() : ex.getCause().getLocalizedMessage();
    log.error(message, ex);
    OpenBankingErrorDto response = new OpenBankingErrorDto(HttpStatus.BAD_REQUEST, message);
    return ResponseEntity.badRequest().body(response);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<Object> handleGenericException(Exception ex) {
    String message = ex.getCause() == null ? ex.getLocalizedMessage() : ex.getCause().getLocalizedMessage();
    log.error(message, ex);
    OpenBankingErrorDto response = new OpenBankingErrorDto(HttpStatus.BAD_REQUEST, message);
    return ResponseEntity.badRequest().body(response);
  }
}
