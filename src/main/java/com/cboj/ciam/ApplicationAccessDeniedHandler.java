package com.cboj.ciam;

import com.cboj.ciam.consts.RestConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.WebContext;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Handle access denied exceptions.
 */
@RequiredArgsConstructor
@Component
public class ApplicationAccessDeniedHandler implements AccessDeniedHandler {

  private final SpringTemplateEngine templateEngine;

  @Override
  public void handle(HttpServletRequest request,
                     HttpServletResponse response,
                     AccessDeniedException accessDeniedException) throws IOException, ServletException {
    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
    WebContext context = new WebContext(request, response, request.getServletContext());

    String htmlContent = templateEngine.process(RestConstants.Templates.ERROR_PAGE, context);

    response.setContentType(MediaType.TEXT_HTML_VALUE);
    response.getWriter().write(htmlContent);
  }
}
