package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import org.springframework.http.HttpStatus;

/**
 * Represents an exception that is thrown when Open Banking API errors occur.
 */
public class OpenBankingException extends ApplicationHttpStatusCodeException {

  public OpenBankingException(HttpStatus statusCode, String statusText) {
    super(statusCode, statusText);
  }
}
