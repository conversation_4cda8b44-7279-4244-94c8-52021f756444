package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.Views;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is a representation of consent permission for mobile.
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Consent permission representation.")
public class AisPermissionWrapper {

  @JsonIgnore
  private final AisOutputConsentDto.AisPermissionOutputDto permission;

  public AisPermissionWrapper(AisOutputConsentDto.AisPermissionOutputDto permission) {
    this.permission = permission;
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Permission handle value", example = "ReadAccounts")
  public String getHandle() {
    return permission.getHandle();
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Permission description in Arabic", example = "أنت تصرح بالاطلاع على معلومات حساباتك لمزود الطرف الثالث (TPP).")
  public String getDescriptionAr() {
    return permission.getDescriptionAr();
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Permission description in English",
      example = "You are granting permission to the disclosure of your accounts information to a Third-Party Provider (TPP)")
  public String getDescriptionEn() {
    return permission.getDescriptionEn();
  }

  @JsonView(Views.Admin.class)
  @Schema(description = "Whether the permission is active or not")
  public Boolean getActive() {
    return permission.getActive();
  }
}
