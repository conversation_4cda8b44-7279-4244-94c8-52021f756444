package com.cboj.ciam.api.ais;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * Data transfer object that represents only input attributes for an AIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "AIS consent input data representation.")
public class AisMobileInputConsentDto {

  @NotNull(message = "Status is empty")
  @Schema(description = "Target consent status (Active or Rejected)", requiredMode = Schema.RequiredMode.REQUIRED, example = "Active")
  private String status;

  @Schema(description = "Approved user account references (not numbers!) as part of the consent", example = "[ \"4867184\", \"5316173\"]")
  private Set<String> accountRefs;
}
