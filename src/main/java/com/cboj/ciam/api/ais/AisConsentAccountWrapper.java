package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.Views;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is a representation of consent account for mobile.
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account information related to the consent.")
public class AisConsentAccountWrapper {

  @JsonIgnore
  private final AisOutputConsentDto.ConsentAccountOutputDto account;

  public AisConsentAccountWrapper(AisOutputConsentDto.ConsentAccountOutputDto account) {
    this.account = account;
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Account number", example = "*****************")
  public String getAccountNumber() {
    return account.getIbanNumber();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Account external identifier", example = "4867184")
  public String getAccountRef() {
    return account.getAccountRef();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Account currency", example = "JOD")
  public String getCurrency() {
    return account.getCurrency();
  }

}
