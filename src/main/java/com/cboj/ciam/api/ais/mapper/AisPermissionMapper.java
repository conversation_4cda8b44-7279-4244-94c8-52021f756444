package com.cboj.ciam.api.ais.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.ais.AisPermissionDto;
import com.cboj.ciam.jpa.ais.AisPermission;
import org.springframework.stereotype.Component;

/**
 * Function that converts an AisPermissionDto object to an AisPermission object.
 */
@Component
public class AisPermissionMapper implements Mapper<AisPermissionDto, AisPermission> {

  @Override public AisPermission apply(AisPermissionDto dto) {
    AisPermission entity = new AisPermission();
    entity.setId(dto.getId());
    entity.setActive(dto.getActive());
    entity.setDescriptionAr(dto.getDescriptionAr());
    entity.setDescriptionEn(dto.getDescriptionEn());
    entity.setHandle(dto.getHandle());

    return entity;
  }
}
