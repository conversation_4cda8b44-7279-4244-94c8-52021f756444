package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * This class is a representation of consent for API responses.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account Information Services consent representation.")
public class AisConsentWrapper {

  @JsonIgnore
  protected final AisOutputConsentDto consent;

  public AisConsentWrapper(AisOutputConsentDto consent) {
    this.consent = consent;
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "AIS consent external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getConsentRef() {
    return consent.getConsentRef();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "AIS consent sso external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getSsoConsentRef() {
    return consent.getSsoCustomerRef();
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  public String getStatus() {
    return consent.getStatus();
  }

  @JsonView({ Views.Mule.class, Views.Admin.class })
  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  public String getCustomerRef() {
    return consent.getCustomerRef();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application ID that created the consent",
      example = "c3bdde79e4ea4b63933885dbbe450539")
  public String getClientAppId() {
    return consent.getClientAppId();
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application name that created the consent",
      example = "Fintech Company")
  public String getClientAppName() {
    return consent.getClientAppName();
  }

  @JsonView({ Views.Admin.class, Views.Public.class })
  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime getCreateDateTime() {
    return consent.getCreateDate();
  }

  @JsonView({ Views.Admin.class, Views.Public.class })
  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime getUpdateDateTime() {
    return consent.getUpdateDate();
  }

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs",
      example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> getLoginUrls() {
    return consent.getLoginUrls();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class, Views.Public.class })
  @Schema(description = "Expiration date of the consent")
  public LocalDateTime getExpirationDateTime() {
    return consent.getExpirationDateTime();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class, Views.Public.class })
  @Schema(description = "Transaction start date for account information")
  public LocalDateTime getTransactionFromDateTime() {
    return consent.getTransactionFromDate();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class, Views.Public.class })
  @Schema(description = "Transaction start date for account information")
  public LocalDateTime getTransactionToDateTime() {
    return consent.getTransactionToDate();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Customer accounts associated with the consent")
  public Set<AisConsentAccountWrapper> getAccounts() {
    return consent.getAccounts().stream()
        .map(AisConsentAccountWrapper::new)
        .collect(Collectors.toSet());
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class, Views.Public.class, Views.Mule.class })
  @Schema(name = "permissions", description = "Account permissions associated with the consent")
  @JsonSerialize(using = PermissionSerializer.class)
  public Object getPermissions() {
    return consent.getPermissions();
  }

}
