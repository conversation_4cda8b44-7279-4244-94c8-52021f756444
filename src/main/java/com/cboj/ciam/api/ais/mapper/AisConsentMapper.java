package com.cboj.ciam.api.ais.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.jpa.ais.AisConsent;
import org.springframework.stereotype.Component;


/**
 * Function that converts an AisConsentDto object to an AisConsent object.
 */
@Component
public class AisConsentMapper implements Mapper<AisConsentDto, AisConsent> {

  @Override public AisConsent apply(AisConsentDto dto) {
    AisConsent entity = new AisConsent();
    entity.setId(dto.getId());
    entity.setClientAppId(dto.getClientAppId());
    entity.setClientAppName(dto.getClientAppName());
    entity.setCustomerRef(dto.getCustomerRef());
    entity.setRedirectUrl(dto.getRedirectUrl());
    entity.setAuthCode(dto.getAuthCode());
    entity.setRefreshToken(dto.getRefreshToken());
    entity.setTransactionFromDate(dto.getTransactionFromDate());
    entity.setTransactionToDate(dto.getTransactionToDate());

    return entity;
  }
}
