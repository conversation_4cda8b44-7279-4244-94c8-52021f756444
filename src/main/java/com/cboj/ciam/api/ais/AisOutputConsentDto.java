package com.cboj.ciam.api.ais;

import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "AIS internal consent input data representation.")
public class AisOutputConsentDto {

  @Schema(description = "TPP client application ID that created the consent", example = "c3bdde79e4ea4b63933885dbbe450539")
  public String clientAppId;

  @Schema(description = "TPP client application name that created the consent", example = "Fintech Company")
  public String clientAppName;

  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime createDate;

  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime updateDate;

  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  private String customerRef;

  @Schema(description = "Client consent external identifier", example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  private String consentRef;

  @Schema(description = "Customer sso external identifier to fetch additional information", example = "2410116")
  private String ssoCustomerRef;

  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  private String status;

  @Schema(description = "Expiration date of the consent")
  private LocalDateTime expirationDateTime;

  @Schema(description = "A list of possible login page URLs", example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> loginUrls;

  @Schema(description = "Account transactions start date with time")
  private LocalDateTime transactionFromDate;

  @Schema(description = "Account transactions end date with time")
  private LocalDateTime transactionToDate;

  @Schema(description = "List of account permissions granted by the consent", example = "[\"ReadAccounts\", \"ReadBalances\"]")
  private Set<AisPermissionOutputDto> permissions;

  @Schema(description = "User accounts identifiers")
  private Set<ConsentAccountOutputDto> accounts;

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class AisPermissionOutputDto {

    @Schema(description = "Handle")
    private String handle;

    @Schema(description = "Description on english")
    private String descriptionEn;

    @Schema(description = "Description on original")
    private String descriptionAr;

    @Schema(description = "Activity sign")
    private Boolean active;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class ConsentAccountOutputDto {

    @Schema(description = "Account identifier")
    private String accountRef;

    @Schema(description = "IBAN number")
    private String ibanNumber;

    @Schema(description = "Account title on english")
    private String titleEn;

    @Schema(description = "Account title on arabic")
    private String titleAr;

    @Schema(description = "Account currency")
    private String currency;

    @Schema(description = "Account view in list in english")
    private String listViewEn;

    @Schema(description = "Account view in list in arabic")
    private String listViewAr;

  }
}
