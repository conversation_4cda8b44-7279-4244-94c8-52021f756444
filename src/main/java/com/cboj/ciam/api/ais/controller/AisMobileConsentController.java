package com.cboj.ciam.api.ais.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.*;
import com.cboj.ciam.api.ais.*;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.service.auth.ais.AisAuthorizationService;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * AIS-specific consent controller.
 */
@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.MOBILE_AIS_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "Mobile Backend API")
@SecurityRequirement(name = SecurityConstants.MOBILE_API_BEARER_TOKEN)
public class AisMobileConsentController {

  private final AisAuthorizationService aisAuthorizationService;
  private final AisConsentDataService aisConsentService;
  private final Mapper<AisConsent, AisOutputConsentDto> outputMapper;

  /**
   * Retrieves AIS consent data by its extended identifier.
   *
   * @param consentRef The consent extended identifier.
   * @return ResponseEntity containing a UserConsentDto.
   * @throws NotFoundException if the AisConsent is not found for the specified consentRef.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetAisConsent",
      summary = "Get accounts consent by extended identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mobile.class)
  public CompletableFuture<ResponseEntity<AisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef) {
    return aisConsentService.findByAttr(AisConsent_.CONSENT_REF, consentRef)
        .thenApply(aisConsent -> {
          if (aisConsent == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          AisOutputConsentDto output = outputMapper.apply(aisConsent);

          return ResponseEntity.ok(new AisConsentWrapper(output));
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Processes the consent for a given account.
   *
   * @param consentRef The consent extended identifier.
   * @param dto        The data model for giving consent {@link AisConsentDto}.
   * @return ResponseEntity containing the response to the consent request.
   */
  @PutMapping(path = "/{consentRef}",
      consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "UpdateAisConsent",
      summary = "Update AIS consent.",
      description = "Update the AIS consent based on the customer's decision.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful completion of operation")
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mobile.class)
  public CompletableFuture<ResponseEntity<Void>> processConsent(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef,
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Data model for giving consent")
      @RequestBody @Valid @NotNull AisMobileInputConsentDto dto) {

    CompletableFuture<ResponseEntity<Void>> result;

    if (dto.getStatus().equals(ConsentStatus.REJECTED.getValue())) {
      result = aisAuthorizationService.declineConsents(consentRef)
          .thenApply(unused -> ResponseEntity.status(HttpStatus.NO_CONTENT).build());
    } else {
      result = aisAuthorizationService.approveConsents(consentRef, dto.getAccountRefs())
          .thenApply(unused -> ResponseEntity.status(HttpStatus.NO_CONTENT).build());
    }

    return result.exceptionally(throwable -> {
      throw processException(throwable);
    });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
