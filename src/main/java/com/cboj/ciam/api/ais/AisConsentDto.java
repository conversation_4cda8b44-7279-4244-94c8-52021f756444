package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.BaseDto;
import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Data transfer object that represents a full set of attributes for an AIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account consent")
public class AisConsentDto extends BaseDto {

  @Schema(description = "Client consent external identifier", requiredMode = Schema.RequiredMode.REQUIRED, example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  private String consentRef;

  @Schema(description = "Consent status", requiredMode = Schema.RequiredMode.REQUIRED, implementation = ConsentStatus.class)
  private String status;

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> loginUrls;

  @Schema(description = "Account transactions start date with time", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDateTime transactionFromDate;

  @Schema(description = "Account transactions end date with time", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDateTime transactionToDate;

  @Schema(description = "List of account permissions granted by the consent", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"ReadAccounts\", \"ReadBalances\"]")
  private Set<String> permissions;

  @JsonIgnore
  @Schema(description = "User accounts identifiers")
  private Set<String> accounts;

  @JsonIgnore
  @Schema(description = "Customer external identifier")
  private String customerRef;

  @JsonIgnore
  @Schema(description = "TPP client application identifier")
  private String clientAppId;

  @JsonIgnore
  @Schema(description = "TPP client application name")
  private String clientAppName;

  @JsonIgnore
  @Schema(description = "TPP client application redirect URL")
  private String redirectUrl;

  @JsonIgnore
  @Schema(description = "OAuth2 authorization code")
  private String authCode;

  @JsonIgnore
  @Schema(description = "Refresh token related to the consent")
  private String refreshToken;

}
