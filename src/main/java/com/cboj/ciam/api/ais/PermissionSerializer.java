package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.Views;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * This class is responsible for serializing a set of AisOutputConsentDto.AisPermissionOutputDto objects to JSON.
 */
public class PermissionSerializer extends JsonSerializer<Set<AisOutputConsentDto.AisPermissionOutputDto>> {

  @Override
  public void serialize(Set<AisOutputConsentDto.AisPermissionOutputDto> permissions,
                        JsonGenerator jsonGenerator,
                        SerializerProvider serializerProvider) throws IOException {
    Class<?> activeView = serializerProvider.getActiveView();

    Object result;
    if (activeView.equals(Views.Public.class) || activeView.equals(Views.Mule.class)) {
      result = permissions.stream()
          .filter(AisOutputConsentDto.AisPermissionOutputDto::getActive)
          .map(AisOutputConsentDto.AisPermissionOutputDto::getHandle)
          .collect(Collectors.toSet());
    } else {
      result = permissions.stream()
          .filter(AisOutputConsentDto.AisPermissionOutputDto::getActive)
          .map(AisPermissionWrapper::new)
          .collect(Collectors.toSet());
    }

    jsonGenerator.writeObject(result);
  }
}
