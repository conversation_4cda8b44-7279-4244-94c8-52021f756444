package com.cboj.ciam.api.ais.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsentAccount;
import com.cboj.ciam.jpa.ais.AisPermission;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Mapper class to convert AisConsent object to an AisOutputConsentDto object.
 */
@Component
public class AisOutputConsentMapper implements Mapper<AisConsent, AisOutputConsentDto> {

  @Override
  public AisOutputConsentDto apply(AisConsent entity) {
    AisOutputConsentDto result = new AisOutputConsentDto();

    result.setStatus(entity.getStatus().getValue());
    result.setCustomerRef(entity.getCustomerRef());
    result.setConsentRef(entity.getConsentRef().toString());
    result.setSsoCustomerRef(entity.getSsoCustomerRef());
    result.setClientAppId(entity.getClientAppId());
    result.setClientAppName(entity.getClientAppName());
    result.setCreateDate(entity.getCreateDate());
    result.setUpdateDate(entity.getUpdateDate());
    result.setExpirationDateTime(entity.getExpiryDate());
    result.setTransactionToDate(entity.getTransactionToDate());
    result.setTransactionFromDate(entity.getTransactionFromDate());

    if (entity.getAccounts() != null) {
      Set<AisOutputConsentDto.ConsentAccountOutputDto> accounts = entity.getAccounts().stream()
          .map(this::prepareAccount).collect(Collectors.toSet());

      result.setAccounts(accounts);
    }

    Set<AisOutputConsentDto.AisPermissionOutputDto> permissions = entity.getPermissions().stream()
        .map(this::preparePermission).collect(Collectors.toSet());


    result.setPermissions(permissions);

    return result;
  }

  /**
   * Prepares a ConsentAccountOutputDto object based on the provided AisConsentAccount object.
   *
   * @param account the AisConsentAccount object to be processed
   * @return a ConsentAccountOutputDto object populated with account information
   */
  private AisOutputConsentDto.ConsentAccountOutputDto prepareAccount(AisConsentAccount account) {
    AisOutputConsentDto.ConsentAccountOutputDto result = new AisOutputConsentDto.ConsentAccountOutputDto();
    result.setAccountRef(account.getAccountRef());
    result.setCurrency(account.getCurrency());
    result.setIbanNumber(account.getAccountNumber());
    return result;
  }

  /**
   * Prepares an AisPermissionOutputDto object based on the provided AisPermission object.
   *
   * @param permission the AisPermission object to be processed
   * @return an AisPermissionOutputDto object populated with permission information
   */
  private AisOutputConsentDto.AisPermissionOutputDto preparePermission(AisPermission permission) {
    AisOutputConsentDto.AisPermissionOutputDto result = new AisOutputConsentDto.AisPermissionOutputDto();
    result.setActive(permission.getActive());
    result.setDescriptionAr(permission.getDescriptionAr());
    result.setDescriptionEn(permission.getDescriptionEn());
    result.setHandle(permission.getHandle());
    return result;
  }
}
