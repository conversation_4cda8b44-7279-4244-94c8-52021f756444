package com.cboj.ciam.api.ais.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.*;
import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.api.ais.AisConsentWrapper;
import com.cboj.ciam.api.ais.AisInputConsentDto;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.service.TokenClaimsUtils;
import com.cboj.ciam.service.auth.ais.AisAuthorizationService;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.KeycloakRequest;
import com.fasterxml.jackson.annotation.JsonView;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@RestController
@RequestMapping(RestConstants.Paths.API_AIS_ACCOUNT_ACCESS_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "Open Banking API")
@SecurityRequirement(name = SecurityConstants.OAUTH_CLIENT_CRED_SCHEME)
public class AisConsentController {

  private final AisAuthorizationService aisAuthorizationService;
  private final AisConsentDataService service;
  private final KeycloakAdminService keycloakAdminService;
  private final Mapper<AisConsent, AisOutputConsentDto> outputMapper;

  /**
   * Create client consent.
   *
   * @param aisInputConsentDto The consent information.
   * @param request            The HTTP servlet request.
   * @return ResponseEntity with CreateConsentResponse
   * @throws org.springframework.web.server.ResponseStatusException if there is an error during the operation.
   */
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize("hasAuthority(T(com.cboj.ciam.consts.SsoConstants.Scope).ACCOUNTS)")
  @Operation(operationId = "CreateAisConsent",
      summary = "Creates a new AIS consent based on the input transaction date range and permissions.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "403", description = "Authorization error",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = OpenBankingErrorDto.class)))
      })
  @DefaultPublicErrorResponse
  @JsonView(Views.Public.class)
  public CompletableFuture<ResponseEntity<AisConsentWrapper>> createConsent(
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Consent input data", required = true)
      @RequestBody @Valid @NotNull AisInputConsentDto aisInputConsentDto,
      HttpServletRequest request) {
    AisConsentDto aisConsentDto = new AisConsentDto();
    aisConsentDto.setTransactionFromDate(aisInputConsentDto.getTransactionFromDate());
    aisConsentDto.setTransactionToDate(aisInputConsentDto.getTransactionToDate());
    aisConsentDto.setPermissions(aisInputConsentDto.getPermissions());
    return aisAuthorizationService.createConsent(aisConsentDto, request)
        .thenApply(consentResponse -> ResponseEntity.status(HttpStatus.CREATED).body(new AisConsentWrapper(consentResponse)))
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Get accounts consent by extended identifier.
   *
   * @param consentRef The consent extended identifier. (path parameter)
   * @return Accounts consent object.
   * @throws NotFoundException        If AisConsent is not found for the consentRef.
   * @throws UnauthorizedException    If the authorization header is missing or not using the basic authentication type.
   * @throws IllegalArgumentException If consentRef is blank.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetAisConsent",
      summary = "Returns information about the AIS consent using an external consent identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = OpenBankingErrorDto.class)))
      })
  @DefaultPublicErrorResponse
  @JsonView(Views.Public.class)
  public CompletableFuture<ResponseEntity<AisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class, example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7"))
      @NonNull @PathVariable("consentRef") UUID consentRef, HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    Map<String, Object> attrsData = new HashMap<>();
    attrsData.put(AisConsent_.CONSENT_REF, consentRef);
    attrsData.put(AisConsent_.CLIENT_APP_ID, clientId);

    return service.findByAttrs(attrsData)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          AisOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new AisConsentWrapper(output));
        })
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Deletes a consent by extended identifier.
   *
   * @param consentRef The consent extended identifier.
   * @param request    The HttpServletRequest object.
   * @return Response indicating the result of the delete operation.
   * @throws NotFoundException If the AisConsent is not found for the consentRef.
   * @throws RuntimeException  If the revoke user token request fails.
   */
  @DeleteMapping(value = "/{consentRef}")
  @Operation(operationId = "DeleteAisConsent",
      summary = "Delete consent by external identifier.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful completion of operation")
      })
  @DefaultPublicErrorResponse
  public CompletableFuture<ResponseEntity<Void>> delete(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier") @PathVariable("consentRef") @NonNull UUID consentRef,
      HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    //Checking
    Map<String, Object> attrsData = new HashMap<>();
    attrsData.put(AisConsent_.CONSENT_REF, consentRef);
    attrsData.put(AisConsent_.CLIENT_APP_ID, clientId);

    return service.findByAttrs(attrsData)
        .thenCompose(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          if (!consent.getStatus().equals(ConsentStatus.ACTIVE)) {
            throw new NotFoundException(
                String.format("Error due deleting consent %s. Non active AisConsent cannot be deleted ", consentRef));
          }

          String sessionState;
          String userId;

          if (consent.getRefreshToken() != null) {
            try {
              JWTClaimsSet claims = JWTParser.parse(consent.getRefreshToken()).getJWTClaimsSet();
              sessionState = claims.getStringClaim(SsoConstants.User.SID_CLAIM_NAME);
              userId = claims.getStringClaim(SsoConstants.User.SUB_CLAIM_NAME);
            } catch (ParseException ex) {
              throw new UnauthorizedException(ex.getLocalizedMessage());
            }

            keycloakAdminService.processRevokeUserToken(KeycloakRequest.builder()
                .clientId(clientId)
                .userId(userId)
                .state(sessionState)
                .refreshToken(consent.getRefreshToken())
                .build());
          }

          consent.setStatus(ConsentStatus.REVOKED);

          return service.update(consent);
        })
        .thenApply(updated -> ResponseEntity.status(HttpStatus.NO_CONTENT).<Void>build())
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private OpenBankingException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new OpenBankingException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new OpenBankingException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
