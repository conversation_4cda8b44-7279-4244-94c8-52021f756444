package com.cboj.ciam.api.ais;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Data transfer object that represents only input attributes for an AIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "AIS consent input data representation.")
public class AisInputConsentDto {

  @NotNull(message = "Requested consent transaction start date is empty")
  @Schema(description = "Requested consent transaction start date", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDateTime transactionFromDate;

  @NotNull(message = "Requested consent transaction end date is empty")
  @Schema(description = "Requested consent transaction end date", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDateTime transactionToDate;

  @NotEmpty(message = "Consent permissions list is empty")
  @Schema(description = "Requested consent permission handles", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"ReadAccounts\", \"ReadBalances\"]")
  private Set<String> permissions;

}
