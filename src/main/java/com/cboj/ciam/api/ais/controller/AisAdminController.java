package com.cboj.ciam.api.ais.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.*;
import com.cboj.ciam.api.ais.AisConsentWrapper;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.jpa.caf.CafConsent_;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.KeycloakRequest;
import com.fasterxml.jackson.annotation.JsonView;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * AIS-specific administration controller.
 */
@Validated
@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.ADMIN_AIS_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "Administration API")
@SecurityRequirement(name = SecurityConstants.ADMIN_API_BEARER_TOKEN)
public class AisAdminController {

  private final Mapper<AisConsent, AisOutputConsentDto> outputMapper;
  private final KeycloakAdminService keycloakAdminService;
  private final AisConsentDataService service;

  /**
   * Search list of AIS consents by Customer identifier or Client ID.
   *
   * @param customerRef The customer identifier (optional)
   * @param clientId   The client ID (optional)
   * @param page       The page number (optional)
   * @param perPage    The number of elements per page (optional)
   * @return The Page contains the list of AIS consents.
   * @throws NullPointerException if any of the parameters is null.
   */
  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "SearchAisConsent",
      summary = "Returns information about an AIS consent based on its reference which is sent as an URL parameter.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation")
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Admin.class)
  public CompletableFuture<Page<AisConsentWrapper>> search(
      @Parameter(name = "customerRef", in = ParameterIn.QUERY,
          description = "Customer external identifier") @Nullable String customerRef,
      @Parameter(name = "clientId", in = ParameterIn.QUERY,
          description = "TPP client application identifier") @Nullable String clientId,
      @Parameter(name = "page", in = ParameterIn.QUERY,
          description = "Page number") @Nullable Integer page,
      @Parameter(name = "perPage", in = ParameterIn.QUERY,
          description = "Number of items per page") @Nullable Integer perPage) {
    Map<String, Object> data = new HashMap<>(2);
    data.put(AisConsent_.CUSTOMER_REF, StringUtils.isEmpty(customerRef) ? null : customerRef);
    data.put(AisConsent_.CLIENT_APP_ID, StringUtils.isEmpty(clientId) ? null : clientId);
    return service.searchByAttrs(data, page, perPage)
        .thenApply(pageResult -> Page.<AisConsentWrapper>builder()
            .content(pageResult.getContent().stream().map(consent -> {
              AisOutputConsentDto output = outputMapper.apply(consent);
              return new AisConsentWrapper(output);
            }).collect(Collectors.toList()))
            .page(page)
            .itemsPerPage(perPage)
            .totalItems((int) pageResult.getTotalElements())
            .build())
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Get accounts consent by extended identifier.
   *
   * @param consentRef The consent extended identifier. (path parameter)
   * @return Accounts consent object.
   * @throws NotFoundException        If AisConsent is not found for the consentRef.
   * @throws UnauthorizedException    If the authorization header is missing or not using the basic authentication type.
   * @throws IllegalArgumentException If consentRef is blank.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetAisConsent",
      summary = "Get accounts consent by its external identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @JsonView(Views.Admin.class)
  @DefaultInternalErrorResponse
  public CompletableFuture<ResponseEntity<AisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier") @NonNull @PathVariable("consentRef") UUID consentRef) {
    return service.findByAttr(AisConsent_.CONSENT_REF, consentRef)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          AisOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new AisConsentWrapper(output));
        })
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Deletes a consent by extended identifier.
   *
   * @param consentRef The consent extended identifier.
   * @return Response indicating the result of the delete operation.
   * @throws NotFoundException If the AisConsent is not found for the consentRef.
   * @throws RuntimeException  If the revoke user token request fails.
   */
  @DeleteMapping(path = "/{consentRef}")
  @Operation(operationId = "RevokeAisConsent",
      summary = "Revokes a certain consent based on the passed consent ID.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful consent revocation")
      })
  @DefaultInternalErrorResponse
  public CompletableFuture<ResponseEntity<Void>> delete(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier",
          schema = @Schema(implementation = UUID.class)) @PathVariable("consentRef") @NonNull UUID consentRef,

      @Parameter(name = "customerRef", in = ParameterIn.HEADER, required = true,
              description = "Customer identifier",
              schema = @Schema(implementation = Long.class)) @RequestHeader("customerRef") @NonNull Long customerRef) {
    //Checking
    Map<String, Object> attrsData = new HashMap<>();
    attrsData.put(CafConsent_.CONSENT_REF, consentRef);
    attrsData.put(CafConsent_.STATUS, ConsentStatus.ACTIVE);
    attrsData.put(AisConsent_.CUSTOMER_REF, customerRef);

    return service.findByAttrs(attrsData)
        .thenCompose(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s and customerRef %s", consentRef, customerRef));
          }

          String userId;
          String sessionState;
          String clientId;

          if (consent.getRefreshToken() != null) {
            try {
              JWTClaimsSet claims = JWTParser.parse(consent.getRefreshToken()).getJWTClaimsSet();
              userId = claims.getStringClaim(SsoConstants.User.SUB_CLAIM_NAME);
              sessionState = claims.getStringClaim(SsoConstants.User.SID_CLAIM_NAME);
              clientId = claims.getStringClaim(SsoConstants.User.AZP_CLAIM_NAME);
            } catch (Exception ex) {
              throw new UnauthorizedException(ex.getLocalizedMessage());
            }

            keycloakAdminService.processRevokeUserToken(KeycloakRequest.builder()
                .clientId(clientId)
                .userId(userId)
                .state(sessionState)
                .refreshToken(consent.getRefreshToken())
                .build());
          }

          consent.setStatus(ConsentStatus.REVOKED);

          return service.update(consent);
        })
        .thenApply(updated -> ResponseEntity.status(HttpStatus.NO_CONTENT).<Void>build())
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }

}
