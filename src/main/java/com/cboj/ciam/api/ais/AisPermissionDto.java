package com.cboj.ciam.api.ais;

import com.cboj.ciam.api.BaseDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * Data transfer object that represents the account permission.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account permission")
public class AisPermissionDto extends BaseDto {

  @NotNull(message = "Handle not specified")
  @Schema(description = "Handle")
  private String handle;

  @Schema(description = "Description on english")
  private String descriptionEn;

  @Schema(description = "Description on original")
  private String descriptionAr;

  @Schema(description = "Activity sign")
  private Boolean active;

}
