package com.cboj.ciam.api.ais.mapper;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.jpa.ais.AisConsentAccount;
import org.springframework.stereotype.Component;

/**
 * Function that converts an AisConsentAccountDto object to an AisConsentAccount object.
 */
@Component
public class AisConsentAccountMapper implements Mapper<ConsentAccountDto, AisConsentAccount> {

  @Override public AisConsentAccount apply(ConsentAccountDto dto) {
    AisConsentAccount account = new AisConsentAccount();
    account.setId(dto.getId());
    account.setAccountRef(dto.getAccountRef());
    account.setAccountNumber(dto.getIbanNumber());
    account.setCurrency(dto.getCurrency());
    account.setConsentId(dto.getConsentId());
    return account;
  }
}
