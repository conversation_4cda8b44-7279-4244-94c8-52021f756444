package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import org.springframework.http.HttpStatus;

/**
 * Represents an exception that is thrown when errors when OAuth authentication and authorization errors occur.
 */
public class OAuthException extends ApplicationHttpStatusCodeException {

  public OAuthException(HttpStatus statusCode, String statusText) {
    super(statusCode, statusText);
  }
}
