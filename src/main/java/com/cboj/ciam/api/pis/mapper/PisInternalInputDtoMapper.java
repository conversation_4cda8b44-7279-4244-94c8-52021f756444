package com.cboj.ciam.api.pis.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisInternalInputConsentDto;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;

@Component
public class PisInternalInputDtoMapper implements Mapper<PisInternalInputConsentDto, PisConsentDto> {

  @Override
  public PisConsentDto apply(PisInternalInputConsentDto dto) {
    PisConsentDto result = new PisConsentDto();
    PisInternalInputConsentDto.TransactionInformation transactionInformation = dto.getTransactionInformation();
    if (transactionInformation != null) {
      result.setInstructionIdentification(transactionInformation.getInstructionIdentification());
      result.setEndToEndIdentification(transactionInformation.getEndToEndIdentification());
    }

    PisInternalInputConsentDto.Initiation initiation = dto.getInitiation();
    if (initiation != null) {
      if (initiation.getPaymentType() != null) {
        result.setPaymentType(initiation.getPaymentType());
      }

      if (initiation.getPaymentPurposeCode() != null) {
        result.setPaymentPurposeCode(initiation.getPaymentPurposeCode());
      } else {
        throw new ValidationException("paymentPurposeCode is empty");
      }

      if (initiation.getCategoryPurposeCode() != null) {
        result.setCategoryPurposeCode(initiation.getCategoryPurposeCode());
      } else {
        throw new ValidationException("categoryPurposeCode is empty");
      }

      result.setPaymentReason(initiation.getPaymentReason());
      result.setChargeType(initiation.getChargeType());

      // Setting local instrument
      result.setLocalInstrument(LocalInstrument.INTERNAL.getValue());

      PisInternalInputConsentDto.InstructedAmount instructedAmount = initiation.getInstructedAmount();
      if (instructedAmount != null) {
        result.setAmount(instructedAmount.getAmount());
        result.setCurrency(instructedAmount.getCurrency());
      }

      PisInternalInputConsentDto.Account crerditorAccount = initiation.getCreditorAccount();
      if (crerditorAccount != null) {
        result.setCreditorAccRef(crerditorAccount.getIdentification());
        if (StringUtils.isEmpty(crerditorAccount.getIdentification())) {
          throw new ValidationException("Creditor account Identification is empty");
        }
        if (StringUtils.isEmpty(crerditorAccount.getSchemeName())) {
          throw new ValidationException("Creditor account scheme name is empty");
        }

        result.setCreditorAccScheme(crerditorAccount.getSchemeName());
      } else {
        throw new ValidationException("creditorAccount is empty");
      }

      PisInternalInputConsentDto.Account debtorAccount = initiation.getDebtorAccount();
      if (debtorAccount != null) {
        if (StringUtils.isEmpty(debtorAccount.getIdentification())) {
          throw new ValidationException("Debtor account Identification is empty");
        }

        result.setDebtorAccRef(debtorAccount.getIdentification());

        if (StringUtils.isEmpty(debtorAccount.getSchemeName())) {
          throw new ValidationException("Debtor account scheme name is empty");
        }

        result.setDebtorAccScheme(debtorAccount.getSchemeName());
      } else {
        throw new ValidationException("debtorAccount is empty");
      }
    }
    return result;
  }
}
