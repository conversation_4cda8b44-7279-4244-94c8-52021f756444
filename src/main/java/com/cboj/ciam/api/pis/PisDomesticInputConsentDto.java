package com.cboj.ciam.api.pis;

import com.cboj.ciam.api.Views;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * Data transfer object that represents only input attributes for the domestic PIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "PIS domestic consent input data representation.")
public class PisDomesticInputConsentDto extends PisBaseConsentDto {

  @Schema(description = "Initiation information", requiredMode = Schema.RequiredMode.REQUIRED)
  protected Initiation initiation;

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode(callSuper = true)
  public static class Initiation extends PisBaseConsentDto.Initiation {

    @JsonView({Views.Mule.class, Views.Mobile.class})
    @Schema(description = "The domestic payment method", requiredMode = Schema.RequiredMode.REQUIRED, example = "DomesticRtgs")
    private String localInstrument;

    @JsonView({Views.Mule.class})
    @Schema(description = "Beneficiary information")
    private BeneficiaryData beneficiaryData;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class BeneficiaryData {

    @JsonView({Views.Mule.class})
    @Schema(description = "Beneficiary names")
    private Name beneficiaryName;

    @JsonView({Views.Mule.class})
    @Schema(description = "Beneficiary address")
    private Address beneficiaryAddress;

    @JsonView({Views.Mule.class})
    @Schema(description = "Beneficiary Agent")
    private BeneficiaryAgent beneficiaryAgent;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class BeneficiaryAgent {

    @JsonView({Views.Mule.class})
    @Schema(description = "Beneficiary Agent Identification")
    private Agent agentIdentification;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class Agent {

    @JsonView({Views.Mule.class})
    @Schema(description = "Agent Identification")
    private String identification;

    @JsonView({Views.Mule.class})
    @Schema(description = "Schema of Agent")
    private String schema;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class Name {

    @JsonView({Views.Mule.class})
    @Schema(description = "First Name")
    private String firstName;

    @JsonView({Views.Mule.class})
    @Schema(description = "Middle name")
    private String middleName;

    @JsonView({Views.Mule.class})
    @Schema(description = "Last Name")
    private String lastName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class Address {

    @JsonView({Views.Mule.class})
    @Schema(description = "Address line")
    private String addressLine;

    @JsonView({Views.Mule.class})
    @Schema(description = "City")
    private String city;

    @JsonView({Views.Mule.class})
    @Schema(description = "State")
    private String state;

    @JsonView({Views.Mule.class})
    @Schema(description = "Post code")
    private String postcode;

    @JsonView({Views.Mule.class})
    @Schema(description = "Street Name")
    private String streetName;

    @JsonView({Views.Mule.class})
    @Schema(description = "Town Name")
    private String townName;

    @JsonView({Views.Mule.class})
    @Schema(description = "Country information")
    private CountryInfo countryInfo;

    @JsonView({Views.Mule.class})
    @Schema(description = "Building Number")
    private String buildingNumber;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class CountryInfo {

    @JsonView({Views.Mule.class})
    @Schema(description = "Country code")
    private String countryCode;

    @JsonView({Views.Mule.class})
    @Schema(description = "Country name")
    private String countryName;

  }
}
