package com.cboj.ciam.api.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Consent account input data representation for getting transaction information.")
public class PisMobileAccountDto {

  @NotNull
  @Schema(description = "Consent debtor account", requiredMode = Schema.RequiredMode.REQUIRED,
      implementation = PisBaseConsentDto.Account.class)
  private PisBaseConsentDto.Account debtorAccount;

}
