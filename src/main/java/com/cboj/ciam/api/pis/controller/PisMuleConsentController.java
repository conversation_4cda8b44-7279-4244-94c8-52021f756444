package com.cboj.ciam.api.pis.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.DefaultInternalErrorResponse;
import com.cboj.ciam.api.InternalApiErrorDto;
import com.cboj.ciam.api.InternalApiException;
import com.cboj.ciam.api.Views;
import com.cboj.ciam.api.pis.PisConsentWrapper;
import com.cboj.ciam.api.pis.PisMuleInputConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.ValidationException;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.MULE_PIS_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "MuleSoft Consent API")
@SecurityRequirement(name = SecurityConstants.MULE_API_BEARER_TOKEN)
public class PisMuleConsentController {

  private final Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper;
  private final PisConsentDataService dataService;

  /**
   * Get payments consent by extended identifier.
   *
   * @param consentRef The consent extended identifier. (path parameter)
   * @return PIS consent object.
   * @throws NotFoundException        If PisConsent is not found for the consentRef.
   * @throws UnauthorizedException    If the authorization header is missing or not using the basic authentication type.
   * @throws IllegalArgumentException If consentRef is blank.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetPisConsent",
      summary = "Get payments consent by extended identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mule.class)
  public CompletableFuture<ResponseEntity<PisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef) {
    return dataService.findByAttr(PisConsentEntity_.CONSENT_REF, consentRef)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
          }

          PisOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new PisConsentWrapper(output));
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Retrieves a payment consent by its payment reference.
   *
   * @param paymentRef The payment external identifier.
   * @return ResponseEntity containing the PisConsentWrapper.
   * @throws NotFoundException If the PisConsent is not found for the given paymentRef.
   */
  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetPisConsentByPaymentRef",
      summary = "Get payments consent by payment external identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mule.class)
  public CompletableFuture<ResponseEntity<PisConsentWrapper>> getByPaymentRef(
      @Parameter(name = "paymentRef", in = ParameterIn.QUERY, required = true,
          description = "Payment external identifier") @NotNull String paymentRef) {
    return dataService.findByAttr(PisConsentEntity_.PAYMENT_REF, paymentRef)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for paymentRef %s", paymentRef));
          }

          PisOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new PisConsentWrapper(output));
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  @PutMapping(path = "/{consentRef}",
      consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "UpdatePisConsent",
      summary = "Update PIS consent.",
      description = "Update the PIS consent status and payment reference.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful completion of operation")
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mule.class)
  public CompletableFuture<ResponseEntity<Void>> update(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef,
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Data model for giving consent")
      @RequestBody @Valid @NotNull PisMuleInputConsentDto dto) {

    if (!ConsentStatus.CONSUMED.getValue().equals(dto.getStatus())) {
      throw new ValidationException(String.format("Consent status %s not supported", dto.getStatus()));
    }

    return dataService.findByAttr(PisConsentEntity_.CONSENT_REF, consentRef)
        .thenCompose(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
          }

          consent.setStatus(ConsentStatus.getFromValue(dto.getStatus()));
          if (StringUtils.isNotEmpty(dto.getPaymentRef())) {
            consent.setPaymentRef(dto.getPaymentRef());
          }

          return dataService.update(consent)
              .thenApply(unused -> ResponseEntity.status(HttpStatus.NO_CONTENT).<Void>build());
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Processes an exception and returns an InternalApiException object.
   *
   * @param throwable the Throwable object representing the exception to be processed
   * @return an InternalApiException object
   */
  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
