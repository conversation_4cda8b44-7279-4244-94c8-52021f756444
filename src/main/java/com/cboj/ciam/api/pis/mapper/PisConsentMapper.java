package com.cboj.ciam.api.pis.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PaymentType;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


@Component
public class PisConsentMapper implements Mapper<PisConsentDto, PisConsentEntity> {

  @Override
  public PisConsentEntity apply(PisConsentDto pisConsentDto) {
    PisConsentEntity pisConsentEntity = new PisConsentEntity();
    pisConsentEntity.setId(pisConsentDto.getId());
    pisConsentEntity.setClientAppId(pisConsentDto.getClientAppId());
    pisConsentEntity.setClientAppName(pisConsentDto.getClientAppName());
    pisConsentEntity.setCustomerRef(pisConsentDto.getCustomerRef());
    pisConsentEntity.setRedirectUrl(pisConsentDto.getRedirectUrl());
    pisConsentEntity.setAuthCode(pisConsentDto.getAuthCode());
    pisConsentEntity.setRefreshToken(pisConsentDto.getRefreshToken());

    pisConsentEntity.setInstructionIdentification(pisConsentDto.getInstructionIdentification());
    pisConsentEntity.setEndToEndIdentification(pisConsentDto.getEndToEndIdentification());
    pisConsentEntity.setAmount(pisConsentDto.getAmount());
    pisConsentEntity.setCreditorAccRef(pisConsentDto.getCreditorAccRef());
    pisConsentEntity.setCreditorSecondaryIdentificationSchemeName(pisConsentDto.getSecondaryIdentificationSchemeName());
    pisConsentEntity.setCreditorSecondaryIdentification(pisConsentDto.getSecondaryIdentification());
    pisConsentEntity.setDebtorAccRef(pisConsentDto.getDebtorAccRef());
    pisConsentEntity.setPaymentPurposeCode(pisConsentDto.getPaymentPurposeCode());
    pisConsentEntity.setPaymentReason(pisConsentDto.getPaymentReason());
    pisConsentEntity.setChargeType(pisConsentDto.getChargeType());
    pisConsentEntity.setCreditorNameEn(pisConsentDto.getCreditorNameEn());
    pisConsentEntity.setCreditorNameAr(pisConsentDto.getCreditorNameAr());
    pisConsentEntity.setCreditorAddressLine1(pisConsentDto.getBeneficiaryAddressLine());
    pisConsentEntity.setCreditorCity(pisConsentDto.getCreditorCity());
    pisConsentEntity.setCreditorState(pisConsentDto.getCreditorState());
    pisConsentEntity.setCreditorPostcode(pisConsentDto.getCreditorPostcode());
    pisConsentEntity.setCreditorCountryCode(pisConsentDto.getCreditorCountryCode());
    pisConsentEntity.setCreditorCountryName(pisConsentDto.getCreditorCountryName());
    pisConsentEntity.setCreditorCurrency(pisConsentDto.getCreditorCurrency());

    //beneficiary mapping
    pisConsentEntity.setBeneficiaryFirstName(pisConsentDto.getBeneficiaryFirstName());
    pisConsentEntity.setBeneficiaryMiddleName(pisConsentDto.getBeneficiaryMiddleName());
    pisConsentEntity.setBeneficiaryLastName(pisConsentDto.getBeneficiaryLastName());

    if (StringUtils.isNotEmpty(pisConsentDto.getCurrency())) {
      pisConsentEntity.setCurrency(PaymentCurrency.valueOf(pisConsentDto.getCurrency()));
    }
    if (StringUtils.isNotEmpty(pisConsentDto.getPaymentType())) {
      pisConsentEntity.setPaymentType(PaymentType.getFromValue(pisConsentDto.getPaymentType()));
    }
    if (StringUtils.isNotEmpty(pisConsentDto.getLocalInstrument())) {
      pisConsentEntity.setLocalInstrument(LocalInstrument.getFromValue(pisConsentDto.getLocalInstrument()));
    }
    if (StringUtils.isNotEmpty(pisConsentDto.getCreditorAccScheme())) {
      pisConsentEntity.setCreditorAccScheme(AccountScheme.getFromValue(pisConsentDto.getCreditorAccScheme()));
    }
    if (StringUtils.isNotEmpty(pisConsentDto.getDebtorAccScheme())) {
      pisConsentEntity.setDebtorAccScheme(AccountScheme.getFromValue(pisConsentDto.getDebtorAccScheme()));
    }

    if (StringUtils.isNotEmpty(pisConsentDto.getAgentIdentification())) {
      pisConsentEntity.setAgentIdentification(pisConsentDto.getAgentIdentification());
    }

    if (StringUtils.isNotEmpty(pisConsentDto.getAgentSchema())) {
      pisConsentEntity.setAgentSchema(pisConsentDto.getAgentSchema());
    }
    pisConsentEntity.setPaymentPurposeDesc(pisConsentDto.getPaymentPurposeDesc());
    pisConsentEntity.setBeneficiaryBicCode(pisConsentDto.getBeneficiaryBicCode());
    return pisConsentEntity;
  }

}
