package com.cboj.ciam.api.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class PisMuleInputConsentDto {

  @NotNull
  @Schema(description = "Target consent status (Consumed)", requiredMode = Schema.RequiredMode.REQUIRED, example = "Consumed")
  private String status;

  @Schema(description = "Reference to the performed payment", example = "FT21039OTRT1HJPQ")
  private String paymentRef;
}
