package com.cboj.ciam.api.pis.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import org.springframework.stereotype.Component;

import static com.cboj.ciam.service.auth.pis.UtilValidation.isNullOrEmpty;

@Component
public class PisOutputConsentMapper implements Mapper<PisConsentEntity, PisOutputConsentDto> {

  @Override
  public PisOutputConsentDto apply(PisConsentEntity entity) {
    PisOutputConsentDto result = new PisOutputConsentDto();

    result.setStatus(entity.getStatus().getValue());
    result.setCustomerRef(entity.getCustomerRef());
    result.setSsoCustomerRef(entity.getSsoCustomerRef());
    result.setConsentRef(entity.getConsentRef().toString());
    result.setClientAppId(entity.getClientAppId());
    result.setClientAppName(entity.getClientAppName());
    result.setCreateDate(entity.getCreateDate());
    result.setUpdateDate(entity.getUpdateDate());

    PisOutputConsentDto.TransactionInformation transactionInformation = new PisOutputConsentDto.TransactionInformation();
    transactionInformation.setEndToEndIdentification(entity.getEndToEndIdentification());
    transactionInformation.setInstructionIdentification(entity.getInstructionIdentification());
    result.setTransactionInformation(transactionInformation);

    PisOutputConsentDto.Initiation initiation = new PisOutputConsentDto.Initiation();
    initiation.setPaymentType(entity.getPaymentType().getValue());
    initiation.setLocalInstrument(entity.getLocalInstrument().getValue());
    initiation.setPaymentPurposeCode(entity.getPaymentPurposeCode());
    initiation.setPaymentReason(entity.getPaymentReason());
    initiation.setChargeType(entity.getChargeType());

    if (entity.getCreditorCurrency() != null) {
      initiation.setCreditorCurrency(entity.getCreditorCurrency());
    }

    PisOutputConsentDto.InstructedAmount instructedAmount = new PisOutputConsentDto.InstructedAmount();
    instructedAmount.setAmount(entity.getAmount());
    instructedAmount.setCurrency(entity.getCurrency().name());
    initiation.setInstructedAmount(instructedAmount);

    PisOutputConsentDto.Account crerditorAccount = new PisOutputConsentDto.Account();
    crerditorAccount.setIdentification(entity.getCreditorAccRef());
    crerditorAccount.setSchemeName(entity.getCreditorAccScheme().getValue());
    crerditorAccount.setSecondaryIdentificationSchemeName(entity.getCreditorSecondaryIdentificationSchemeName());
    crerditorAccount.setSecondaryIdentification(entity.getCreditorSecondaryIdentification());
    initiation.setCreditorAccount(crerditorAccount);

    PisOutputConsentDto.Account debtorAccount = new PisOutputConsentDto.Account();
    debtorAccount.setIdentification(entity.getDebtorAccRef());
    if (entity.getDebtorAccScheme() != null) {
      debtorAccount.setSchemeName(entity.getDebtorAccScheme().getValue());
    }
    initiation.setDebtorAccount(debtorAccount);

    PisOutputConsentDto.BeneficiaryData beneficiaryData = processBeneficiaryData(entity);
    initiation.setBeneficiaryData(beneficiaryData);
    result.setInitiation(initiation);


    return result;
  }

  private PisOutputConsentDto.BeneficiaryData processBeneficiaryData(PisConsentEntity entity) {
    PisOutputConsentDto.BeneficiaryData beneficiaryData = new PisOutputConsentDto.BeneficiaryData();

    String beneficiaryNameEn = entity.getCreditorNameEn();
    if(isNullOrEmpty(beneficiaryNameEn) ){
      beneficiaryNameEn = entity.getBeneficiaryFirstName()+" " +entity.getBeneficiaryMiddleName() + " " + entity.getBeneficiaryLastName();
    }
    PisOutputConsentDto.Name name = new PisOutputConsentDto.Name();
    name.setArName(entity.getCreditorNameAr());
    name.setEnName(beneficiaryNameEn);
    beneficiaryData.setBeneficiaryName(name);

    PisOutputConsentDto.Address address = new PisOutputConsentDto.Address();
    address.setCity(entity.getCreditorCity());
    address.setState(entity.getCreditorState());
    address.setPostcode(entity.getCreditorPostcode());

    PisOutputConsentDto.AddressLines addressLines = new PisOutputConsentDto.AddressLines();
    addressLines.setAddressLine1(entity.getCreditorAddressLine1());
    addressLines.setAddressLine2(entity.getCreditorAddressLine2());
    addressLines.setAddressLine3(entity.getCreditorAddressLine3());
    address.setAddressLines(addressLines);

    PisOutputConsentDto.CountryInfo countryInfo = new PisOutputConsentDto.CountryInfo();
    countryInfo.setCountryCode(entity.getCreditorCountryCode());
    countryInfo.setCountryName(entity.getCreditorCountryName());
    address.setCountryInfo(countryInfo);

    beneficiaryData.setBeneficiaryAddress(address);

    PisOutputConsentDto.BeneficiaryAgent beneficiaryAgent = new PisOutputConsentDto.BeneficiaryAgent();
    PisOutputConsentDto.Agent agent = new PisOutputConsentDto.Agent();
    if (entity.getAgentIdentification() != null && entity.getAgentSchema() != null) {
      agent.setIdentification(entity.getAgentIdentification());
      agent.setSchema(entity.getAgentSchema());
    }else if(!isNullOrEmpty(entity.getBeneficiaryBicCode())){ //Check if the beneficiary BicCode available (in case of retrieved from Jopacc fat Alias resolving)
      agent.setIdentification(entity.getBeneficiaryBicCode());
      agent.setSchema(AppConstants.BIC_CODE);
    }

    beneficiaryAgent.setAgentIdentification(agent);
    beneficiaryData.setBeneficiaryAgent(beneficiaryAgent);

    return beneficiaryData;
  }
}
