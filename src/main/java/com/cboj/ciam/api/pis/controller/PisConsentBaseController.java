package com.cboj.ciam.api.pis.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.*;
import com.cboj.ciam.api.pis.PisBaseConsentDto;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisConsentWrapper;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.TokenClaimsUtils;
import com.cboj.ciam.service.auth.pis.PisAuthorizationService;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.KeycloakRequest;
import com.fasterxml.jackson.annotation.JsonView;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

@Slf4j
@RequiredArgsConstructor
public class PisConsentBaseController<T extends PisBaseConsentDto> {

  private final Mapper<T, PisConsentDto> mapper;
  private final Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper;
  private final PisAuthorizationService pisAuthorizationService;
  private final KeycloakAdminService keycloakAdminService;
  private final PisConsentDataService service;

  /**
   * Create client consent.
   *
   * @param inputConsentDto The consent information.
   * @param request         The HTTP servlet request.
   * @return ResponseEntity with CreateConsentResponse
   * @throws org.springframework.web.server.ResponseStatusException if there is an error during the operation.
   */
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize("hasAuthority(T(com.cboj.ciam.consts.SsoConstants.Scope).PAYMENTS)")
  @Operation(operationId = "CreatePisConsent",
      summary = "Creates a new PIS consent.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "403", description = "Authorization error",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = OpenBankingErrorDto.class)))
      })
  @DefaultPublicErrorResponse
  @JsonView(Views.Public.class)
  public CompletableFuture<ResponseEntity<PisConsentWrapper>> createConsent(
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Consent input data", required = true)
      @RequestBody @Valid @NotNull T inputConsentDto,
      HttpServletRequest request) {
    return pisAuthorizationService.createConsent(mapper.apply(inputConsentDto), request)
        .thenApply(consentResponse -> ResponseEntity.status(HttpStatus.CREATED).body(new PisConsentWrapper(consentResponse)))
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Get accounts consent by extended identifier.
   *
   * @param consentRef The consent extended identifier. (path parameter)
   * @return Accounts consent object.
   * @throws NotFoundException        If PisConsent is not found for the consentRef.
   * @throws UnauthorizedException    If the authorization header is missing or not using the basic authentication type.
   * @throws IllegalArgumentException If consentRef is blank.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetPisConsent",
      summary = "Returns information about the PIS consent using an external consent identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = OpenBankingErrorDto.class)))
      })
  @DefaultPublicErrorResponse
  @JsonView(Views.Public.class)
  public CompletableFuture<ResponseEntity<PisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class, example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7"))
      @NonNull @PathVariable("consentRef") UUID consentRef, HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    Map<String, Object> attrsData = new HashMap<>();
    attrsData.put(PisConsentEntity_.CONSENT_REF, consentRef);
    attrsData.put(PisConsentEntity_.CLIENT_APP_ID, clientId);

    return service.findByAttrs(attrsData)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
          }

          PisOutputConsentDto outputConsentDto = outputMapper.apply(consent);

          return ResponseEntity.ok(new PisConsentWrapper(outputConsentDto));
        })
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Deletes a consent by extended identifier.
   *
   * @param consentRef The consent extended identifier.
   * @param request    The HttpServletRequest object.
   * @return Response indicating the result of the delete operation.
   * @throws NotFoundException If the PisConsent is not found for the consentRef.
   * @throws RuntimeException  If the revoke user token request fails.
   */
  @DeleteMapping(value = "/{consentRef}")
  @Operation(operationId = "DeletePisConsent",
      summary = "Delete consent by external identifier.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful completion of operation")
      })
  @DefaultPublicErrorResponse
  public CompletableFuture<ResponseEntity<Void>> delete(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier") @PathVariable("consentRef") @NonNull UUID consentRef,
      HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    //Checking
    Map<String, Object> attrsData = new HashMap<>();
    attrsData.put(PisConsentEntity_.CONSENT_REF, consentRef);
    attrsData.put(PisConsentEntity_.CLIENT_APP_ID, clientId);

    return service.findByAttrs(attrsData)
        .thenCompose(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
          }

          if (!consent.getStatus().equals(ConsentStatus.ACTIVE)) {
            throw new NotFoundException(
                String.format("Error due deleting consent %s. Non active PisConsent cannot be deleted ", consentRef));
          }

          String sessionState;
          String userId;

          if (consent.getRefreshToken() != null) {
            try {
              JWTClaimsSet claims = JWTParser.parse(consent.getRefreshToken()).getJWTClaimsSet();
              sessionState = claims.getStringClaim(SsoConstants.User.SID_CLAIM_NAME);
              userId = claims.getStringClaim(SsoConstants.User.SUB_CLAIM_NAME);
            } catch (ParseException ex) {
              throw new UnauthorizedException(ex.getLocalizedMessage());
            }

            keycloakAdminService.processRevokeUserToken(KeycloakRequest.builder()
                .clientId(clientId)
                .userId(userId)
                .state(sessionState)
                .refreshToken(consent.getRefreshToken())
                .build());
          }

          consent.setStatus(ConsentStatus.REVOKED);

          return service.update(consent);
        })
        .thenApply(updated -> ResponseEntity.status(HttpStatus.NO_CONTENT).<Void>build())
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private OpenBankingException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new OpenBankingException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new OpenBankingException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
