package com.cboj.ciam.api.pis.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisDomesticInputConsentDto;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;

@Component
public class PisDomesticInputDtoMapper implements Mapper<PisDomesticInputConsentDto, PisConsentDto> {

    @Override
    public PisConsentDto apply(PisDomesticInputConsentDto dto) {
        PisConsentDto result = new PisConsentDto();
        PisDomesticInputConsentDto.TransactionInformation transactionInformation = dto.getTransactionInformation();
        if (transactionInformation != null) {
            result.setInstructionIdentification(transactionInformation.getInstructionIdentification());
            result.setEndToEndIdentification(transactionInformation.getEndToEndIdentification());
        }

        PisDomesticInputConsentDto.Initiation initiation = dto.getInitiation();
        if (initiation != null) {
            result.setPaymentType(initiation.getPaymentType());
            result.setPaymentPurposeCode(initiation.getPaymentPurposeCode());
            result.setPaymentReason(initiation.getPaymentReason());
            result.setChargeType(initiation.getChargeType());

            // Setting local instrument
            String localInstrument = StringUtils.isEmpty(initiation.getLocalInstrument())
                    ? LocalInstrument.DOMESTIC.getValue() : initiation.getLocalInstrument();
            result.setLocalInstrument(localInstrument);

            PisDomesticInputConsentDto.InstructedAmount instructedAmount = initiation.getInstructedAmount();
            if (instructedAmount != null) {
                result.setAmount(instructedAmount.getAmount());
                result.setCurrency(instructedAmount.getCurrency());
            }

            if (initiation.getLocalInstrument().equals(LocalInstrument.DOMESTIC.getValue())) {
                if (initiation.getCategoryPurposeCode() == null) {
                    throw new ValidationException("categoryPurposeCode is empty");
                } else {
                    result.setCategoryPurposeCode(initiation.getCategoryPurposeCode());
                }
            }


            PisDomesticInputConsentDto.Account crerditorAccount = initiation.getCreditorAccount();
            if (crerditorAccount != null) {
                result.setCreditorAccRef(crerditorAccount.getIdentification());
                result.setCreditorAccScheme(crerditorAccount.getSchemeName());
            }

            PisDomesticInputConsentDto.Account debtorAccount = initiation.getDebtorAccount();
            if (debtorAccount != null) {
                result.setDebtorAccRef(debtorAccount.getIdentification());

                if (StringUtils.isEmpty(debtorAccount.getSchemeName())) {
                    throw new ValidationException("Debtor account scheme name is empty");
                }

                result.setDebtorAccScheme(debtorAccount.getSchemeName());
            }

            PisDomesticInputConsentDto.BeneficiaryData beneficiaryData = initiation.getBeneficiaryData();
            if (beneficiaryData != null) {
                PisDomesticInputConsentDto.Name name = beneficiaryData.getBeneficiaryName();
                if (name != null) {
                    result.setBeneficiaryFirstName(name.getFirstName());
                    result.setBeneficiaryMiddleName(name.getMiddleName());
                    result.setBeneficiaryLastName(name.getLastName());

                }

                PisDomesticInputConsentDto.Address address = beneficiaryData.getBeneficiaryAddress();
                if (address != null) {
                    result.setCreditorCity(address.getCity());
                    result.setCreditorState(address.getState());
                    result.setCreditorPostcode(address.getPostcode());
                    result.setBeneficiaryAddressLine(address.getAddressLine());
                    result.setStreetName(address.getStreetName());
                    result.setTownName(address.getTownName());
                    result.setBuildingNumber(address.getBuildingNumber());

                    PisDomesticInputConsentDto.CountryInfo countryInfo = address.getCountryInfo();
                    if (countryInfo != null) {
                        result.setCreditorCountryCode(countryInfo.getCountryCode());
                        result.setCreditorCountryName(countryInfo.getCountryName());
                    }
                }

                PisDomesticInputConsentDto.BeneficiaryAgent beneficiaryAgent = beneficiaryData.getBeneficiaryAgent();
                if (beneficiaryAgent != null) {
                    PisDomesticInputConsentDto.Agent agent = beneficiaryAgent.getAgentIdentification();
                    if (agent != null) {
                        result.setAgentIdentification(agent.getIdentification());
                        result.setAgentSchema(agent.getSchema());
                    }
                }
            }
        }
        return result;
    }
}
