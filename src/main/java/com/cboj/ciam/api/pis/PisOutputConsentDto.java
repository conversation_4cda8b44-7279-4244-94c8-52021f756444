package com.cboj.ciam.api.pis;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "PIS internal consent input data representation.")
public class PisOutputConsentDto extends PisBaseConsentDto {

  @Schema(description = "TPP client application ID that created the consent", example = "c3bdde79e4ea4b63933885dbbe450539")
  public String clientAppId;

  @Schema(description = "TPP client application name that created the consent", example = "Fintech Company")
  public String clientAppName;

  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime createDate;

  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime updateDate;

  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  private String customerRef;

  @Schema(description = "Customer sso external identifier to fetch additional information", example = "2410116")
  private String ssoCustomerRef;

  @Schema(description = "Client consent external identifier", example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  private String consentRef;

  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  private String status;

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs", example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> loginUrls;

  @JsonView({ Views.Mobile.class, Views.Public.class })
  @Schema(description = "A list of possible debtor accounts", implementation = AccountView.class)
  private Set<AccountView> accounts;

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Initiation information", implementation = Initiation.class)
  private Initiation initiation;

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode(callSuper = true)
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Initiation extends PisBaseConsentDto.Initiation {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "The domestic payment method", example = "DomesticRtgs")
    private String localInstrument;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Beneficiary information")
    private BeneficiaryData beneficiaryData;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class BeneficiaryData {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Beneficiary names")
    private Name beneficiaryName;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Beneficiary address")
    private Address beneficiaryAddress;

    @JsonView({ Views.Admin.class, Views.Mule.class})
    @Schema(description = "Beneficiary Agent")
    private BeneficiaryAgent beneficiaryAgent;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class BeneficiaryAgent {

    @JsonView({Views.Admin.class, Views.Mule.class})
    @Schema(description = "Beneficiary Agent Identification")
    private Agent agentIdentification;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  public static class Agent {

    @JsonView({ Views.Admin.class, Views.Mule.class})
    @Schema(description = "Agent Identification")
    private String identification;

    @JsonView({Views.Admin.class, Views.Mule.class})
    @Schema(description = "Schema of Agent")
    private String schema;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Name {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "A payment requisite to make external payments")
    private String enName;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "A payment requisite to make external payments")
    private String arName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Address {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Address lines")
    private AddressLines addressLines;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "City")
    private String city;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "State")
    private String state;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Post code")
    private String postcode;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Country information")
    private CountryInfo countryInfo;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class AddressLines {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Address line 1")
    private String addressLine1;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Address line 2")
    private String addressLine2;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Address line 3")
    private String addressLine3;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class CountryInfo {

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Country code")
    private String countryCode;

    @JsonView({ Views.Admin.class, Views.Mule.class })
    @Schema(description = "Country name")
    private String countryName;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class AccountView {

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Account identifier")
    private String accountRef;

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Account number")
    private String accountNumber;

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Account currency")
    private String currency;

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Account code")
    private String gbCode;

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Description in English")
    private String descriptionEn;

    @JsonView({ Views.Mobile.class })
    @Schema(description = "Description in Arabic")
    private String descriptionAr;

  }
}
