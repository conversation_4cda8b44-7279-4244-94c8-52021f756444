package com.cboj.ciam.api.pis;

import com.cboj.ciam.api.BaseDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PaymentType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


/**
 * Data transfer object that represents a full set of attributes for an PIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "PIS consent")
public class PisConsentDto extends BaseDto {

  @JsonIgnore
  @Schema(description = "Customer external identifier")
  private String customerRef;

  @JsonIgnore
  @Schema(description = "TPP client application identifier")
  private String clientAppId;

  @JsonIgnore
  @Schema(description = "TPP client application name")
  private String clientAppName;

  @JsonIgnore
  @Schema(description = "TPP client application redirect URL")
  private String redirectUrl;

  @JsonIgnore
  @Schema(description = "OAuth2 authorization code")
  private String authCode;

  @JsonIgnore
  @Schema(description = "Refresh token related to the consent")
  private String refreshToken;

  @Schema(description = "A unique string that identifies the payment initiation")
  private String instructionIdentification;

  @Schema(description = "A unique string that identifies the whole business transaction")
  private String endToEndIdentification;

  @Schema(description = "The amount of the payment excluding charges")
  private Double amount;

  @Schema(description = "The creditor account identifier")
  private String creditorAccRef;

  @Schema(description = "The debtor account identifier")
  private String debtorAccRef;

  @Schema(description = "Payment code that is mandatory for a payment transaction")
  private String paymentPurposeCode;

  @Schema(description = "Payment purpose description")
  private String paymentPurposeDesc;

  @Schema(description = "Category Purpose code that is mandatory for a payment transaction")
  private String categoryPurposeCode;

  @Schema(description = "Payment reason")
  private String paymentReason;

  @Schema(description = "Creditor name (EN)")
  private String creditorNameEn;

  @Schema(description = "Creditor name (AR)")
  private String creditorNameAr;

  @Schema(description = "Creditor address line")
  private String beneficiaryAddressLine;

  @Schema(description = "Creditor city")
  private String creditorCity;

  @Schema(description = "Creditor state")
  private String creditorState;

  @Schema(description = "Creditor post code")
  private String creditorPostcode;

  @Schema(description = "Creditor country code")
  private String creditorCountryCode;

  @Schema(description = "Creditor country name")
  private String creditorCountryName;

  @Schema(description = "The charge type")
  private String chargeType;

  @Schema(description = "The currency of the payment", implementation = PaymentCurrency.class)
  private String currency;

  @Schema(description = "The payment method", implementation = LocalInstrument.class)
  private String localInstrument;

  @Schema(description = "Payment type", implementation = PaymentType.class)
  private String paymentType;

  @Schema(description = "The creditor account identification scheme", implementation = AccountScheme.class)
  private String creditorAccScheme;

  @Schema(description = "The debtor account identification scheme", implementation = AccountScheme.class)
  private String debtorAccScheme;

  @Schema(description = "Beneficiary agent identification")
  private String agentIdentification;

  @Schema(description = "Beneficiary agent schema")
  private String agentSchema;

  @Schema(description = "Beneficiary first name")
  private String beneficiaryFirstName;

  @Schema(description = "Beneficiary middle name")
  private String beneficiaryMiddleName;

  @Schema(description = "Beneficiary last name")
  private String beneficiaryLastName;

  @Schema(description = "Creditor currency")
  private String creditorCurrency;

  @Schema(description = "Street name")
  private String streetName;

  @Schema(description = "Building Number")
  private String buildingNumber;

  @Schema(description = "townName")
  private String townName;

  @Schema(description = "beneficiary bicCode")
  private String beneficiaryBicCode;

  @Schema(description = "SecondaryIdentificationSchemeName")
  private String secondaryIdentificationSchemeName;

  @Schema(description = "SecondaryIdentification")
  private String secondaryIdentification;
}

