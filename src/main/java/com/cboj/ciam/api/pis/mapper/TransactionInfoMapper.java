package com.cboj.ciam.api.pis.mapper;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import org.springframework.stereotype.Component;

@Component
public class TransactionInfoMapper implements Mapper<PisConsentDto, TransactionInfoRequest> {

    @Override
    public TransactionInfoRequest apply(PisConsentDto dto) {
        return TransactionInfoRequest.builder()
                .initiation(TransactionInfoRequest.InitiationDTO.builder()
                        .paymentType("Single")
                        .paymentPurposeCode(dto.getPaymentPurposeCode())
                        .paymentPurposeDesc(dto.getPaymentPurposeDesc())
                        .localInstrument(dto.getLocalInstrument())
                        .chargeType(dto.getChargeType())
                        .instructedAmount(TransactionInfoRequest.InstructedAmountDTO.builder()
                                .amount(dto.getAmount())
                                .currency(dto.getCurrency())
                                .build())
                        .creditorAccount(TransactionInfoRequest.AccountDTO.builder()
                                .identification(dto.getCreditorAccRef())
                                .schemeName(dto.getCreditorAccScheme())
                                .build())
                        .debtorAccount(TransactionInfoRequest.AccountDTO.builder()
                                .identification(dto.getDebtorAccRef())
                                .schemeName(dto.getDebtorAccScheme())
                                .build())
                        .beneficiaryData(TransactionInfoRequest.BeneficiaryDataDTO.builder()
                                .beneficiaryName(TransactionInfoRequest.BeneficiaryNameDTO.builder()
                                        .firstName(defaultEmptyStringIfNull(dto.getBeneficiaryFirstName()))
                                        .middleName(defaultEmptyStringIfNull(dto.getBeneficiaryMiddleName()))
                                        .lastName(defaultEmptyStringIfNull(dto.getBeneficiaryLastName()))
                                        .build())
                                .beneficiaryAddress(TransactionInfoRequest.BeneficiaryAddressDTO.builder()
                                        .city(defaultEmptyStringIfNull(dto.getCreditorCity()))
                                        .state(defaultEmptyStringIfNull(dto.getCreditorState()))
                                        .postcode(defaultEmptyStringIfNull(dto.getCreditorPostcode()))
                                        .countryInfo(TransactionInfoRequest.CountryInfoDTO.builder()
                                                .countryCode(defaultEmptyStringIfNull(dto.getCreditorCountryCode()))
                                                .countryName(defaultEmptyStringIfNull(dto.getCreditorCountryName()))
                                                .build())
                                        .build())
                                .beneficiaryAgent(TransactionInfoRequest.BeneficiaryAgentDTO.builder()
                                        .agentIdentification(TransactionInfoRequest.AgentIdentificationDTO.builder()
                                                .identification(defaultEmptyStringIfNull(dto.getAgentIdentification()))
                                                .schema(defaultEmptyStringIfNull(dto.getAgentSchema()))
                                                .build())
                                        .build())
                                .build())
                        .build())
                .customerRef("") // Optional
                .build();
    }

    private static String defaultEmptyStringIfNull(String value) {
        return value == null ? "" : value;
    }

}
