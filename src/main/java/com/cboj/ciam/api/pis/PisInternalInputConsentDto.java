package com.cboj.ciam.api.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * Data transfer object that represents only input attributes for the internal PIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "PIS internal consent input data representation.")
public class PisInternalInputConsentDto extends PisBaseConsentDto {
}
