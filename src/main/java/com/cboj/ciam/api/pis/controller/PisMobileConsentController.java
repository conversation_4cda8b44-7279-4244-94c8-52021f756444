package com.cboj.ciam.api.pis.controller;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.DefaultInternalErrorResponse;
import com.cboj.ciam.api.InternalApiErrorDto;
import com.cboj.ciam.api.InternalApiException;
import com.cboj.ciam.api.Views;
import com.cboj.ciam.api.pis.PisConsentWrapper;
import com.cboj.ciam.api.pis.PisMobileAccountDto;
import com.cboj.ciam.api.pis.PisMobileInputConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.auth.pis.PisAuthorizationService;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoDto;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

/**
 * PIS-specific consent controller.
 */
@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.MOBILE_PIS_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "Mobile Backend API")
@SecurityRequirement(name = SecurityConstants.MOBILE_API_BEARER_TOKEN)
public class PisMobileConsentController {

  private final Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper;
  private final PisAuthorizationService authorizationService;
  private final PisConsentDataService dataService;
  private final TransactionInfoService transactionInfoService;

  /**
   * Retrieves PIS consent data by its extended identifier.
   *
   * @param consentRef The consent extended identifier.
   * @return ResponseEntity containing a UserConsentDto.
   * @throws NotFoundException if the PisConsent is not found for the specified consentRef.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetPisConsent",
      summary = "Get payments consent by extended identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mobile.class)
  public CompletableFuture<ResponseEntity<PisConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef) {

    return dataService.findByAttr(PisConsentEntity_.CONSENT_REF, consentRef)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
          }

          PisOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new PisConsentWrapper(output));
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  /**
   * Processes the consent for a given account.
   *
   * @param consentRef The consent extended identifier.
   * @param dto        The data model for giving consent {@link PisMobileInputConsentDto}.
   * @return ResponseEntity containing the response to the consent request.
   */
  @PutMapping(path = "/{consentRef}",
      consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "UpdatePisConsent",
      summary = "Update PIS consent.",
      description = "Update the PIS consent based on the customer's decision.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful completion of operation")
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mobile.class)
  public CompletableFuture<ResponseEntity<Void>> processConsent(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent external identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef,
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Data model for giving consent")
      @RequestBody @Valid @NotNull PisMobileInputConsentDto dto) {

    CompletableFuture<ResponseEntity<Void>> result;

    if (dto.getStatus().equals(ConsentStatus.REJECTED.getValue())) {
      result = authorizationService.declineConsents(consentRef)
          .thenApply(unused -> ResponseEntity.status(HttpStatus.NO_CONTENT).build());
    } else {
      result = authorizationService.approveConsents(consentRef, dto.getDebtorAccountRef())
          .thenApply(unused -> ResponseEntity.status(HttpStatus.NO_CONTENT).build());
    }

    return result.exceptionally(throwable -> {
      throw processException(throwable);
    });
  }

  /**
   * Retrieves the transaction information for a given consent.
   *
   * @param consentRef The extended identifier of the consent.
   * @param dto        The data model containing the transaction information.
   * @return Transaction information.
   * @throws NotFoundException if the consent or customer ID associated with the consent is not found.
   */
  @GetMapping(path = "/{consentRef}/transaction-info", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetPisConsentTransactionInfo",
      summary = "Get payments consents' transaction info by consents' extended identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  public CompletableFuture<ResponseEntity<TransactionInfoDto>> getTransactionInfo(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef,
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Data model for giving transaction information")
      @RequestBody @Valid @NotNull PisMobileAccountDto dto) {
    return transactionInfoService.getTransactionInfo(consentRef.toString(), dto.getDebtorAccount().getIdentification())
        .thenApply(ResponseEntity::ok)
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
