package com.cboj.ciam.api.pis;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.consts.AppConstants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public abstract class PisBaseConsentDto {

  @Schema(description = "Transaction information", requiredMode = Schema.RequiredMode.REQUIRED)
  protected TransactionInformation transactionInformation;

  @Schema(description = "Initiation information", requiredMode = Schema.RequiredMode.REQUIRED)
  protected Initiation initiation;

  @Get<PERSON>
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class TransactionInformation {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "A unique string that identifies the whole business transaction",
        requiredMode = Schema.RequiredMode.REQUIRED,
        example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
    private String endToEndIdentification;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "A unique string that identifies the payment initiation",
        requiredMode = Schema.RequiredMode.REQUIRED,
        example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
    private String instructionIdentification;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Initiation {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Payment type", requiredMode = Schema.RequiredMode.REQUIRED, example = "Single")
    private String paymentType;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Payment code that is mandatory for a payment transaction", requiredMode = Schema.RequiredMode.REQUIRED,example = "PRS")
    private String paymentPurposeCode;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Category purpose code that is mandatory for a payment transaction", requiredMode = Schema.RequiredMode.REQUIRED,example = "Personal")
    private String categoryPurposeCode;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Payment reason", example = "PRS")
    private String paymentReason;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Amount information", requiredMode = Schema.RequiredMode.REQUIRED)
    private InstructedAmount instructedAmount;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Creditor account information", requiredMode = Schema.RequiredMode.REQUIRED)
    private Account creditorAccount;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Debtor account information", requiredMode = Schema.RequiredMode.REQUIRED)
    private Account debtorAccount;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Charge type")
    private String chargeType;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "Creditor Currency",  example = "Single")
    private String creditorCurrency;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class InstructedAmount {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The amount of the payment excluding charges", requiredMode = Schema.RequiredMode.REQUIRED, example = "123.32")
    private Double amount;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The currency of the payment", requiredMode = Schema.RequiredMode.REQUIRED, example = AppConstants.Currencies.JOD)
    private String currency;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Account {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The creditor account identifier", requiredMode = Schema.RequiredMode.REQUIRED,
        example = "******************************")
    private String identification;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The creditor account identification scheme", requiredMode = Schema.RequiredMode.REQUIRED,
        example = "JO.OB.IBAN")
    private String schemeName;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The secondary creditor account identification scheme", requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            example = "JO.OB.IBAN")
    private String secondaryIdentificationSchemeName;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The secondary creditor account identifier", requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            example = "******************************")
    private String secondaryIdentification;
  }
}
