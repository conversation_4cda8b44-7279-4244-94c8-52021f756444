package com.cboj.ciam.api.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "PIS internal consent input data representation.")
public class PisMobileInputConsentDto {

  @Schema(description = "Target consent status (Active or Rejected)", requiredMode = Schema.RequiredMode.REQUIRED, example = "Active")
  private String status;

  @Schema(description = "The debtor account identifier", example = "4867184")
  private String debtorAccountRef;
}
