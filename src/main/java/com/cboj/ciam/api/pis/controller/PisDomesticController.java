package com.cboj.ciam.api.pis.controller;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisDomesticInputConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.auth.pis.PisAuthorizationService;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@RestController
@RequestMapping(RestConstants.Paths.API_PIS_DOMESTIC_PAYMENT_CONSENTS)
@Tag(name = "Open Banking API")
@SecurityRequirement(name = SecurityConstants.OAUTH_CLIENT_CRED_SCHEME)
public class PisDomesticController extends PisConsentBaseController<PisDomesticInputConsentDto> {

  public PisDomesticController(Mapper<PisDomesticInputConsentDto, PisConsentDto> mapper,
                               Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper,
                               PisAuthorizationService pisAuthorizationService,
                               KeycloakAdminService keycloakAdminService,
                               PisConsentDataService service) {
    super(mapper, outputMapper, pisAuthorizationService, keycloakAdminService, service);
  }
}
