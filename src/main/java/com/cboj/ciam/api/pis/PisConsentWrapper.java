package com.cboj.ciam.api.pis;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * This class is a representation of consent for API responses.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Payment Information Services consent representation.")
public class PisConsentWrapper {

  @JsonIgnore
  protected final PisOutputConsentDto consent;

  public PisConsentWrapper(PisOutputConsentDto consent) {
    this.consent = consent;
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "PIS consent external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getConsentRef() {
    return consent.getConsentRef();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "PIS consent sso external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getSsoConsentRef() {
    return consent.getSsoCustomerRef();
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  public String getStatus() {
    return consent.getStatus();
  }

  @JsonView({ Views.Mule.class, Views.Admin.class })
  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  public String getCustomerRef() {
    return consent.getCustomerRef();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application ID that created the consent",
      example = "c3bdde79e4ea4b63933885dbbe450539")
  public String getClientAppId() {
    return consent.getClientAppId();
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application name that created the consent",
      example = "Fintech Company")
  public String getClientAppName() {
    return consent.getClientAppName();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime getCreateDateTime() {
    return consent.getCreateDate();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime getUpdateDateTime() {
    return consent.getUpdateDate();
  }

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs",
      example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> getLoginUrls() {
    return consent.getLoginUrls();
  }

  @JsonView({ Views.Mobile.class, Views.Public.class })
  @Schema(description = "A list of possible debtor accounts", implementation = PisOutputConsentDto.AccountView.class)
  public Set<PisOutputConsentDto.AccountView> getAccounts() {
    return consent.getAccounts();
  }

  @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
  @Schema(description = "Transaction information")
  private PisOutputConsentDto.TransactionInformation getTransactionInformation() {
    return consent.getTransactionInformation();
  }

  @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
  @Schema(description = "Initiation information")
  private PisOutputConsentDto.Initiation getInitiation() {
    return consent.getInitiation();
  }

}
