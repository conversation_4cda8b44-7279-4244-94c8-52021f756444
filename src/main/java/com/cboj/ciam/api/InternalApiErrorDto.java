package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Internal response object")
public class InternalApiErrorDto {

  public InternalApiErrorDto(HttpStatus status, String message) {
    this.success = false;
    this.code = status.value();
    this.reasonCode = status.getReasonPhrase();
    this.message = message;
  }

  public InternalApiErrorDto() {
    this.success = false;
  }

  @Schema(description = "Whether the operation was successful or not", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
  private Boolean success;

  @Schema(description = "HTTP error code", requiredMode = Schema.RequiredMode.REQUIRED, example = "400")
  private Integer code;

  @Schema(description = "Error message", requiredMode = Schema.RequiredMode.REQUIRED, example = "Bad request")
  private String message;

  @Schema(description = "Reason code")
  private String reasonCode;
}
