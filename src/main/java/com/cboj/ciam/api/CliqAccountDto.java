package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
        ignoreUnknown = true
)
@Schema(description = "Customer account information")
public class CliqAccountDto extends BaseDto {

    @Schema(description = "IBAN")
    private String iban;

    @Schema(description = "Account type")
    private String type;

    @Schema(description = "Account currency")
    private String currency;

    @Schema(description = "BIC code")
    private String bic;

    @Schema(description = "Customer type")
    private String customerType;

    @Schema(description = "Customer name")
    private String customerName;

    @Schema(description = "Customer Address")
    private String customerAddress;

}
