package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * Controller class to handle various admin operations.
 */
@Validated
@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.ADMIN_COMMON)
@RequiredArgsConstructor
@Tag(name = "Administration API")
@SecurityRequirement(name = SecurityConstants.ADMIN_API_BEARER_TOKEN)
public class AdminController {

  private final KeycloakAdminService keycloakService;
  private final AisConsentDataService service;

  /**
   * Revoke client.
   *
   * @param clientId The identifier of the client to be revoked.
   * @return Response indicating the result of the revoking operation.
   * @throws UnauthorizedException If the request does not contain basic authorization.
   */
  @DeleteMapping(path = "/clients/{clientId}")
  @Operation(operationId = "DisableClient",
      summary = "Disables the specified client application in RH SSO and revoke all of its consents.",
      responses = {
          @ApiResponse(responseCode = "204", description = "Successful client deletion")
      })
  @DefaultInternalErrorResponse
  public CompletableFuture<ResponseEntity<Void>> revokeClient(
      @Parameter(name = "clientId", in = ParameterIn.PATH, required = true,
          description = "Client identifier") @PathVariable("clientId") @NonNull String clientId) {

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null) {
      log.error("Method required authentication");
      throw new UnauthorizedException("Method required authentication");
    }

    CompletableFuture<ResponseEntity<String>> chain = CompletableFuture.supplyAsync(
        () -> keycloakService.processRevokeClient(clientId, authentication.getDetails().toString()));

    //Searching only NEW and ACTIVE consents (not final statuses)
    return chain.thenCompose(
            unused -> service.searchByStatusesAndClientId(clientId, ConsentStatus.NEW, ConsentStatus.ACTIVE))
        .thenCompose(aisConsents -> {
          CompletableFuture<ResponseEntity<Boolean>> consentsFuture = CompletableFuture.supplyAsync(() -> null);

          for (AisConsent consent : aisConsents) {
            consentsFuture = consentsFuture.thenCompose(__ -> service.delete(consent));
          }
          return consentsFuture;
        })
        .thenApply(updated -> ResponseEntity.status(HttpStatus.NO_CONTENT).<Void>build())
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
