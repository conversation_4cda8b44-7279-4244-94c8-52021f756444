package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.UUID;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Open Banking error response.")
public class OpenBankingErrorDto {

  public OpenBankingErrorDto(HttpStatus status, String message) {
    this.id = UUID.randomUUID();
    Status current = Status.getFromCode(status.value());
    this.code = current.getDescription();
    this.desc = message;
  }

  @Schema(description = "Unique identifier for the error response. Used for logging and tracking errors", requiredMode = Schema.RequiredMode.REQUIRED)
  private UUID id;

  @Schema(description = "High-level error code", requiredMode = Schema.RequiredMode.REQUIRED, example = "resource.notFound")
  private String code;

  @Schema(description = "Descriptive message of the error", example = "The requested resource could not be found")
  private String desc;

  @Getter
  private enum Status {
    REQUEST_INVALID(400, "http.request.invalid"),
    AUTH_INVALID(401, "http.auth.invalid"),
    RESOURCE_NOT_FOUND(404, "resource.notFound"),
    METHOD_INVALID(405, "http.method.invalid"),
    HTTP_HEADERS_INVALID(406, "http.headers.invalid"),
    UNSUPPORTED_ENCODING(415, "media.unsupportedEncoding"),
    SERVER_ERROR(500, "server.error");

    private final Integer code;
    private final String description;

    Status(Integer code, String description) {
      this.code = code;
      this.description = description;
    }

    public static Status getFromCode(Integer code) {
      return Arrays.stream(values())
          .filter(c -> c.code.equals(code))
          .findFirst()
          .orElse(SERVER_ERROR);
    }
  }
}
