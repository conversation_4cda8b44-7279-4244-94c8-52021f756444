package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * Data transfer object that represents the consent account.
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account information")
public class AccountDto extends BaseDto {

  @Schema(description = "Account identifier")
  private String accountRef;

  @Schema(description = "IBAN number")
  private String ibanNumber;

  @Schema(description = "Account currency")
  private String currency;

  @Schema(description = "Account Type")
  private String accountType;

  @Schema(description = "Category")
  private String category;

  @Schema(description = "Category Description")
  private String categoryDescription;

  @Schema(description = "Customer identifier")
  private String customerId;

  @Schema(description = "Account Holder Nane English")
  private String accountHolderNameEn;

  @Schema(description = "Account Holder Nane Arabic")
  private String accountHolderNameAr;

  //  @JsonIgnore
  private boolean selected;
}
