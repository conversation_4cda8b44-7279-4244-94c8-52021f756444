package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.UUID;

/**
 * Data Transfer Object (DTO) for giving user credentials.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Login customer request for the mobile backend.")
public class MobileAuthRequestDto {

  @NotNull(message = "Customer username not specified")
  @Schema(description = "Customer username", requiredMode = Schema.RequiredMode.REQUIRED, example = "john.smith")
  private String userName;

  @NotNull(message = "Customer password not specified")
  @Schema(description = "Customer password", requiredMode = Schema.RequiredMode.REQUIRED, example = "mypwd123")
  private String password;

  @Schema(description = "Key for decryption password ", requiredMode = Schema.RequiredMode.REQUIRED, example = "xYk4Ds8oJpwK3m")
  private String key;

  @NotNull(message = "Consent external identifier not specified")
  @Schema(description = "Consent external identifier", requiredMode = Schema.RequiredMode.REQUIRED, example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  private UUID consentRef;

  @NotNull(message = "TPP client ID not specified")
  @Schema(description = "TPP client ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "c3bdde79e4ea4b63933885dbbe450539")
  private String clientId;

  @NotNull(message = "TPP redirect URL not specified")
  @Schema(description = "TPP redirect URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com")
  private String redirectUrl;

  @NotNull(message = "Scope not specified")
  @Schema(description = "One of Open Banking scopes", requiredMode = Schema.RequiredMode.REQUIRED, example = "accounts")
  private String scope;

  @NotNull(message = "State not specified")
  @Schema(description = "OAuth2 state parameter used in authorization code grant type", requiredMode = Schema.RequiredMode.REQUIRED, example = "xYk4Ds8oJpwK3m")
  private String state;
}
