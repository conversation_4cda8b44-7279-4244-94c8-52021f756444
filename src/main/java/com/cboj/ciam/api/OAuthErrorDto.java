package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.util.Arrays;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "SSO response object")
public class OAuthErrorDto {

  public final static OAuthErrorDto defaultInstance =
      new OAuthErrorDto("Unrecognised server error", "See logs or details");

  private String error;
  private String error_description;
  private Status status;

  public OAuthErrorDto(String error, String error_description) {
    this.error = error;
    this.error_description = error_description;
    this.status = Status.getFromValue(error);
  }

  @Getter
  public enum Status {
    INVALID_REQUEST(HttpStatus.BAD_REQUEST, "invalid_request"),
    UNAUTHORIZED_CLIENT(HttpStatus.UNAUTHORIZED, "unauthorized_client"),
    INVALID_SCOPE(HttpStatus.FORBIDDEN, "invalid_scope"),
    SERVER_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "server_error");

    private final HttpStatus httpStatus;
    private final String value;

    Status(HttpStatus httpStatus, String value) {
      this.httpStatus = httpStatus;
      this.value = value;
    }

    public static OAuthErrorDto.Status getFromValue(String value) {
      return Arrays.stream(values())
          .filter(c -> c.value.equals(value))
          .findFirst()
          .orElse(SERVER_ERROR);
    }
  }

}
