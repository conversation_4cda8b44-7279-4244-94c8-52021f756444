package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * Data transfer object that represents the consent account.
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Consent account")
public class ConsentAccountDto extends BaseDto {

  @Schema(description = "Account identifier")
  private String accountRef;

  @Schema(description = "IBAN number")
  private String ibanNumber;

  @Schema(description = "Account title on english")
  private String titleEn;

  @Schema(description = "Account title on arabic")
  private String titleAr;

  @Schema(description = "Account currency")
  private String currency;

  @Schema(description = "Account view in list in english")
  private String listViewEn;

  @Schema(description = "Account view in list in arabic")
  private String listViewAr;

  @Schema(description = "Consent external identifier")
  private Long consentId;

  @Schema(description = "Category of Account")
  private String category;

  //  @JsonIgnore
  private boolean selected;
}
