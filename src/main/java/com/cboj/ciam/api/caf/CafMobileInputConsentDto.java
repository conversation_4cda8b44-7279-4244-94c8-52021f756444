package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.Views;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "CAF internal consent input data representation.")
public class CafMobileInputConsentDto {

  @JsonView(Views.Mobile.class)
  @Schema(description = "Target consent status (Active or Rejected)", requiredMode = Schema.RequiredMode.REQUIRED, example = "Active")
  private String status;

  @JsonView(Views.Mobile.class)
  @Schema(description = "The debtor account identifier", example = "4867184")
  private String debtorAccountRef;
}
