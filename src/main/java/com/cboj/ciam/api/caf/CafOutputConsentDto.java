package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "CAF internal consent input data representation.")
public class CafOutputConsentDto {

  @Schema(description = "TPP client application ID that created the consent", example = "c3bdde79e4ea4b63933885dbbe450539")
  public String clientAppId;

  @Schema(description = "TPP client application name that created the consent", example = "Fintech Company")
  public String clientAppName;

  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime createDate;

  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime updateDate;

  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  private String customerRef;

  @Schema(description = "Client consent external identifier", example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  private String consentRef;

  @Schema(description = "Customer sso external identifier to fetch additional information", example = "2410116")
  private String ssoCustomerRef;

  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  private String status;

  @JsonView({Views.Mule.class})
  @Schema(description = "Expiration date of the consent")
  private LocalDateTime expirationDateTime;

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs", example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> loginUrls;

  @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
  @Schema(description = "Debtor account information")
  private Account debtorAccount;

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Account {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The debtor account identifier", example = "******************************")
    private String identification;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The debtor account identification scheme", example = "JO.OB.IBAN")
    private String schemeName;
  }
}
