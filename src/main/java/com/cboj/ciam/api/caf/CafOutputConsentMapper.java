package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.jpa.caf.CafConsent;
import org.springframework.stereotype.Component;

/**
 * Mapper class to convert CafConsent objects to CafOutputConsentDto objects.
 */
@Component
public class CafOutputConsentMapper implements Mapper<CafConsent, CafOutputConsentDto> {

  @Override
  public CafOutputConsentDto apply(CafConsent entity) {
    CafOutputConsentDto result = new CafOutputConsentDto();

    result.setStatus(entity.getStatus().getValue());
    result.setCustomerRef(entity.getCustomerRef());
    result.setConsentRef(entity.getConsentRef().toString());
    result.setSsoCustomerRef(entity.getSsoCustomerRef());
    result.setClientAppId(entity.getClientAppId());
    result.setClientAppName(entity.getClientAppName());
    result.setCreateDate(entity.getCreateDate());
    result.setUpdateDate(entity.getUpdateDate());
    result.setExpirationDateTime(entity.getExpiryDate());

    CafOutputConsentDto.Account debtorAccount = new CafOutputConsentDto.Account();
    debtorAccount.setIdentification(entity.getDebtorAccRef());
    if (entity.getDebtorAccScheme() != null) {
      debtorAccount.setSchemeName(entity.getDebtorAccScheme().getValue());
    }
    result.setDebtorAccount(debtorAccount);

    return result;
  }
}
