package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.caf.CafConsent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;

/**
 * Function that converts an AisConsentDto object to an AisConsent object.
 */
@Component
public class CafConsentMapper implements Mapper<CafConsentDto, CafConsent> {

  @Override
  public CafConsent apply(CafConsentDto dto) {
    CafConsent entity = new CafConsent();
    entity.setId(dto.getId());
    entity.setClientAppId(dto.getClientAppId());
    entity.setClientAppName(dto.getClientAppName());
    entity.setCustomerRef(dto.getCustomerRef());
    entity.setRedirectUrl(dto.getRedirectUrl());
    entity.setAuthCode(dto.getAuthCode());
    entity.setRefreshToken(dto.getRefreshToken());
    entity.setExpiryDate(dto.getExpirationDateTime());

    final CafConsentDto.Account debtorAccount = dto.getDebtorAccount();

    if (debtorAccount != null) {
      if (StringUtils.isNotEmpty(debtorAccount.getIdentification())) {
        entity.setDebtorAccRef(debtorAccount.getIdentification());

        if (StringUtils.isEmpty(debtorAccount.getSchemeName())) {
          throw new ValidationException("Debtor account scheme name is empty");
        }

        entity.setDebtorAccScheme(AccountScheme.getFromValue(debtorAccount.getSchemeName()));
      }
    }

    return entity;
  }
}
