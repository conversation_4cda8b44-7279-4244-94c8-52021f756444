package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.ConsentStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * This class is a representation of consent for API responses.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account Information Services consent representation.")
public class CafConsentWrapper {

  @JsonIgnore
  protected final CafOutputConsentDto dto;

  public CafConsentWrapper(CafOutputConsentDto dto) {
    this.dto = dto;
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "CAF consent external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getConsentRef() {
    return dto.getConsentRef();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "PIS consent sso external identifier",
      example = "23b1600c-ec0b-4e4f-97e7-ab65b99787e7")
  public String getSsoConsentRef() {
    return dto.getSsoCustomerRef();
  }

  @JsonView({ Views.Public.class, Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "Consent status", implementation = ConsentStatus.class)
  public String getStatus() {
    return dto.getStatus();
  }

  @JsonView({ Views.Mule.class, Views.Admin.class })
  @Schema(description = "Customer external identifier to fetch additional information", example = "2410116")
  public String getCustomerRef() {
    return dto.getCustomerRef();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application ID that created the consent",
      example = "c3bdde79e4ea4b63933885dbbe450539")
  public String getClientAppId() {
    return dto.getClientAppId();
  }

  @JsonView({ Views.Mobile.class, Views.Admin.class })
  @Schema(description = "TPP client application name that created the consent",
      example = "Fintech Company")
  public String getClientAppName() {
    return dto.getClientAppName();
  }

  @JsonView(Views.Public.class)
  @Schema(description = "A list of possible login page URLs",
      example = "[\"app://open.my.app\", \"https://www.example.com\"]")
  private Set<String> getLoginUrls() {
    return dto.getLoginUrls();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "The date with time of the consent creation")
  public LocalDateTime getCreateDateTime() {
    return dto.getCreateDate();
  }

  @JsonView({ Views.Admin.class })
  @Schema(description = "The date with time of the last consent attributes update")
  public LocalDateTime getUpdateDateTime() {
    return dto.getUpdateDate();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class, Views.Public.class })
  @Schema(description = "Expiration date of the consent")
  public LocalDateTime getExpirationDateTime() {
    return dto.getExpirationDateTime();
  }

  @JsonView({ Views.Mule.class, Views.Mobile.class, Views.Admin.class, Views.Public.class })
  @Schema(description = "Debtor account information", implementation = CafOutputConsentDto.Account.class)
  public CafOutputConsentDto.Account getDebtorAccount() {
    return dto.getDebtorAccount();
  }

}
