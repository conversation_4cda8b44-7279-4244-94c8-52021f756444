package com.cboj.ciam.api.caf;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.*;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.jpa.caf.CafConsent_;
import com.cboj.ciam.service.data.caf.CafConsentDataService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.MULE_CAF_CONSENTS)
@RequiredArgsConstructor
@Tag(name = "MuleSoft Consent API")
@SecurityRequirement(name = SecurityConstants.MULE_API_BEARER_TOKEN)
public class CafMuleConsentController {

  private final Mapper<CafConsent, CafOutputConsentDto> outputMapper;
  private final CafConsentDataService dataService;

  /**
   * Get accounts consent by extended identifier.
   *
   * @param consentRef The consent extended identifier. (path parameter)
   * @return CAF consent object.
   * @throws NotFoundException        If CafConsent is not found for the consentRef.
   * @throws UnauthorizedException    If the authorization header is missing or not using the basic authentication type.
   * @throws IllegalArgumentException If consentRef is blank.
   */
  @GetMapping(path = "/{consentRef}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "GetCafConsent",
      summary = "Get CAF consent by extended identifier.",
      responses = {
          @ApiResponse(responseCode = "200", description = "Successful completion of operation"),
          @ApiResponse(responseCode = "404", description = "Object not found",
              content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                  schema = @Schema(implementation = InternalApiErrorDto.class)))
      })
  @DefaultInternalErrorResponse
  @JsonView(Views.Mule.class)
  public CompletableFuture<ResponseEntity<CafConsentWrapper>> get(
      @Parameter(name = "consentRef", in = ParameterIn.PATH, required = true,
          description = "Consent extended identifier",
          schema = @Schema(implementation = UUID.class)) @NonNull @PathVariable("consentRef") UUID consentRef) {

    return dataService.findByAttr(CafConsent_.CONSENT_REF, consentRef)
        .thenApply(consent -> {
          if (consent == null) {
            throw new NotFoundException(String.format("CafConsent not found for consentRef %s", consentRef));
          }

          CafOutputConsentDto output = outputMapper.apply(consent);

          return ResponseEntity.ok(new CafConsentWrapper(output));
        }).exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
