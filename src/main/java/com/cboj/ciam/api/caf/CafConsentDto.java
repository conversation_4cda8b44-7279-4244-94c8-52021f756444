package com.cboj.ciam.api.caf;

import com.cboj.ciam.api.BaseDto;
import com.cboj.ciam.api.Views;
import com.cboj.ciam.jpa.AccountScheme;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Data transfer object that represents a full set of attributes for an AIS consent.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Account consent")
public class CafConsentDto extends BaseDto {

  @JsonIgnore
  @Schema(description = "Customer external identifier")
  private String customerRef;

  @JsonIgnore
  @Schema(description = "TPP client application identifier")
  private String clientAppId;

  @JsonIgnore
  @Schema(description = "TPP client application name")
  private String clientAppName;

  @JsonIgnore
  @Schema(description = "TPP client application redirect URL")
  private String redirectUrl;

  @JsonIgnore
  @Schema(description = "OAuth2 authorization code")
  private String authCode;

  @JsonIgnore
  @Schema(description = "Refresh token related to the consent")
  private String refreshToken;

  @JsonIgnore
  @Schema(description = "Expiration date of the consent")
  private LocalDateTime expirationDateTime;

  @Schema(description = "The debtor account identification scheme", implementation = AccountScheme.class)
  private Account debtorAccount;

  @Getter
  @Setter
  @NoArgsConstructor
  @ToString
  @EqualsAndHashCode
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Account {

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The debtor account identifier", example = "******************************")
    private String identification;

    @JsonView({ Views.Mobile.class, Views.Public.class, Views.Mule.class, Views.Admin.class })
    @Schema(description = "The debtor account identification scheme", example = "JO.OB.IBAN")
    private String schemeName;
  }

}
