package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.service.auth.AuthenticationService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.representations.AccessTokenResponse;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * Controller responsible for handling authorization-related API endpoints. It provides methods to generate client tokens, create client
 * consents, and retrieve user tokens.
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Hidden
public class OAuthController {

    private final AuthenticationService authenticationService;
    private final KeycloakAdminService keycloakService;

    /**
     * Does token process based on received data:
     * <p>
     * 1. Retrieves the client token for a given set of client credentials.
     * 2. Refresh user token.
     * 3. Exchange authorization code to user token.
     *
     * @param tokenParams Post form urlencoded token request params.
     * @return ResponseEntity containing the client token.
     */
    @PostMapping(path = RestConstants.Paths.TOKEN,
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<AccessTokenResponse>> processToken(@RequestBody MultiValueMap<String, String> tokenParams) {
        CompletableFuture<AccessTokenResponse> tokenFuture;
        AuthorizationDto authorizationDto = AuthorizationDto.builder()
                .clientId(tokenParams.getFirst("client_id"))
                .clientSecret(tokenParams.getFirst("client_secret"))
                .scope(processScopes(tokenParams.getFirst("scope")))
                .authCode(tokenParams.getFirst("code"))
                .refreshToken(tokenParams.getFirst("refresh_token"))
                .redirectUri(tokenParams.getFirst("redirect_uri"))
                .build();

        String grantType = tokenParams.getFirst("grant_type");
        if ("refresh_token".equals(grantType)) {
            tokenFuture = authenticationService.refreshUserToken(authorizationDto);
        } else if ("authorization_code".equals(grantType)) {
            tokenFuture = authenticationService.getUserToken(authorizationDto);
        } else if ("client_credentials".equals(grantType)) {
            tokenFuture = authenticationService.getClientToken(authorizationDto);
        } else {
            throw new OAuthException(HttpStatus.BAD_REQUEST, "Incorrect grant type");
        }
        return tokenFuture.thenApply(response -> {
                    AccessTokenResponse safeResponse = AccessTokenResponse.from(response);
                    return ResponseEntity
                            .ok()
                            .body(safeResponse);
                })
                .exceptionally(throwable -> {
                    throw processException(throwable);
                });
    }

    @GetMapping(path = RestConstants.Paths.CERTS, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> processCerts() {
        try {
            return keycloakService.getCerts();
        } catch (Exception e) {
            throw processException(e);
        }
    }

    /**
     * Process the given exception and create an OAuthException.
     * If the cause of the exception is an ApplicationHttpStatusCodeException,
     * create an OAuthException with the status code and message of the cause.
     * Otherwise, create an OAuthException with a status code of HttpStatus.BAD_REQUEST
     * and the localized message of the throwable.
     *
     * @param throwable the exception to be processed
     * @return an OAuthException with the appropriate status code and message
     */
    private OAuthException processException(Throwable throwable) {

        log.error("Authentication error occurred: {}", throwable.getLocalizedMessage());

        if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
            ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
            return new OAuthException(statusCodeException.getStatusCode(), "statusCodeException.getMessage()");
        }
        return new OAuthException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
    }

    /**
     * Extracts the first scope from a given scopeString.
     *
     * @param scopeString The string containing multiple scopes separated by spaces.
     * @return The first scope in the scopeString, or null if the scopeString is empty or only contains one scope.
     */
    private String processScopes(String scopeString) {
        String[] scopes = StringUtils.split(scopeString, " ");
        if (scopes.length > 1) {
            throw new OAuthException(HttpStatus.BAD_REQUEST, "Only one scope allowed");
        }
        return scopeString;
    }
}
