package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * Client credentials used for authorization.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Client credentials")
public class AuthorizationDto {

  @NotNull(message = "Client identifier not specified")
  @Schema(description = "Client identifier")
  private String clientId;

  @NotNull(message = "Client secret not specified")
  @Schema(description = "Client secret")
  private String clientSecret;

  @Schema(description = "Requested scopes")
  private String scope;

  @Schema(description = "Authorization code")
  private String authCode;

  @Schema(description = "Refresh token")
  private String refreshToken;

  @Schema(description = "Redirect URI")
  private String redirectUri;
}
