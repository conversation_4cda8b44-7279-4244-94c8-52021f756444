package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SecurityConstants;
import com.cboj.ciam.service.auth.AuthenticationService;
import com.cboj.ciam.web.model.LoginModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping(RestConstants.Paths.MOBILE_COMMON)
@RequiredArgsConstructor
@Tag(name = "Mobile Backend API")
@SecurityRequirement(name = SecurityConstants.MOBILE_API_BEARER_TOKEN)
public class MobileController {

  private final AuthenticationService authenticationService;

  /**
   * This method is used to perform login for a user and create a consent.
   *
   * @param dto The data model for giving user credentials {@link MobileAuthRequestDto}
   * @return ResponseEntity with the authorization code
   * @throws UnauthorizedException if basic authorization is missing
   * @throws RuntimeException      if there is an error in the response body or cookie
   */
  @PostMapping(path = "/auth",
      consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(operationId = "LoginUser",
      summary = "Login mobile user.",
      description = "The operation is used to perform login for a mobile customer to proceed with the mobile-based consent flow.",
      responses = {
          @ApiResponse(responseCode = "201", description = "Successful customer login with an authentication code in the response")
      })
  @DefaultInternalErrorResponse
  public CompletableFuture<ResponseEntity<MobileAuthResponseDto>> login(
      @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Data model for giving user credentials", required = true)
      @RequestBody @Valid @NotNull MobileAuthRequestDto dto, HttpServletRequest request) {
    return authenticationService.mobileLogin(LoginModel.builder()
            .consentRef(dto.getConsentRef())
            .userName(dto.getUserName())
            .password(dto.getPassword())
            .passwordKey(dto.getKey())
            .redirect_uri(dto.getRedirectUrl())
            .client_id(dto.getClientId())
            .state(dto.getState())
            .scope(dto.getScope())
            .build(), request)
        .thenApply(authCode -> ResponseEntity.ok(new MobileAuthResponseDto(authCode)))
        .exceptionally(throwable -> {
          throw processException(throwable);
        });
  }

  private InternalApiException processException(Throwable throwable) {
    log.error(throwable.getLocalizedMessage(), throwable);
    if (throwable.getCause() instanceof ApplicationHttpStatusCodeException) {
      ApplicationHttpStatusCodeException statusCodeException = (ApplicationHttpStatusCodeException) throwable.getCause();
      return new InternalApiException(statusCodeException.getStatusCode(), statusCodeException.getMessage());
    }
    return new InternalApiException(HttpStatus.BAD_REQUEST, throwable.getLocalizedMessage());
  }
}
