package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "A single page of entries with information about other pages.")
@Builder
@JsonView(Views.Admin.class)
public class Page<T> implements Serializable {

  @Schema(description = "Entries within the page")
  private List<T> content;

  @Schema(description = "Total amount of entries")
  private Integer totalItems;

  @Schema(description = "Page number")
  private Integer page;

  @Schema(description = "Items displayed on one page")
  private Integer itemsPerPage;
}

