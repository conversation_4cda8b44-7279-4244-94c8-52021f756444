package com.cboj.ciam.api;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import org.springframework.http.HttpStatus;

/**
 * Represents an exception that is thrown when an internal API error occurs.
 * It encapsulates the HTTP status code and status text that are returned by the API.
 */
public class InternalApiException extends ApplicationHttpStatusCodeException {

  public InternalApiException(HttpStatus statusCode, String statusText) {
    super(statusCode, statusText);
  }
}
