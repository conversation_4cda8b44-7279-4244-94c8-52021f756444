package com.cboj.ciam.api;

import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.service.crypto.EncryptionService;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Hidden
public class CommonRestController {

  private final EncryptionService encryptionService;

  /**
   * Retrieves the RSA public keys for encrypting user data.
   *
   * @return the HTTP response entity containing the public keys
   */
  @GetMapping(path = RestConstants.Paths.WEB.PUBLIC_KEYS, produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<EncryptionService.KeyResponse> getPublicKey() {
    EncryptionService.KeyResponse key = encryptionService.loadPublicKeyResource();

    return key.isSuccess()
        ? ResponseEntity.ok(key)
        : ResponseEntity.status(HttpStatus.BAD_REQUEST).body(key);
  }

}
