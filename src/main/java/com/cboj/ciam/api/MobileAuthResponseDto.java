package com.cboj.ciam.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(description = "Customer login data for the mobile backend.")
public class MobileAuthResponseDto {

  @Schema(description = "OAuth2 authorization code", requiredMode = Schema.RequiredMode.REQUIRED, example = "44309383-0998-4332-a05d-f4a5c2353f4c")
  private String authCode;
}
