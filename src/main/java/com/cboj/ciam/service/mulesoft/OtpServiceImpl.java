package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.data.SessionService;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * Service implementation that provides functionality for sending and validating OTP (One-Time Password)
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.otp.mock", havingValue = "false")
public class OtpServiceImpl extends OtpServiceBase {

  private final String requestUri;
  private final String validateUri;
  private final String template;

  private final ObjectMapper mapper;

  protected OtpServiceImpl(RestTemplate restTemplate,
                           SessionService sessionService,
                           KeycloakClientService keycloakClientService,
                           MulesoftServiceProperties properties) {
    super(restTemplate, sessionService, keycloakClientService, properties);
    this.mapper = new ObjectMapper();
    this.template = properties.getOtpTemplate();
    this.requestUri = properties.getOtpRequestUrl();
    this.validateUri = properties.getOtpValidateUrl();
  }

  /**
   * Sends a request using the given mobile number and HTTP servlet request.
   *
   * @param mobileNumber The mobile number to send the request to.
   * @return The OTP reference number as a string.
   * @throws RuntimeException if the OTP process fails or if there is an exception in the OTP response.
   */
  @Override
  protected String sendRequest(String mobileNumber) {
    Map<String, Object> payload = new HashMap<>(2);
    payload.put(RestConstants.Otp.MOBILE_NUMBER, mobileNumber);
    payload.put(RestConstants.Otp.MESSAGE, template);

    log.debug("Sending OTP wit payload: {}", payload.get(RestConstants.Otp.MOBILE_NUMBER));

    try {
      ResponseEntity<String> responseEntity = processOtpRequest(payload, requestUri);
      OtpResponseDto response = mapper.readValue(responseEntity.getBody(), OtpResponseDto.class);
      return response.getResponse().getOtpReferenceNumber();
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
      throw new OtpRequestException("Otp request exception");
    }
  }

  /**
   * Validates the OTP (One-Time Password) for a given reference number.
   *
   * @param otp             the one-time password to validate
   * @return true if the OTP is valid and successfully validated, false otherwise
   * @throws RuntimeException if there is an error during the OTP validation process
   */
  @Override
  public boolean validateOtp(String otp, @NotNull HttpSession session) {
    String referenceNumber = (String) session.getAttribute(SessionConstants.REFERENCE_NUMBER_ATTR);

    if (StringUtils.isEmpty(referenceNumber)) {
      throw new OtpValidationException("Reference number is empty");
    }

    Map<String, Object> variables = new HashMap<>(2);
    variables.put(RestConstants.Otp.OTP_REFERENCE_NUMBER, referenceNumber);
    variables.put(RestConstants.Otp.OTP, otp);

    try {
      ResponseEntity<String> responseEntity = processOtpRequest(variables, validateUri);
      OtpResponseDto response = mapper.readValue(responseEntity.getBody(), OtpResponseDto.class);
      return response.getStatus().isSuccess();
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
      throw new OtpValidationException("Otp validation exception");
    }
  }

  /**
   * Processes the OTP request and sends it to the specified request URI.
   *
   * @param payload    the map of request variables
   * @param requestUri the request URI to send the OTP request to
   * @return the response entity containing the result of the otp request
   * @throws RuntimeException if there is an exception while sending the OTP request
   */
  private ResponseEntity<String> processOtpRequest(Map<String, Object> payload, String requestUri) {
    ResponseEntity<String> responseEntity = processRequest(requestUri, HttpMethod.POST, payload, null);
    if ((responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.CREATED)
        && !responseEntity.getStatusCode().equals(HttpStatus.OK))
        || responseEntity.getBody() == null) {
      log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
      throw new RuntimeException("Otp process failed");
    }
    return responseEntity;
  }
}
