package com.cboj.ciam.service.mulesoft.dto;

import lombok.Data;
import java.util.List;

@Data
public class DomesticAchResponse {
    private Status status;
    private Response response;

    @Data
    public static class Status {
        private boolean success;
        private String code;
        private String arabicMessage;
        private String englishMessage;
    }

    @Data
    public static class Response {
        private String ftReference;
        private String chargeAccountNumber;
        private String chargeCode;
        private String chargeType;
        private String commissionCode;
        private List<CommissionInfo> commissionInfo;
        private String creditAccountNumber;
        private String creditCurrency;
        private String creditedAmount;
        private String creditValueDate;
        private String customerCharged;
        private String debitAccountNumber;
        private String debitCurrency;
        private String debitCustomer;
        private String debitedAmount;
        private String debitValueDate;
        private String fixingRate;
        private String ibanChargesAccount;
        private String ibanDebit;
        private String localChargeAmount;
        private String localCreditedAmount;
        private String localDebitedAmount;
        private String localPosChargeAmount;
        private String processingDate;
        private String recieverBank;
        private String stpFlag;
        private String totalChargeAmount;
        private String totalRecieveChargeCurrency;
        private String totalSendChargeCurrency;
        private String transactionType;
        private String transferRef;
        private String limitExpiredFlag;
        private String limitExpiresBeforeTxnFlag;
        private String recordStatus;
    }

    @Data
    public static class CommissionInfo {
        private String commissionAmount;
        private String commissionFor;
        private String commissionType;
    }
}
