package com.cboj.ciam.service.mulesoft.pis;

import com.cboj.ciam.api.Views;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class InitiationDto {

  private String paymentType;
  private String paymentPurposeCode;
  private String paymentPurposeDesc;
  private String paymentReason;
  private String localInstrument;
  private String chargeType;
  private InstructedAmount instructedAmount;
  private Account creditorAccount;
  private Account debtorAccount;
  private BeneficiaryData beneficiaryData;

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class InstructedAmount {

    private Double amount;
    private String currency;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Account {

    private String identification;
    private String schemeName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class BeneficiaryData {

    private BeneficiaryName beneficiaryName;
    private Address beneficiaryAddress;
    private BeneficiaryAgent beneficiaryAgent;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
          ignoreUnknown = true
  )
  public static class BeneficiaryAgent {

    private Agent agentIdentification;

  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
          ignoreUnknown = true
  )
  public static class Agent {
    private String identification;
    private String schema;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Name {

    private String enName;
    private String arName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
          ignoreUnknown = true
  )
  public static class BeneficiaryName {

    private String firstName;
    private String middleName;
    private String lastName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class Address {

    private AddressLines addressLines;
    private String city;
    private String state;
    private String postcode;
    private CountryInfo countryInfo;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class AddressLines {

    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  public static class CountryInfo {

    private String countryCode;
    private String countryName;
  }
}
