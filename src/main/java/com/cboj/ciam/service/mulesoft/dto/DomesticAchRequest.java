package com.cboj.ciam.service.mulesoft.dto;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class DomesticAchRequest {
    private String coreAction;
    private String customerId;
    private DebitorDetails debitorDetails;
    private CreditorDetails creditorDetails;
    private ChargesDetails chargesDetails;
    private TransferDetails transferDetails;
    private BeneficiaryDetails beneficiaryDetails;
    private CustomerDetails customerDetails;
    private AdditionalDetails additionalDetails;

    @Builder
    @Data
    public static class DebitorDetails {
        private String valueDate;
        private String debitReference;
        private DebitorIdentification debitorIdentification;
    }

    @Builder
    @Data
    public static class DebitorIdentification {
        private String accountNumber;
    }

    @Builder
    @Data
    public static class CreditorDetails {
        private String valueDate;
        private String creditReference;
        private Amount amount;
    }

    @Builder
    @Data
    public static class Amount {
        private String amount;
        private String currency;
    }

    @Builder
    @Data
    public static class ChargesDetails {
        private String chargeType;
        private ChargesIdentification chargesIdentification;
    }

    @Builder
    @Data
    public static class ChargesIdentification {
        private String accountNumber;
    }

    @Builder
    @Data
    public static class TransferDetails {
        private String fxRate;
        private String paymentDetails;
        private String channelReference;
        private String mainPurposeCode;
        private String subPurposeCode;
        private String receiverPurposeCode;
        private String sendToReceiverCode;
        private String sendToReceiverInformation;
        private String relationshipSTR;
        private String relationshipSTROther;
    }

    @Builder
    @Data
    public static class BeneficiaryDetails {
        private String receiverBank;
        private String accountWithInstitution;
        private String intermediaryInstitutionBIC;
        private String beneficiaryAccountIBAN;
        private String beneficiaryName1;
        private String beneficiaryName2;
        private String beneficiaryAddress;
        private String beneficiaryCountry;
    }

    @Builder
    @Data
    public static class CustomerDetails {
        private String orderingCustomerAccount;
        private String orderingCustomer1;
        private String orderingCustomer2;
        private String orderingCustomer3;
        private String orderingCustomer4;
    }

    @Builder
    @Data
    public static class AdditionalDetails {
        private String profitCentreDepartment;
    }
}

