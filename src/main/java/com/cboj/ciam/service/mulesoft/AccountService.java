package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.CliqAccountDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoResponse;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.List;

/**
 * Interface defines the functionalities of an account service.
 */
public interface AccountService {

  List<ConsentAccountDto> getCustomerAccounts(String customerId);

  AccountDto getAccountDetails(String accountId);

  CliqAccountDto getCustomerAliasAccounts(String customerId, String aliasId, AccountScheme aliasType);
  TransactionInfoResponse validatePaymentAgainstCore(TransactionInfoRequest transactionInfoRequest) throws JsonProcessingException;

}
