package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.cboj.ciam.service.keycloak.KeycloakRequest;
import com.cboj.ciam.service.keycloak.representations.AccessTokenResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator;
import com.nimbusds.jose.util.JSONObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Base class for Mulesoft OTP service.
 */
@Slf4j
public abstract class MulesoftService {

  protected String MULE_RESPONSE_EX_TEMPLATE = "Response does not have body or failed with code {}";

  protected final RestTemplate restTemplate;

  private final TimeBasedEpochGenerator uuidGenerator;
  private final KeycloakClientService keycloakClientService;
  private final MulesoftServiceProperties properties;

  private final String clientId;
  private final String clientSecret;
  private final Integer retryAttemptsCount;

  private static final ObjectMapper objectMapper = new ObjectMapper();

  protected MulesoftService(RestTemplate restTemplate,
                            KeycloakClientService keycloakClientService,
                            MulesoftServiceProperties properties) {
    this.uuidGenerator = new TimeBasedEpochGenerator(new Random());
    this.restTemplate = restTemplate;
    this.keycloakClientService = keycloakClientService;
    this.clientId = properties.getClientId();
    this.clientSecret = properties.getClientSecret();
    this.retryAttemptsCount = properties.getRetryAttemptsCount();
    this.properties = properties;
  }

  public interface Headers {
    String CLIENT_ID = "client_id";
    String CLIENT_SECRET = "client_secret";
    String CHANNEL_ID = "x-channel-id";
    String CORRELATION_ID = "x-correlation-id";
    String BANK_ID = "x-bank-id";
  }

  /**
   * Process the HTTP request using the given request URI, HTTP method, payload, and request variables. This method performs the necessary
   * setup of headers and logs the request before making the actual HTTP call.
   *
   * @param requestUri       the URI of the HTTP request
   * @param method           the HTTP method of the request
   * @param payload          the payload of the HTTP request
   * @param requestVariables the variables for the request URI (if any)
   * @return a ResponseEntity containing the response body as a string
   */
  protected ResponseEntity<String> processRequest(String requestUri,
                                                  HttpMethod method,
                                                  @Nullable Map<String, Object> payload,
                                                  @Nullable Map<String, String> requestVariables) {
    log.info("Process request {} ", requestUri);

    CountDownLatch countDownLatch = new CountDownLatch(retryAttemptsCount);

    while (countDownLatch.getCount() > 0) {
      final MultiValueMap<String, String> headers = processHeaders();
      try {
        if (requestVariables == null) {
          ResponseEntity<String> res =  restTemplate.exchange(requestUri, method, new HttpEntity<>(payload, headers), String.class);
          return res;
        } else {
          return restTemplate.exchange(requestUri, method, new HttpEntity<>(payload, headers), String.class, requestVariables);
        }
      } catch (RestClientException e) {
        countDownLatch.countDown();

        log.warn("Attempt {} failed with RestClientException: {}", retryAttemptsCount - countDownLatch.getCount(), e.getLocalizedMessage());

        if (countDownLatch.getCount() == 0) {
          // Log an error message and return an empty list if all attempts have failed
          log.error("All {} attempts failed", retryAttemptsCount, e);
          return null;
        } else {
          // Add a delay between retries
          try {
            countDownLatch.await(2, TimeUnit.SECONDS);
          } catch (InterruptedException ie) {
            log.error("Attempt {} interrupted", retryAttemptsCount, ie);
          }
        }
      }
    }
    return null;
  }

  /**
   * Processes the headers for an HTTP request.
   *
   * @return the processed headers as a MultiValueMap
   */
  private MultiValueMap<String, String> processHeaders() {
    MultiValueMap<String, String> headers = createHeaders();
    String correlationId = getCorrelationHeader();
    headers.set(HttpHeaders.AUTHORIZATION, getToken());
    headers.set(Headers.CORRELATION_ID, correlationId);

    return headers;
  }

  /**
   * Creates a MultiValueMap containing the headers required for the request.
   *
   * @return The MultiValueMap containing the headers with the specified values.
   */
  private MultiValueMap<String, String> createHeaders() {
    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    headers.add(Headers.CHANNEL_ID, properties.getChannel());
    headers.add(Headers.BANK_ID, properties.getBank());
    headers.add(Headers.CLIENT_ID, clientId);
    headers.add(Headers.CLIENT_SECRET, clientSecret);
    return headers;
  }

  /**
   * Retrieves the access token required for authentication.
   *
   * @return The access token as a String.
   */
  private String getToken() {
    AccessTokenResponse response = keycloakClientService.getMuleClientToken(KeycloakRequest.builder()
        .clientId(clientId)
        .clientSecret(clientSecret)
        .build());

    return SsoConstants.TOKEN_TYPE_BEARER + " " + response.getToken();
  }

  /**
   * Retrieves the correlation header for the request.
   *
   * @return The generated correlation header as a String.
   */
  private String getCorrelationHeader() {
    return uuidGenerator.generate().toString();
  }

  /**
   * Processes the response body and converts it into a Map of key-value pairs.
   *
   * @param responseBody the response body in JSON format to be processed
   * @return Parsed response body
   */
  protected Map<String, Object> processResponseBody(String responseBody) {
    Map<String, Object> body;
    try {
      body = JSONObjectUtils.parse(responseBody);
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
      throw new RuntimeException(e);
    }

    if (body == null) {
      log.error("Response is missed");
    }

    return body;
  }

  public static Map<String, Object> processResponseBody(Object dto) {
    if (dto == null) {
      throw new IllegalArgumentException("DTO object cannot be null");
    }
    return objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {});
  }
}

