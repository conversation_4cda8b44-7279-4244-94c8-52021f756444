package com.cboj.ciam.service.mulesoft.dto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionInfoResponse {
    private FxRateDTO fxRate;
    private TransactionChargesDTO transactionCharges;
    private InitiationDTO initiation;
    private AmountDTO totalAmount;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FxRateDTO {
        private String rate;
        private String debitCurrency;
        private double debitAmount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionChargesDTO {
        private double amount;
        private String currency;
        private String chargeAccount;
        private String localInstrument;
        private String chargeType;
        private String recordStatus;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InitiationDTO {
        private String paymentPurposeCode;
        private AccountDTO creditorAccount;
        private String beneficiaryName;
        private AmountDTO instructedAmount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountDTO {
        private String identification;
        private String schemeName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AmountDTO {
        private double amount;
        private String currency;
    }
}
