package com.cboj.ciam.service.mulesoft.pis;

import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "true")
public class TransactionInfoServiceMock implements TransactionInfoService {

  private final ObjectMapper mapper;

  @Override
  public CompletableFuture<TransactionInfoDto> getTransactionInfo(@NotNull String consentRef, String accountRef) {
    return CompletableFuture.supplyAsync(() -> {
      try {
        Thread.sleep(5000);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
      if (accountRef.equals("4162173")) {
        //      return  null;
                    try {
                      return this.mapper.convertValue(mapper.readValue(getCharges(), Map.class), TransactionInfoDto.class);
                    } catch (JsonProcessingException e) {
                      throw new RuntimeException(e);
                    }
      }
      throw new UnsupportedOperationException("Not supported yet.");
    });
  }

  @Override
  public ExchangeAmountDto getExchangeRate(String fromCurrency, String toCurrency, double currencyAmount, String currency) {
    ExchangeAmountDto dto = new ExchangeAmountDto();
    return dto;

  }


  private String getCharges() {
    return "{\n"
        + "    \"fxRate\": {\n"
        + "        \"rate\": \"0.71\",\n"
        + "        \"debitCurrency\": \"USD\",\n"
        + "        \"debitAmount\": 141.24\n"
        + "    },\n"
        + "    \"transactionCharges\": {\n"
        + "        \"amount\": 4.23,\n"
        + "        \"currency\": \"USD\",\n"
        + "        \"chargeAccount\": \"4162173\",\n"
        + "        \"localInstrument\": \"DomesticAch\",\n"
        + "        \"chargeType\": \"sender\"\n"
        + "    },\n"
        + "    \"initiation\": {\n"
        + "        \"paymentPurposeCode\": \"0203\",\n"
        + "        \"creditorAccount\": {\n"
        + "            \"identification\": \"******************************\",\n"
        + "            \"schemeName\": \"JO.OB.IBAN\"\n"
        + "        },\n"
        + "        \"beneficiaryName\": \"Hamada Kamil Fihmi Mosbah\",\n"
        + "        \"instructedAmount\": {\n"
        + "            \"amount\": 100,\n"
        + "            \"currency\": \"JOD\"\n"
        + "        }\n"
        + "    },\n"
        + "    \"totalAmount\": {\n"
        + "        \"amount\": 145.47,\n"
        + "        \"currency\": \"USD\"\n"
        + "    }\n"
        + "}";
  }
}
