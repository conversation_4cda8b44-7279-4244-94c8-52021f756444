package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.data.SessionService;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * Class provides common functionality and abstract methods for OTP (One-Time Password) flow
 */
public abstract class OtpServiceBase extends MulesoftService implements OtpService {

  private final Integer requestOtpAttempts;
  private final Integer submitOtpAttempts;
  private final Integer otpSessionTime;
  private final Integer renewOtpSessionTime;

  private final SessionService sessionService;

  protected OtpServiceBase(RestTemplate restTemplate,
                           SessionService sessionService,
                           KeycloakClientService keycloakClientService,
                           MulesoftServiceProperties properties) {
    super(restTemplate, keycloakClientService, properties);
    this.sessionService = sessionService;
    this.requestOtpAttempts = properties.getRequestOtpAttempts();
    this.submitOtpAttempts = properties.getSubmitOtpAttempts();
    this.otpSessionTime = properties.getOtpSessionTime();
    this.renewOtpSessionTime = properties.getRenewOtpSessionTime();
  }

  /**
   * Sends a request using the given mobile number and HTTP servlet request.
   *
   * @param mobileNumber The mobile number to send the request to.
   * @return The response from the request as a string.
   */
  protected abstract String sendRequest(String mobileNumber);

  /**
   * Validates the given reference number and OTP (One-Time Password).
   *
   * @param otp     The OTP to validate.
   * @param session The HTTP session.
   * @return true if the reference number and OTP are valid, false otherwise.
   */
  public abstract boolean validateOtp(String otp, HttpSession session);

  @Override
  public boolean validate(String otp, HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    boolean valid = validateOtp(otp, session);
    if (!valid) {
      checkSubmitAttempts(session);
    }
    return valid;
  }

  /**
   * Sends a request using the given mobile number and HTTP servlet request.
   *
   * @param mobileNumber The mobile number to send the request to.
   * @param request      The HTTP servlet request object.
   */
  @Override
  public HttpSession send(String mobileNumber, HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    if (session == null) {
      throw new UnauthorizedException("Session not found");
    }

    // Checking if the request can be re-requested to avoid requests each Log in time
    Long renewTime = (Long) session.getAttribute(SessionConstants.OTP_SESSION_RENEW_TIME);
    Integer submitAttempts = (Integer) session.getAttribute(SessionConstants.SUBMIT_ATTEMPT);
    if (renewTime != null
        && renewTime > LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        && submitAttempts == 0) {
      return session;
    }

    checkRequestAttempts(session);
    String referenceNumber = sendRequest(mobileNumber);
    fillSessionAttrs(referenceNumber, session);
    return session;
  }

  /**
   * Sets the OTP session timers in the HttpSession object.
   *
   * @param referenceNumber OTP reference number.
   * @param session         The HTTP session.
   */
  public void fillSessionAttrs(String referenceNumber, @NotNull HttpSession session) {
    LocalDateTime now = LocalDateTime.now();
    if (session.getAttribute(SessionConstants.OTP_SESSION_EXPIRATION_TIME) == null) {
      session.setAttribute(SessionConstants.OTP_SESSION_EXPIRATION_TIME, now.plusMinutes(otpSessionTime)
          .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }
    session.setAttribute(SessionConstants.OTP_SESSION_RENEW_TIME, now.plusMinutes(renewOtpSessionTime)
        .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    session.setAttribute(SessionConstants.REFERENCE_NUMBER_ATTR, referenceNumber);
  }

  /**
   * Method to check the number of maximum OTP request attempts and take appropriate action based on the attempts count.
   *
   * @param session The HTTP session.
   */
  protected void checkRequestAttempts(@NotNull HttpSession session) {
    Integer attempt = (Integer) session.getAttribute(SessionConstants.REQUEST_ATTEMPT);

    if (attempt == null) {
      attempt = requestOtpAttempts;
    } else if (attempt == 0) {
      sessionService.deleteSession(session);
      throw new OtpRequestException("Max request OTP attempts");
    }

    session.setAttribute(SessionConstants.SUBMIT_ATTEMPT, submitOtpAttempts - 1);
    session.setAttribute(SessionConstants.REQUEST_ATTEMPT, attempt - 1);
  }

  /**
   * Method to check the number of OTP submit attempts and take appropriate action based on the attempts count.
   *
   * @param session The HTTP session.
   */
  protected void checkSubmitAttempts(@NotNull HttpSession session) {
    Integer attempt = (Integer) session.getAttribute(SessionConstants.SUBMIT_ATTEMPT);

    if (attempt == 0) {
      Integer requestAttempts = (Integer) session.getAttribute(SessionConstants.REQUEST_ATTEMPT);
      if (requestAttempts == 0) {
        throw new OtpRequestException("Max request OTP attempts");
      }
      throw new OtpSendingException("Max submit OTP attempts");
    }

    session.setAttribute(SessionConstants.SUBMIT_ATTEMPT, --attempt);
  }
}
