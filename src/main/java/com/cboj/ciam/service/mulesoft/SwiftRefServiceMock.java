package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.api.pis.PisConsentDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "true")
public class SwiftRefServiceMock implements SwiftRefService {

  @Override
  public boolean validateIban(PisConsentDto pisConsentDto) {
    return true;
  }

  @Override
  public String getBicCode(String iban) {
    return "BICCODEXXXX";
  }
}
