package com.cboj.ciam.service.mulesoft.pis;

import com.cboj.ciam.jpa.pis.PaymentCurrency;

import javax.validation.constraints.NotNull;
import java.util.concurrent.CompletableFuture;

/**
 * Interface defines the functionalities of transaction information.
 */
public interface TransactionInfoService {

  CompletableFuture<TransactionInfoDto> getTransactionInfo(@NotNull String customerId, String accountRef);

  ExchangeAmountDto getExchangeRate(String fromCurrency, String toCurrency, double currencyAmount, String amountCurrency);
}
