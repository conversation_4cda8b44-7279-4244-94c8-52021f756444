package com.cboj.ciam.service.mulesoft;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "true")
public class EligibilityServiceMock implements EligibilityService {

  @Override
  public boolean checkCustomerEligibility(String customerId) {
    return true;
  }
}
