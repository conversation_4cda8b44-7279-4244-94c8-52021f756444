package com.cboj.ciam.service.mulesoft.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TransactionInfoRequest {
    private InitiationDTO initiation;
    private String customerRef;

    @Data
    @Builder
    public static class InitiationDTO {
        private String paymentType;
        private String paymentPurposeCode;
        private String paymentPurposeDesc;
        private String paymentReason;
        private String localInstrument;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String chargeType;
        private InstructedAmountDTO instructedAmount;
        private AccountDTO creditorAccount;
        private AccountDTO debtorAccount;
        private BeneficiaryDataDTO beneficiaryData;
    }

    @Data
    @Builder
    public static class InstructedAmountDTO {
        private double amount;
        private String currency;
    }

    @Data
    @Builder
    public static class AccountDTO {
        private String identification;
        private String schemeName;
    }

    @Data
    @Builder
    public static class BeneficiaryDataDTO {
        private BeneficiaryNameDTO beneficiaryName;
        private BeneficiaryAddressDTO beneficiaryAddress;
        private BeneficiaryAgentDTO beneficiaryAgent;
    }

    @Data
    @Builder
    public static class BeneficiaryNameDTO {
        private String firstName;
        private String middleName;
        private String lastName;
    }

    @Data
    @Builder
    public static class BeneficiaryAddressDTO {
        private String city;
        private String state;
        private String postcode;
        private CountryInfoDTO countryInfo;
    }

    @Data
    @Builder
    public static class CountryInfoDTO {
        private String countryCode;
        private String countryName;
    }

    @Data
    @Builder
    public static class BeneficiaryAgentDTO {
        private AgentIdentificationDTO agentIdentification;
    }

    @Data
    @Builder
    public static class AgentIdentificationDTO {
        private String identification;
        private String schema;
    }
}
