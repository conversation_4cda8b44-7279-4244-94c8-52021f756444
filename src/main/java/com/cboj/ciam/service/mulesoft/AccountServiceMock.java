package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.CliqAccountDto;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.config.CacheConfig;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.service.mulesoft.dto.DomesticAchResponse;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "true")
public class AccountServiceMock implements AccountService {

  private final ObjectMapper mapper;

  @Cacheable(value = CacheConfig.ACCOUNTS_CACHE_NAME, key = "#customerId")
  @SuppressWarnings("unchecked")
  @Override
  public List<ConsentAccountDto> getCustomerAccounts(String customerId) {
    try {
      return (List) mapper.readValue(getAccounts(), List.class).stream()
          .map((account) -> this.mapper.convertValue(account, ConsentAccountDto.class)).collect(Collectors.toList());
    } catch (JsonProcessingException e) {
      return Collections.emptyList();
    }
  }

  @Override
  public AccountDto getAccountDetails(String accountId) {
    return AccountDto.builder()
        .accountRef("4162173")
        .accountType("CURRENT")
        .ibanNumber("******************************")
        .categoryDescription("Regular-Current")
        .category("1102")
        .currency("JOD")
        .build();
  }

  @Override
  public CliqAccountDto getCustomerAliasAccounts(String customerId, String aliasId, AccountScheme accountScheme) {
    return CliqAccountDto.builder()
        .customerType("Individual")
        .type("DFLT")
        .currency("JOD")
        .iban("******************************")
        .bic("BICCODEXXX")
        .build();
  }

  @Override
  public TransactionInfoResponse validatePaymentAgainstCore(TransactionInfoRequest transactionInfoRequest) throws JsonProcessingException {
    return null;
  }


  private String getAccounts() {
    return "[\n" +
        "  {\n" +
        "    \"accountRef\": \"4162173\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Margin - MOHAMMAD MOUSA TAWFIQ ALQR AINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4000919-JOD\",\n" +
        "    \"listViewAr\": \"4000919-JOD\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4000921\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4000921-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4000921-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4000924\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"USD\",\n" +
        "    \"listViewEn\": \"4000924-USD-Current Account\",\n" +
        "    \"listViewAr\": \"4000924-USD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4146606\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Margin - MOHAMMAD MOUSA TAWFIQ ALQR AINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4146606-JOD\",\n" +
        "    \"listViewAr\": \"4146606-JOD\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4146607\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Margin - MOHAMMAD MOUSA TAWFIQ ALQR AINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4146607-JOD\",\n" +
        "    \"listViewAr\": \"4146607-JOD\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4166384\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Margin - MOHAMMAD MOUSA TAWFIQ ALQR AINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4166384-JOD\",\n" +
        "    \"listViewAr\": \"4166384-JOD\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4166386\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Margin - MOHAMMAD MOUSA TAWFIQ ALQR AINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4166386-JOD\",\n" +
        "    \"listViewAr\": \"4166386-JOD\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501097\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501097-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501097-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501098\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"USD\",\n" +
        "    \"listViewEn\": \"4501098-USD-Current Account\",\n" +
        "    \"listViewAr\": \"4501098-USD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501099\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"GBP\",\n" +
        "    \"listViewEn\": \"4501099-GBP-Current Account\",\n" +
        "    \"listViewAr\": \"4501099-GBP-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501100\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"EUR\",\n" +
        "    \"listViewEn\": \"4501100-EUR-Current Account\",\n" +
        "    \"listViewAr\": \"4501100-EUR-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501101\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"MOHAMMAD MOUSA TAWFIQ ALQRAINI\",\n" +
        "    \"titleAr\": \"محمد موسى توفيق القريني\",\n" +
        "    \"currency\": \"CHF\",\n" +
        "    \"listViewEn\": \"4501101-CHF-Current Account\",\n" +
        "    \"listViewAr\": \"4501101-CHF-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501219\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501219-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501219-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501220\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501220-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501220-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501221\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501221-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501221-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501242\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501242-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501242-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501267\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501267-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501267-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4501268\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Name-******** Name-********\",\n" +
        "    \"titleAr\": \"Name-******** Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4501268-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4501268-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4502876\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4502876-JOD-Gold Road Saving NIB\",\n" +
        "    \"listViewAr\": \"4502876-JOD-حساب طريقك دهب بدون فائدة\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503014\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503014-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503014-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503015\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503015-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503015-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503016\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503016-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503016-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503019\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503019-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503019-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503020\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"AED\",\n" +
        "    \"listViewEn\": \"4503020-AED-Current Account\",\n" +
        "    \"listViewAr\": \"4503020-AED-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503021\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503021-JOD-Premium Saving IB Account\",\n" +
        "    \"listViewAr\": \"4503021-JOD-حساب التوفير مع فائدة\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503309\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503309-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503309-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503310\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503310-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503310-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503311\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503311-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503311-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503317\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4503317-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4503317-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503356\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"JOD-Basic Account\",\n" +
        "    \"listViewAr\": \"JOD-حساب أساسي\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503437\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"JOD-Basic Account\",\n" +
        "    \"listViewAr\": \"JOD-حساب أساسي\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4503438\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"JOD-Basic Account\",\n" +
        "    \"listViewAr\": \"JOD-حساب أساسي\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4510385\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4510385-JOD-Gold Road Saving Private Banking\",\n" +
        "    \"listViewAr\": \"4510385-JOD-حساب طريقك دهب\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4510387\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"TESTSEVEN Name-********\",\n" +
        "    \"titleAr\": \"TESTSEVEN Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4510387-JOD-Gold Road Saving Private Banking\",\n" +
        "    \"listViewAr\": \"4510387-JOD-حساب طريقك دهب\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4700338\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Full Name English Name-********\",\n" +
        "    \"titleAr\": \"Full Name Arabic Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4700338-JOD-Current Account\",\n" +
        "    \"listViewAr\": \"4700338-JOD-حساب جاري\",\n" +
        "    \"selected\": false\n" +
        "  },\n" +
        "  {\n" +
        "    \"accountRef\": \"4700535\",\n" +
        "    \"ibanNumber\": \"******************************\",\n" +
        "    \"titleEn\": \"Full Name English Name-********\",\n" +
        "    \"titleAr\": \"Full Name Arabic Name-********\",\n" +
        "    \"currency\": \"JOD\",\n" +
        "    \"listViewEn\": \"4700535-JOD-Salary Bonus Account\",\n" +
        "    \"listViewAr\": \"4700535-JOD-حساب مكافأة الراتب\",\n" +
        "    \"selected\": false\n" +
        "  }\n" +
        "]";
  }
}
