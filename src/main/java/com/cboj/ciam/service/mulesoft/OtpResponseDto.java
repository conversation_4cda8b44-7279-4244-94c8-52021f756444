package com.cboj.ciam.service.mulesoft;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class OtpResponseDto implements Serializable {

  private Status status;
  private Response response;

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Status {
    String code;
    String arabicMessage;
    String englishMessage;
    private boolean success;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Response {
    private String otpReferenceNumber;
  }
}
