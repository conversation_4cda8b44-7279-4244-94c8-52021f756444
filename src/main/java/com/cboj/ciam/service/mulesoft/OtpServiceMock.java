package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.service.data.SessionService;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpSession;

@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.otp.mock", havingValue = "true")
public class OtpServiceMock extends OtpServiceBase {

  protected OtpServiceMock(RestTemplate restTemplate,
                           SessionService sessionService,
                           KeycloakClientService keycloakClientService,
                           MulesoftServiceProperties properties) {
    super(restTemplate, sessionService, keycloakClientService, properties);
  }

  @Override
  public boolean validateOtp(String otp, HttpSession session) {
    return true;
  }

  @Override
  protected String sendRequest(String mobileNumber) {
    return "143536678";
  }
}
