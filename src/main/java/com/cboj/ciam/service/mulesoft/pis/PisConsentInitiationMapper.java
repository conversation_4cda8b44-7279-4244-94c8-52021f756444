package com.cboj.ciam.service.mulesoft.pis;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import org.springframework.stereotype.Component;


@Component
public class PisConsentInitiationMapper implements Mapper<PisConsentEntity, InitiationDto> {

  @Override
  public InitiationDto apply(PisConsentEntity entity) {
    InitiationDto initiationDto = new InitiationDto();
    initiationDto.setPaymentType(entity.getPaymentType().getValue());
    initiationDto.setLocalInstrument(entity.getLocalInstrument().getValue());
    if (entity.getPaymentPurposeCode() != null) {
      initiationDto.setPaymentPurposeCode(entity.getPaymentPurposeCode());
    }
    if (entity.getPaymentPurposeCode() != null) {
      initiationDto.setPaymentReason(entity.getPaymentReason());
    }
    if (entity.getChargeType() != null) {
      initiationDto.setChargeType(entity.getChargeType());
    }

    InitiationDto.InstructedAmount instructedAmount = new InitiationDto.InstructedAmount();
    instructedAmount.setAmount(entity.getAmount());
    instructedAmount.setCurrency(entity.getCurrency().name());
    initiationDto.setInstructedAmount(instructedAmount);


    InitiationDto.Account crerditorAccount = new InitiationDto.Account();
    crerditorAccount.setIdentification(entity.getCreditorAccRef());
    crerditorAccount.setSchemeName(entity.getCreditorAccScheme().getValue());
    initiationDto.setCreditorAccount(crerditorAccount);

    InitiationDto.Account debtorAccount = new InitiationDto.Account();

    String debtorSchemeName = entity.getDebtorAccScheme() != null
        ? entity.getDebtorAccScheme().getValue()
        : AccountScheme.ACCOUNT_NUMBER.getValue();

    if (entity.getDebtorAccRef() != null) {
      debtorAccount.setIdentification(entity.getDebtorAccRef());
    }
    debtorAccount.setSchemeName(debtorSchemeName);
    initiationDto.setDebtorAccount(debtorAccount);

    InitiationDto.BeneficiaryData beneficiaryData = processBeneficiaryData(entity);
    if (beneficiaryData != null) {
      initiationDto.setBeneficiaryData(beneficiaryData);
    }

    initiationDto.setPaymentPurposeDesc(entity.getPaymentPurposeDesc());
    return initiationDto;
  }

  /**
   * Processes the beneficiary data from the PisConsent entity.
   *
   * @param entity The PisConsent entity from which to process the beneficiary data.
   * @return The processed BeneficiaryData object.
   */
  private InitiationDto.BeneficiaryData processBeneficiaryData(PisConsentEntity entity) {

    InitiationDto.Name name = processName(entity);
    InitiationDto.Address address = processAddress(entity);
    InitiationDto.BeneficiaryAgent beneficiaryAgent = processBeneficiaryAgent(entity);


    InitiationDto.BeneficiaryData beneficiaryData = new InitiationDto.BeneficiaryData();
    InitiationDto.BeneficiaryName beneficiaryName = new InitiationDto.BeneficiaryName();
    beneficiaryName.setFirstName(entity.getBeneficiaryFirstName());
    beneficiaryName.setMiddleName(entity.getBeneficiaryMiddleName());
    beneficiaryName.setLastName(entity.getBeneficiaryLastName());

    beneficiaryData.setBeneficiaryName(beneficiaryName);

    if (address != null) {
      beneficiaryData = beneficiaryData != null ? beneficiaryData : new InitiationDto.BeneficiaryData();
      beneficiaryData.setBeneficiaryAddress(address);
    }
    if( beneficiaryAgent!= null ) {
      beneficiaryData = beneficiaryData != null ? beneficiaryData : new InitiationDto.BeneficiaryData();
      beneficiaryData.setBeneficiaryAgent(beneficiaryAgent);
    }
    return beneficiaryData;
  }

  private InitiationDto.BeneficiaryAgent processBeneficiaryAgent(PisConsentEntity entity) {
    InitiationDto.BeneficiaryAgent beneficiaryAgent = null;
    if (entity.getAgentIdentification() != null && entity.getAgentSchema() != null) {
      beneficiaryAgent = new InitiationDto.BeneficiaryAgent();
      InitiationDto.Agent agent = new InitiationDto.Agent();
      agent.setIdentification(entity.getAgentIdentification());
      agent.setSchema(entity.getAgentSchema());
      beneficiaryAgent.setAgentIdentification(agent);
    }
    return beneficiaryAgent;
  }

  /**
   * Processes the name information of a PisConsent entity and returns an InitiationDto.Name object.
   *
   * @param entity the PisConsent entity containing the creditor name information
   * @return an InitiationDto.Name object with the creditor name information, or null if the entity does not have a creditor name
   */
  private InitiationDto.Name processName(PisConsentEntity entity) {
    InitiationDto.Name name = null;
    if (entity.getCreditorNameAr() != null || entity.getCreditorNameEn() != null) {
      name = new InitiationDto.Name();
      name.setArName(entity.getCreditorNameAr());
      name.setEnName(entity.getCreditorNameEn());
    }
    return name;
  }

  /**
   * Processes the address information from the PisConsent entity and returns an InitiationDto.Address object.
   *
   * @param entity The PisConsent entity from which to process the address information.
   * @return The processed InitiationDto.Address object, or null if no address information is available.
   */
  private InitiationDto.Address processAddress(PisConsentEntity entity) {
    InitiationDto.Address address = null;
    InitiationDto.AddressLines addressLines = null;
    InitiationDto.CountryInfo countryInfo = null;

    if (entity.getCreditorAddressLine1() != null
        || entity.getCreditorAddressLine2() != null
        || entity.getCreditorAddressLine3() != null) {
      addressLines = new InitiationDto.AddressLines();
      addressLines.setAddressLine1(entity.getCreditorAddressLine1());
      addressLines.setAddressLine2(entity.getCreditorAddressLine2());
      addressLines.setAddressLine3(entity.getCreditorAddressLine3());
    }

    if (entity.getCreditorCountryCode() != null || entity.getCreditorCountryName() != null) {
      countryInfo = new InitiationDto.CountryInfo();
      countryInfo.setCountryCode(entity.getCreditorCountryCode());
      countryInfo.setCountryName(entity.getCreditorCountryName());
    }

    if (addressLines != null || countryInfo != null
        || entity.getCreditorCity() != null
        || entity.getCreditorState() != null
        || entity.getCreditorPostcode() != null) {
      address = new InitiationDto.Address();
      if (addressLines != null) {
        address.setAddressLines(addressLines);
      }
      if (countryInfo != null) {
        address.setCountryInfo(countryInfo);
      }
      address.setCity(entity.getCreditorCity());
      address.setState(entity.getCreditorState());
      address.setPostcode(entity.getCreditorPostcode());
    }
    return address;
  }


}
