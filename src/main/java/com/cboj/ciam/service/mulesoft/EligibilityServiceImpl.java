package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Service implementation for checking customer eligibility.
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "false")
public class EligibilityServiceImpl extends MulesoftService implements EligibilityService {

  private final String eligibilityUrl;
  private final String idParam;
  private final String eligibilityPart;

  public EligibilityServiceImpl(MulesoftServiceProperties properties,
                                KeycloakClientService keycloakClientService,
                                RestTemplate restTemplate) {
    super(restTemplate, keycloakClientService, properties);
    this.eligibilityUrl = properties.getEligibilityUrl();
    this.idParam = properties.getIdParameter();
    this.eligibilityPart = properties.getEligibilityPart();
  }

  /**
   * Checks the eligibility of a customer.
   *
   * @param customerId The ID of the customer.
   * @return true if the customer is eligible, false otherwise.
   */
  @Override
  public boolean checkCustomerEligibility(String customerId) {
    Map<String, String> variables = new HashMap<>(1);
    variables.put(idParam, customerId);

    String accountUri = eligibilityUrl +
        "/{" + idParam + "}" + "/" + eligibilityPart;

    ResponseEntity<String> responseEntity = processRequest(accountUri, HttpMethod.GET, null, variables);

    if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
      log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
      return false;
    }

    return processEligibility(responseEntity.getBody());
  }

  /**
   * Process the eligibility response and determine if the customer is eligible.
   *
   * @param responseBody The response body containing eligibility information.
   * @return true if the customer is eligible, false otherwise.
   */
  private boolean processEligibility(String responseBody) {
    Map<String, Object> body = processResponseBody(responseBody);

    if (body == null) {
      return false;
    }

    String eligibility = (String) body.get(RestConstants.EligibilityResponse.ELIGIBILITY);

    return StringUtils.isNotEmpty(eligibility) && eligibility.equals("Yes");
  }
}
