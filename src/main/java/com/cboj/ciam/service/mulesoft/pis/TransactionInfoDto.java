package com.cboj.ciam.service.mulesoft.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class TransactionInfoDto implements Serializable {

  private FxRate fxRate;
  private TransactionCharges transactionCharges;
  private Initiation initiation;
  private Amount totalAmount;

  @Getter
  @Setter
  @NoArgsConstructor
  public static class FxRate {
    private String rate;
    private String debitCurrency;
    private Double debitAmount;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class TransactionCharges {
    private String chargeAccount;
    private String localInstrument;
    private String currency;
    private String chargeType;
    private Double amount;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Initiation {
    private String paymentPurposeCode;
    private String paymentPurposeDesc;
    private String beneficiaryName;
    private CreditorAccount creditorAccount;
    private Amount instructedAmount;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class CreditorAccount {
    private String identification;
    private String schemeName;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Amount {
    private Double amount;
    private String currency;
  }
}
