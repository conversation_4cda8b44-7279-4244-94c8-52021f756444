package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.CliqAccountDto;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.config.CacheConfig;
import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.ConsentAccountType;
import com.cboj.ciam.service.data.Storage;
import com.cboj.ciam.service.data.ConsentAccountTypeStorage;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.cboj.ciam.service.mulesoft.dto.DomesticAchResponse;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;

import com.cboj.ciam.service.mulesoft.dto.TransactionInfoResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.ast.Not;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service implementation for retrieving customer accounts.
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "false")
public class AccountServiceImpl extends MulesoftService implements AccountService {

    private final String accountUrl;
    private final String accountsInfoUrl;
    private final String customerAccountsUrl;

    private final String accountIdParam;
    private final String customerIdParam;
    private final String aliasIdParam;
    private final String aliasTypeParam;
    private final String accountPart;
    private final String activeStatus;
    private final Map<String, ConsentAccountType> accountTypes = new HashMap<>();
    private final Storage<ConsentAccountType> accountTypeStorage;
    private final String transactionInfoUrl;

    public AccountServiceImpl(ConsentAccountTypeStorage accountTypeStorage,
                              KeycloakClientService keycloakClientService,
                              MulesoftServiceProperties properties,
                              RestTemplate restTemplate) {
        super(restTemplate, keycloakClientService, properties);
        this.accountTypeStorage = accountTypeStorage;
        this.accountUrl = properties.getAccountsUrl();
        this.accountIdParam = properties.getIdParameter();
        this.accountPart = properties.getAccountsPart();
        this.customerIdParam = properties.getCustomerIdParameter();
        this.aliasIdParam = properties.getAliasIdParameter();
        this.aliasTypeParam = properties.getAliasTypeParameter();
        this.activeStatus = properties.getAccountStatus();
        this.accountsInfoUrl = properties.getAccountsInfoUrl();
        this.customerAccountsUrl = properties.getCustomerAccountsUrl();
        this.transactionInfoUrl = properties.getTransactionInfoUrl();
    }

    @PostConstruct
    public void init() {
        accountTypeStorage.search(null)
                .thenAccept(types ->
                        types.forEach(a -> accountTypes.put(a.getAccountClass(), a)));
    }

    enum AccountLocale {
        EN,
        AR
    }

    /**
     * Retrieves the customer accounts from the server.
     *
     * @param customerId the ID of the customer
     * @return a list of AisConsentAccountDto objects representing the customer accounts, or an empty list if the request fails or if there is
     * no response body
     */
    @Cacheable(value = CacheConfig.ACCOUNTS_CACHE_NAME, key = "#customerId")
    @Override
    public List<ConsentAccountDto> getCustomerAccounts(String customerId) {
        Map<String, String> variables = new HashMap<>(1);
        variables.put(accountIdParam, customerId);

        String accountUri = accountUrl +
                "/{" + accountIdParam + "}" + "/" + accountPart;

        ResponseEntity<String> responseEntity = processRequest(accountUri, HttpMethod.GET, null, variables);

        if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
            log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
            return Collections.emptyList();
        }

        return processAccounts(responseEntity.getBody());
    }

    /**
     * Retrieves the account details from the server.
     *
     * @param accountId the ID of the customer
     * @return object of AccountDto objects with currency , category of the account no response body
     */
    @Cacheable(value = CacheConfig.SINGLE_ACCOUNT_CACHE_NAME, key = "#accountId")
    @Override
    public AccountDto getAccountDetails(String accountId) {

        String accountUri = accountsInfoUrl + "/" + accountId;

        log.info("getAccountDetailsProcess request {} ", accountUri);
        ResponseEntity<String> responseEntity = processRequest(accountUri, HttpMethod.GET, null, null);

        if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
            log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
            return null;
        }

        return processAccountsInfo(responseEntity.getBody());
    }

    /**
     * Retrieves customer accounts based on customer ID and alias ID.
     *
     * @param customerId The unique identifier of the customer.
     * @param aliasId    The alias identifier associated with the customer.
     * @return CustomerAccountDto object containing the retrieved customer accounts.
     */
    @Override
    public CliqAccountDto getCustomerAliasAccounts(String customerId, String aliasId, AccountScheme accountScheme) {
        Map<String, String> variables = new HashMap<>();
        variables.put(customerIdParam, customerId);
        variables.put(aliasIdParam, aliasId);

        String aliasType = accountScheme.equals(AccountScheme.ALIAS) ? AppConstants.AliasTypes.ALIAS : AppConstants.AliasTypes.MOBL;

        String url = customerAccountsUrl + "?" + aliasTypeParam + "=" + aliasType;

        ResponseEntity<String> responseEntity = processRequest(url, HttpMethod.GET, null, variables);

        if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
            log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
            return null;
        }

        return processCustomerAccounts(responseEntity.getBody());
    }

    @SuppressWarnings("unchecked")
    private AccountDto processAccountsInfo(String responseBody) {
        Map<String, Object> body = processResponseBody(responseBody);

        if (body == null) {
            return null;
        }

        Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.AccountResponse.RESPONSE);

        if (response == null || response.get(RestConstants.AccountResponse.ACCOUNTS_BLOCK) == null) {
            log.error("Response is missed or [accounts] block is empty");
            throw new NotFoundException("Failed to get account info");
        }

        Map<String, Object> account = (Map<String, Object>) response.get(RestConstants.AccountResponse.ACCOUNTS_BLOCK);
        Map<String, Object> customers = (Map<String, Object>) response.get(RestConstants.AccountResponse.CUSTOMERS_BLOCK);

        //todo: Hussein: this is not correct way to wrap a rest responses. it should be wrapped by class using json converter
        String currency = (String) account.get(RestConstants.AccountResponse.CURRENCY);
        String category = (String) account.get(RestConstants.AccountResponse.CATEGORY);
        Map<String, Object> accountHolderNameObj = (Map<String, Object>) account.get(RestConstants.AccountResponse.ACCOUNT_HOLDER_NAME);
        String accountHolderNameEn = (String) accountHolderNameObj.get(RestConstants.AccountResponse.EN);
        String accountHolderNameAr = (String) accountHolderNameObj.get(RestConstants.AccountResponse.AR);

        if (StringUtils.isEmpty(currency) || StringUtils.isEmpty(category)) {
            log.error("Currency or category missing for this account");
            throw new NotFoundException("Missing Account Details");
        }

        return AccountDto.builder()
                .currency(currency)
                .category(category)
                .accountRef((String) account.get(RestConstants.AccountResponse.ACCOUNT_ID))
                .categoryDescription((String) account.get(RestConstants.AccountResponse.CATEGORY_DESCRIPTION))
                .ibanNumber((String) account.get(RestConstants.AccountResponse.ACCOUNT_IBAN))
                .accountType((String) account.get(RestConstants.AccountResponse.ACCOUNT_TYPE))
                .customerId((String) customers.get(RestConstants.AccountResponse.CUSTOMER_ID))
                .accountHolderNameEn(accountHolderNameEn)
                .accountHolderNameAr(accountHolderNameAr)
                .build();

    }

    /**
     * Processes the customer accounts data retrieved from the API response.
     *
     * @param responseBody The response body received from the API call.
     * @return CustomerAccountDto containing the details of the customer account.
     */
    @SuppressWarnings("unchecked")
    private CliqAccountDto processCustomerAccounts(String responseBody) {
        Map<String, Object> body = processResponseBody(responseBody);

        if (body == null) {
            return null;
        }

        Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.AccountResponse.RESPONSE);

        if (response == null) {
            log.error("Response is missed");
            throw new NotFoundException("Missing Account Details");
        }

        Map<String, Object> additionalDetails
                = (Map<String, Object>) response.get(RestConstants.CustomerAliasAccountsResponse.ADDITIONAL_DETAILS);
        if (additionalDetails == null) {
            throw new NotFoundException("Additional details not found");
        }

        String customerType = (String) additionalDetails.get(RestConstants.CustomerAliasAccountsResponse.CUSTOMER_TYPE);
        String type = (String) response.get(RestConstants.CustomerAliasAccountsResponse.TYPE);
        String biCode = (String) response.get("bic");
        String iban = (String) response.get("iban");
        Map<String, Object> enBlock = (Map<String, Object>) additionalDetails.get("en");
        String firstName = (String) enBlock.get("firstName");
//        String secondName = (String) enBlock.get("secondName");
//        String thirdName = (String) enBlock.get("thirdName");
//        String lastName = (String) enBlock.get("lastName");

        Map<String, Object> addressBlock = (Map<String, Object>) response.get("address");
//        String city = (String) addressBlock.get("city");
//        String country = (String) addressBlock.get("country");
//        String stateProvinceRegion = (String) addressBlock.get("stateProvinceRegion");
        String address = (String) addressBlock.get("address");


        if (StringUtils.isEmpty(customerType) || StringUtils.isEmpty(type)) {
            log.error("Customer type or account type missing for this account");
            throw new NotFoundException("Missing Account Details");
        }

        return CliqAccountDto.builder()
                .customerType(customerType)
                .type(type)
                .currency((String) additionalDetails.get(RestConstants.CustomerAliasAccountsResponse.CURRENCY))
                .iban(iban)
                .customerName(firstName)
                .customerAddress(address)
                .bic(biCode)
                .build();
    }

    /**
     * Processes the accounts from the given response body and returns a list of AisConsentAccountDto objects.
     *
     * @param responseBody the response body as a string
     * @return a list of AisConsentAccountDto objects representing the accounts
     */
    @SuppressWarnings("unchecked")
    private List<ConsentAccountDto> processAccounts(String responseBody) {
        Map<String, Object> body = processResponseBody(responseBody);

        if (body == null) {
            return null;
        }

        Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.AccountResponse.RESPONSE);

        if (response == null || response.get(RestConstants.AccountResponse.ACCOUNTS_BLOCK) == null) {
            log.error("Response is missed or [accounts] block is empty");
            return Collections.emptyList();
        }

        List<Map<String, Object>> accounts = (List<Map<String, Object>>) response.get(RestConstants.AccountResponse.ACCOUNTS_BLOCK);

        return accounts.stream()
                // Only accounts with status equals activeStatus
                .filter(account -> activeStatus.equals(account.get(RestConstants.AccountResponse.STATUS)))
                .map(account -> {
                    Map<String, String> titles =
                            (Map<String, String>) account.get(RestConstants.AccountResponse.ACCOUNT_TITLE_BLOCK);
                    String category = (String) account.get(RestConstants.AccountResponse.CATEGORY);
                    String view = processListView(account);

                    return ConsentAccountDto.builder()
                            .accountRef((String) account.get(RestConstants.AccountResponse.ACCOUNT_ID))
                            .ibanNumber((String) account.get(RestConstants.AccountResponse.ACCOUNT_IBAN_NUMBER))
                            .titleEn(titles.get(RestConstants.AccountResponse.ACCOUNT_TITLE_EN))
                            .titleAr(titles.get(RestConstants.AccountResponse.ACCOUNT_TITLE_AR))
                            .currency((String) account.get(RestConstants.AccountResponse.CURRENCY))
                            .listViewEn(view + processAccountTypeDesc(category, AccountLocale.EN))
                            .listViewAr(view + processAccountTypeDesc(category, AccountLocale.AR))
                            .category((String) account.get(RestConstants.AccountResponse.CATEGORY))
                            .build();
                }).collect(Collectors.toList());

    }

    /**
     * This method processes the given account information and returns a formatted string for list view.
     *
     * @param account a map representing the account information
     * @return a formatted string for list view
     */
    private String processListView(Map<String, Object> account) {
        String number = (String) account.get(RestConstants.AccountResponse.ACCOUNT_IBAN_NUMBER);
        String currency = (String) account.get(RestConstants.AccountResponse.CURRENCY);
        return (StringUtils.isNotEmpty(number)
                ? number.substring(number.length() - 7) + RestConstants.AccountResponse.SEPARATOR_FOR_VIEW
                : "")
                + (StringUtils.isNotEmpty(currency) ? currency : "");
    }

    /**
     * Processes the account type description based on the provided category and locale.
     *
     * @param category the category of the account type
     * @param locale   the locale for the account type description
     * @return the processed account type description
     */
    private String processAccountTypeDesc(String category, AccountLocale locale) {
        ConsentAccountType type = accountTypes.get(category);
        if (type == null) {
            log.error("No account type with category {} was found", category);
            return StringUtils.EMPTY;
        }
        return RestConstants.AccountResponse.SEPARATOR_FOR_VIEW
                + (locale.equals(AccountLocale.EN) ? type.getDescriptionEn() : type.getDescriptionAr());
    }


    /**
     * Validate ACH payment with Core
     *
     * @param transactionInfoRequest the ID of the customer
     * @return object of AccountDto objects with currency , category of the account no response body
     */
    public TransactionInfoResponse validatePaymentAgainstCore(TransactionInfoRequest transactionInfoRequest) throws JsonProcessingException {

        log.debug("getAccountDetailsProcess request {} ", transactionInfoUrl);

        Map<String, Object> payloadMap = processResponseBody(transactionInfoRequest);

        ResponseEntity<String> responseEntity = processRequest(transactionInfoUrl, HttpMethod.POST, payloadMap, null);

        if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
            log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response from backend, validateAchPaymentCore()" : responseEntity.getStatusCode());
            throw new NotFoundException("Amount validation failed");

        }

        ObjectMapper objectMapper = new ObjectMapper();

        return objectMapper.readValue(
                responseEntity.getBody(),
                TransactionInfoResponse.class
        );


    }
}
