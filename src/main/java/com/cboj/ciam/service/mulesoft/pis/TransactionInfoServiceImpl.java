package com.cboj.ciam.service.mulesoft.pis;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.cboj.ciam.service.mulesoft.MulesoftService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.cboj.ciam.jpa.AccountScheme.ALIAS;

/**
 * Service implementation for retrieving customer accounts.
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "false")
public class TransactionInfoServiceImpl extends MulesoftService implements TransactionInfoService {

    @Value("${ciam.mulesoft.transaction-info-url}")
    private String transactionInfoUrl;

    @Value("${ciam.mulesoft.exchange-rate-url}")
    private String exchangeRateUrl;

    private final PisConsentDataService pisConsentService;
    private final PisConsentInitiationMapper initiationMapper;
    private final ObjectMapper objectMapper;

    public TransactionInfoServiceImpl(KeycloakClientService keycloakClientService,
                                      MulesoftServiceProperties properties,
                                      RestTemplate restTemplate,
                                      PisConsentDataService pisConsentService, PisConsentInitiationMapper initiationMapper,
                                      ObjectMapper objectMapper) {
        super(restTemplate, keycloakClientService, properties);
        this.pisConsentService = pisConsentService;
        this.initiationMapper = initiationMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    public CompletableFuture<TransactionInfoDto> getTransactionInfo(@NotNull String consentRef, String accountRef) {
        CompletableFuture<TransactionInfoDto> result = pisConsentService.findByAttr(PisConsentEntity_.CONSENT_REF, UUID.fromString(consentRef))
                .thenApply(consent -> {
                    if (consent == null) {
                        throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
                    }
                    if (StringUtils.isEmpty(consent.getCustomerRef())) {
                        throw new NotFoundException(String.format("PisConsent customer ID not found in consent %s", consentRef));
                    }

                    InitiationDto initiationDto = initiationMapper.apply(consent);

                    if (StringUtils.isNotEmpty(accountRef) && initiationDto.getDebtorAccount().getIdentification() == null) {
                        initiationDto.getDebtorAccount().setIdentification(accountRef);
                    }

                    HashMap<String, Object> payload = new HashMap<>();
                    payload.put(RestConstants.TransactionInfo.CUSTOMER_REF_PAYLOAD_ATTR, consent.getCustomerRef());
                    payload.put(RestConstants.TransactionInfo.INITIATION_PAYLOAD_ATTR, initiationDto);

                    if (initiationDto.getBeneficiaryData() == null) {
                        log.info("CBOJ:DEBUG TRANSACTION no beneficiary data");
                    } else if (initiationDto.getBeneficiaryData().getBeneficiaryAgent() == null) {
                        log.info("CBOJ:DEBUG TRANSACTION no beneficiary data");
                    } else {
                        log.info("CBOJ:DEBUG TRANSACTION identification {} and schema available {}",
                                initiationDto.getBeneficiaryData().getBeneficiaryAgent().getAgentIdentification().getIdentification(),
                                initiationDto.getBeneficiaryData().getBeneficiaryAgent().getAgentIdentification().getSchema());
                    }

                    ResponseEntity<String> responseEntity = processRequest(transactionInfoUrl, HttpMethod.POST, payload, null);

                    log.info("CBOJ:DEBUG TRANSACTION API returned");

                    if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
                        log.error(MULE_RESPONSE_EX_TEMPLATE,
                                responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
                        return null;
                    }
                    TransactionInfoDto transactionInfoDto = processResponse(responseEntity.getBody());

                    //set beneficiary name
                    if (consent.getLocalInstrument().equals(LocalInstrument.INTERNAL)) {


                        transactionInfoDto.setFxRate(null);        // No fx rate for internal transfer
                        transactionInfoDto.getInitiation().setBeneficiaryName(consent.getCreditorNameEn());

                    } else if ((consent.getLocalInstrument().equals(LocalInstrument.DOMESTIC_CLIQ)
                            && (consent.getCreditorAccScheme().equals(ALIAS) || consent.getCreditorAccScheme().equals(AccountScheme.MOBILE)))) {

                        transactionInfoDto.getInitiation().setBeneficiaryName(consent.getCreditorNameEn());
                    } else {
                        transactionInfoDto.getInitiation().setBeneficiaryName(
                                initiationDto.getBeneficiaryData().getBeneficiaryName().getFirstName()
                                        + " " + initiationDto.getBeneficiaryData().getBeneficiaryName().getMiddleName()
                                        + " " + initiationDto.getBeneficiaryData().getBeneficiaryName().getLastName()
                        );
                    }


                    transactionInfoDto.getInitiation().setPaymentPurposeDesc(initiationDto.getPaymentPurposeDesc());
                    return transactionInfoDto;
                });
        return result;
    }

    /**
     * Processes the response from a REST API request and converts it into a TransactionInfoDto object.
     *
     * @param responseBody The response body string from the REST API request.
     * @return The TransactionInfoDto object representing the response.
     * @throws RuntimeException If there is an error parsing the response body.
     */
    private TransactionInfoDto processResponse(String responseBody) {
        log.info("CBOJ:DEBUG TRANSACTION API processResponse ");

        Map<String, Object> body = processResponseBody(responseBody);

        if (body == null) {
            return null;
        }

        return objectMapper.convertValue(body, TransactionInfoDto.class);
    }

    @Override
    public ExchangeAmountDto getExchangeRate(String fromCurrency, String toCurrency, double currencyAmount, String amountCurrency) {
        HashMap<String, Object> payload = new HashMap<>();
        payload.put(RestConstants.ExchangeRateRequest.FROM_CURRENCY, fromCurrency);
        payload.put(RestConstants.ExchangeRateRequest.TO_CURRENCY, toCurrency);
        // Only two characters are acceptable after dot
        payload.put(RestConstants.ExchangeRateRequest.CURRENCY_AMOUNT, String.format("%.2f", currencyAmount));
        payload.put(RestConstants.ExchangeRateRequest.AMOUNT_CCY, amountCurrency);

        ResponseEntity<String> responseEntity = processRequest(exchangeRateUrl, HttpMethod.POST, payload, null);

        if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
            log.error(MULE_RESPONSE_EX_TEMPLATE,
                    responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());

            throw new NotFoundException("Exchange Rate Retrieval failed");
        }

        return processExchangeResponse(responseEntity.getBody());


    }

    @SuppressWarnings("unchecked")
    private ExchangeAmountDto processExchangeResponse(String responseBody) {
        log.debug("CBOJ:DEBUG exchange rate API processResponse {}", responseBody);

        Map<String, Object> body = processResponseBody(responseBody);

        if (body == null) {
            return null;
        }

        Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.ExchangeRateResponse.RESPONSE_BLOCK);

        if (response == null || response.get(RestConstants.ExchangeRateResponse.UTILITY) == null) {
            log.error("Response is missed or [utility] block is empty");
            throw new NotFoundException("Missing Exchange Rate Details");
        }

        Map<String, Object> utilities = (Map<String, Object>) response.get(RestConstants.ExchangeRateResponse.UTILITY);

        if (utilities.get(RestConstants.ExchangeRateResponse.EXCHANGE_AMOUNT) == null) {
            log.error(" Exchange amount missing in response");
            throw new NotFoundException("Missing Exchange Rate Details");
        }

        ExchangeAmountDto dto = new ExchangeAmountDto();
        dto.setExchangedAmount(Double.parseDouble((String) utilities.get(RestConstants.ExchangeRateResponse.EXCHANGE_AMOUNT)));
        dto.setSellRate(Double.parseDouble((String) utilities.get(RestConstants.ExchangeRateResponse.SELL_RATE)));

        return dto;
    }
}


