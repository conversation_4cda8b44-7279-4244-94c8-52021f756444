package com.cboj.ciam.service.mulesoft.pis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class TransactionInfoErrorDto implements Serializable {

  private String id;
  private String code;
  private String desc;
  private List<Error> errors;

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Error {
    private String code;
    private String desc;
  }
}
