package com.cboj.ciam.service.mulesoft;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@ConditionalOnProperty(value = "ciam.mulesoft.mock", havingValue = "false")
public class SwiftRefServiceImpl extends MulesoftService implements SwiftRefService {

  private final String ibanValidationUrl;
  private final String bicUrl;
  private final String ibanParam;

  protected SwiftRefServiceImpl(RestTemplate restTemplate,
                                KeycloakClientService keycloakClientService,
                                MulesoftServiceProperties properties) {
    super(restTemplate, keycloakClientService, properties);
    this.ibanValidationUrl = properties.getIbanValidationUrl();
    this.ibanParam = properties.getIbanParameter();
    this.bicUrl = properties.getBicUrl();
  }

  @Override
  public boolean validateIban(@NotNull PisConsentDto pisConsentDto) {
    Map<String, String> variables = new HashMap<>(1);
    variables.put(ibanParam, pisConsentDto.getCreditorAccRef());

    ResponseEntity<String> responseEntity = processRequest(ibanValidationUrl, HttpMethod.GET, null, variables);

    if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
      log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
      return false;
    }

    Map<String, Object> body = processResponseBody(responseEntity.getBody());
    Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.Iban.RESPONSE_BLOCK);


    if (response == null) {
      log.error("Response is missed or [response] block is empty");
      throw new NotFoundException("Missing Iban Details");
    }

    String ibanValid = (String) response.get(RestConstants.Iban.VALIDITY);

    return RestConstants.Iban.IBAN_VALID_VALUE.equals(ibanValid);

//    return processValidationIban(responseEntity.getBody());
  }

  @Override
  public String getBicCode(String iban) {
    Map<String, String> variables = new HashMap<>(1);
    variables.put(ibanParam, iban);

    ResponseEntity<String> responseEntity = processRequest(bicUrl, HttpMethod.GET, null, variables);

    if (responseEntity == null || !responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
      log.error(MULE_RESPONSE_EX_TEMPLATE, responseEntity == null ? "no response after all attempts" : responseEntity.getStatusCode());
      return null;
    }

    return processBicCode(responseEntity.getBody());
  }

  /**
   * Process the IBAN validity response and determine if the IBAN is valid.
   *
   * @param responseBody The response body containing IBAN validity information.
   * @return true if the IBAN is valid, false otherwise.
   */
  @SuppressWarnings("unchecked")
  private boolean processValidationIban(String responseBody) {
    Map<String, Object> body = processResponseBody(responseBody);

    if (body == null) {
      return false;
    }

    Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.Iban.RESPONSE_BLOCK);

    if (response == null) {
      log.error("Response is missed or [response] block is empty");
      throw new NotFoundException("Missing Iban Details");
    }

    String ibanValid = (String) response.get(RestConstants.Iban.VALIDITY);

    return RestConstants.Iban.IBAN_VALID_VALUE.equals(ibanValid);
  }

  /**
   * Process the BIC code from the given response body.
   *
   * @param responseBody the string representing the response body containing BIC details
   * @return the BIC (Bank Identifier Code) extracted from the response body
   */
  @SuppressWarnings("unchecked")
  private String processBicCode(String responseBody) {
    Map<String, Object> body = processResponseBody(responseBody);

    if (body == null) {
      return null;
    }

    Map<String, Object> response = (Map<String, Object>) body.get(RestConstants.Iban.RESPONSE_BLOCK);

    if (response == null) {
      log.error("Response is missed or [response] block is empty");
      throw new NotFoundException("Missing BIC Details");
    }

    return (String) response.get(RestConstants.Iban.BIC);
  }
}
