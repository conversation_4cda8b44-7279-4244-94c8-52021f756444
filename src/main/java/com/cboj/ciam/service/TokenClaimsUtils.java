package com.cboj.ciam.service;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.SsoConstants;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.text.ParseException;

public class TokenClaimsUtils {

  public static String extractClientId(@NotNull HttpServletRequest request) {
    try {
      String token = request.getHeader(HttpHeaders.AUTHORIZATION);

      JWTClaimsSet claims = JWTParser
          .parse(token.substring(SsoConstants.TOKEN_TYPE_BEARER.length() + 1))
          .getJWTClaimsSet();

      return claims.getStringClaim(SsoConstants.Client.CLIENT_ID);
    } catch (ParseException ex) {
      throw new UnauthorizedException(ex.getLocalizedMessage());
    }
  }
}
