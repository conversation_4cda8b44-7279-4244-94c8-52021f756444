package com.cboj.ciam.service;

import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.data.caf.CafConsentDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Scheduled task that checks for expired Consents and updates their status accordingly.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CheckExpiredConsentScheduledTask {

  private final AisConsentDataService aisService;
  private final CafConsentDataService cafService;

  @Scheduled(cron = "${ciam.scheduler.expired.crone}")
  public void execute() {
    log.debug("Starting task for checking expired consents...");
    LocalDateTime expiryDate = LocalDateTime.now();

    CompletableFuture<List<AisConsent>> ais = processAisConsents(expiryDate);
    CompletableFuture<List<CafConsent>> caf = processCafConsents(expiryDate);
    CompletableFuture.allOf(ais, caf);
  }

  private CompletableFuture<List<AisConsent>> processAisConsents(LocalDateTime expiryDate) {
    return aisService.searchExpired(expiryDate)
        .thenCompose(consents -> {
          processConsentObjs(consents, expiryDate);

          return aisService.updateAll(consents);
        });
  }

  private CompletableFuture<List<CafConsent>> processCafConsents(LocalDateTime expiryDate) {
    return cafService.searchExpired(expiryDate)
        .thenCompose(consents -> {
          processConsentObjs(consents, expiryDate);

          return cafService.updateAll(consents);
        });
  }

  private void processConsentObjs(List<? extends Consent> consents, LocalDateTime expiryDate) {
    if (CollectionUtils.isEmpty(consents)) {
      log.debug("There are no Consents for expiring");
    }

    consents.forEach(consent -> {
      consent.setUpdateDate(expiryDate);
      consent.setStatus(ConsentStatus.EXPIRED);
    });
  }
}
