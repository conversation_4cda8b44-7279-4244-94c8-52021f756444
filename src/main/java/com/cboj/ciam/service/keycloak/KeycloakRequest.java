package com.cboj.ciam.service.keycloak;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeycloakRequest {

  private String userId;
  private String userName;
  private String password;
  private String clientId;
  private String clientSecret;
  private String authCode;
  private String authUrl;
  private String sessionCookie;
  private String refreshToken;
  private String redirectUri;
  private String scope;
  private String state;
  private UUID consentRef;

}
