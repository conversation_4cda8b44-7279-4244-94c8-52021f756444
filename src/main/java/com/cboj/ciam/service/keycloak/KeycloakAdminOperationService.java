package com.cboj.ciam.service.keycloak;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.config.CacheConfig;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class KeycloakAdminOperationService {

  @Value("${keycloak.uri.admin-clients-uri}")
  private String adminClientsUri;

  @Value("${keycloak.uri.token-uri}")
  private String tokenUri;

  @Value("${keycloak.admin.client-id}")
  private String adminClientId;

  private final RestTemplate restTemplate;
  private final ObjectMapper mapper;

  /**
   * Retrieves the client representation for the given client ID and access token.
   *
   * @param clientId    The ID of the client.
   * @param accessToken The access token to authorize the request. If it is empty, the method will generate a new access token.
   * @return The {@link ClientRepresentation} object.
   * @throws NotFoundException If the client with the specified ID is not found.
   * @throws RuntimeException  If there is an error processing the response body.
   */
  @Cacheable(value = CacheConfig.CLIENTS_CACHE_NAME, key = "#clientId")
  @SuppressWarnings("unchecked")
  public ClientRepresentation getClientRepresentation(String clientId, String accessToken) {

    URI uri = UriComponentsBuilder.fromUriString(adminClientsUri)
        .queryParam(SsoConstants.Client.CLIENT_ID, clientId)
        .queryParam(SsoConstants.ENABLE, true)
        .build().toUri();

    ResponseEntity<String> clientEntity = makeRequest(uri, HttpMethod.GET, accessToken, String.class);
      log.info(clientEntity.getBody());
    if (!clientEntity.getStatusCode().equals(HttpStatus.OK) || clientEntity.getBody() == null) {
      throw new NotFoundException("Client with ID " + clientId + "not found");
    }

    try {
      List<Map<String, Object>> users = mapper.readValue(clientEntity.getBody(), List.class);
      return mapper.convertValue(users.stream().findFirst(), ClientRepresentation.class);
    } catch (JsonProcessingException e) {
      log.error(e.getLocalizedMessage(), e);
      throw new RuntimeException(e.getLocalizedMessage(), e);
    }
  }

  /**
   * Retrieves an admin token using the given username and password.
   *
   * @param username the admins username
   * @param password the admins password
   * @return the admin token
   */
  @Cacheable(value = CacheConfig.ADMIN_TOKEN_CACHE_NAME, key = "#username")
  public String getAdminToken(String username, String password) {
    log.info("Get admin token for username {}", username);
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, adminClientId);
    map.add(SsoConstants.OAuth2.USERNAME, username);
    map.add(SsoConstants.OAuth2.PASSWORD, password);
    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.PASSWORD);

    return restTemplate.postForObject(tokenUri, new HttpEntity<>(map, null), String.class);
  }

  /**
   * Makes an HTTP request using the given URL, method, payload, access token, and response type.
   *
   * @param url          The URL to send the request to.
   * @param method       The HTTP method to use for the request.
   * @param accessToken  The access token to include in the request header.
   * @param responseType The class type of the expected response.
   * @param <T>          The type of the expected response.
   * @return The ResponseEntity containing the response from the server.
   */
  public <T> ResponseEntity<T> makeRequest(URI url,
                                           HttpMethod method,
                                           String accessToken,
                                           Class<T> responseType) {
    log.info("Make {} request for url {}", method, url);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, SsoConstants.TOKEN_TYPE_BEARER + " " + accessToken);
    HttpEntity<?> entity = new HttpEntity<>(headers);

    return restTemplate.exchange(url, method, entity, responseType);
  }
}
