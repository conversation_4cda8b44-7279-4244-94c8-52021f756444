package com.cboj.ciam.service.keycloak.representations;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class ClientRepresentation {

  protected String id;
  protected String clientId;
  protected String name;
  protected String description;
  protected String rootUrl;
  protected String adminUrl;
  protected String baseUrl;
  protected Boolean surrogateAuthRequired;
  protected Boolean enabled;
  protected Boolean alwaysDisplayInConsole;
  protected String clientAuthenticatorType;
  protected String secret;
  protected String registrationAccessToken;
  protected List<String> redirectUris;
  protected List<String> webOrigins;
  protected Integer notBefore;
  protected Boolean bearerOnly;
  protected Boolean consentRequired;
  protected Boolean standardFlowEnabled;
  protected Boolean implicitFlowEnabled;
  protected Boolean directAccessGrantsEnabled;
  protected Boolean serviceAccountsEnabled;
  protected Boolean oauth2DeviceAuthorizationGrantEnabled;
  protected Boolean authorizationServicesEnabled;
  protected Boolean publicClient;
  protected Boolean frontchannelLogout;
  protected String protocol;
  protected Map<String, String> attributes;
  protected Map<String, String> authenticationFlowBindingOverrides;
  protected Boolean fullScopeAllowed;
  protected Integer nodeReRegistrationTimeout;
  protected Map<String, Integer> registeredNodes;

  protected List<String> defaultClientScopes;
  protected List<String> optionalClientScopes;

  private Map<String, Boolean> access;
  protected String origin;

}
