package com.cboj.ciam.service.keycloak.representations;

import com.fasterxml.jackson.annotation.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class AccessTokenResponse {

  @JsonProperty("access_token")
  protected String token;

  @JsonProperty("expires_in")
  protected long expiresIn;

  @JsonProperty("refresh_expires_in")
  protected long refreshExpiresIn;

  @JsonProperty("refresh_token")
  protected String refreshToken;

  @JsonProperty("token_type")
  protected String tokenType;

  @JsonProperty("id_token")
  protected String idToken;

  @JsonProperty("not-before-policy")
  protected int notBeforePolicy;

  @JsonProperty("session_state")
  protected String sessionState;

  protected Map<String, Object> otherClaims = new HashMap<>();

  @JsonProperty("scope")
  protected String scope;

  @JsonProperty("error")
  protected String error;

  @JsonProperty("error_description")
  protected String errorDescription;

  @JsonProperty("error_uri")
  protected String errorUri;

  @JsonAnyGetter
  public Map<String, Object> getOtherClaims() {
    return otherClaims;
  }

  @JsonAnySetter
  public void setOtherClaims(String name, Object value) {
    otherClaims.put(name, value);
  }

  public static AccessTokenResponse from(AccessTokenResponse full) {
    AccessTokenResponse safe = new AccessTokenResponse();
    safe.setToken(full.getToken());
    safe.setExpiresIn(full.getExpiresIn());
    safe.setRefreshToken(full.getRefreshToken());
    safe.setRefreshExpiresIn(full.getRefreshExpiresIn());
    safe.setTokenType(full.getTokenType());
    safe.setNotBeforePolicy(full.getNotBeforePolicy());
    safe.setSessionState(full.getSessionState());
    safe.setScope(full.getScope());
    return safe;
  }
}
