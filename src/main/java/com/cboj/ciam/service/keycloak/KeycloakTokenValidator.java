package com.cboj.ciam.service.keycloak;

import com.cboj.ciam.JwtValidationException;
import com.cboj.ciam.consts.SsoConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

/**
 * This class is responsible for validating Keycloak access tokens.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KeycloakTokenValidator {

  @Value("${keycloak.uri.token-introspect}")
  private String tokenIntrospectUri;

  @Value("${keycloak.introspect.client_id}")
  private String introspectClientId;

  @Value("${keycloak.introspect.client_secret}")
  private String introspectClientSecret;

  private final RestTemplate restTemplate;

  /**
   * Introspects a token by sending a request to the token introspection endpoint.
   *
   * @param verifiedToken The token to introspect.
   * @return True if the token is valid and active, false otherwise.
   * @throws JwtValidationException If there is an error while introspecting the token.
   */
  @SuppressWarnings("rawtypes")
  public boolean introspectToken(String verifiedToken) {
    URI uri = UriComponentsBuilder.fromUriString(tokenIntrospectUri).build().toUri();

    MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
    payload.add(SsoConstants.OAuth2.CLIENT_ID, introspectClientId);
    payload.add(SsoConstants.OAuth2.CLIENT_SECRET, introspectClientSecret);
    payload.add(SsoConstants.OAuth2.TOKEN, verifiedToken);

    ResponseEntity<Map> response = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(payload, null), Map.class);
    if (response.getStatusCode().equals(HttpStatus.OK) && response.getBody() != null) {
      return (boolean) response.getBody().get("active");
    } else {
      throw new JwtValidationException("Error while introspecting token. Response status: " + response.getStatusCode());
    }
  }

}
