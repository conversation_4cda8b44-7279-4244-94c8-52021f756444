package com.cboj.ciam.service.keycloak;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.cboj.ciam.service.keycloak.representations.UserRepresentation;
import com.cboj.ciam.service.keycloak.representations.UserSessionRepresentation;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jwt.JWTClaimsSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.constraints.NotNull;
import java.net.URI;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class KeycloakAdminService {

  @Value("${keycloak.uri.admin-users-uri}")
  private String adminUsersUri;

  @Value("${keycloak.uri.admin-clients-uri}")
  private String adminClientsUri;

  @Value("${keycloak.uri.admin-sessions-uri}")
  private String adminSessionsUri;

  @Value("${keycloak.uri.jwk-set-uri}")
  private String certsUri;

  @Value("${keycloak.admin.name}")
  private String adminName;

  @Value("${keycloak.admin.password}")
  private String adminPass;

  private final RestTemplate restTemplate;
  private final ObjectMapper mapper;
  private final KeycloakAdminOperationService operationService;

  /**
   * Retrieves certificates using REST template.
   *
   * @return ResponseEntity with the response body as a String
   */
  public ResponseEntity<String> getCerts() {
    return restTemplate.getForEntity(certsUri, String.class);
  }

  /**
   * Processes the revoke of a client with the given client ID and access token.
   *
   * @param clientId    the ID of the client to revoke
   * @param accessToken the access token used for authentication, can be empty
   * @return {@link ResponseEntity} object containing the response from the server
   */
  public ResponseEntity<String> processRevokeClient(String clientId, String accessToken) {
    if (StringUtils.isEmpty(accessToken)) {
      accessToken = processAdminAccessToken(adminName, adminPass);
    }

    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>(1);
    headers.add(HttpHeaders.AUTHORIZATION, SsoConstants.TOKEN_TYPE_BEARER + " " + accessToken);

    ClientRepresentation clientRepresentation = operationService.getClientRepresentation(clientId, accessToken);

    clientRepresentation.setEnabled(false);

    URI uri = UriComponentsBuilder.fromUriString(adminClientsUri)
        .pathSegment(clientRepresentation.getId())
        .build().toUri();

    return restTemplate.exchange(uri, HttpMethod.PUT, new HttpEntity<>(clientRepresentation, headers), String.class);
  }

  /**
   * Retrieves the client representation for the given clientId.
   *
   * @param clientId The identifier of the client.
   * @return The client representation object.
   */
  public ClientRepresentation getClientRepresentation(String clientId) {
    String accessToken = processAdminAccessToken(adminName, adminPass);
    return operationService.getClientRepresentation(clientId, accessToken);
  }

  /**
   * Retrieves the user representation for the given user ID.
   *
   * @param userId the identifier of the user
   * @return the {@link UserRepresentation} associated with the given user ID
   */
  public UserRepresentation getUserRepresentationById(@NotNull String userId) {
    String accessToken = processAdminAccessToken(adminName, adminPass);

    URI uri = UriComponentsBuilder.fromUriString(adminUsersUri)
        .pathSegment(userId)
        .build().toUri();

    return findUser(uri, accessToken);
  }

  /**
   * Retrieves a UserRepresentation object by the given username.
   *
   * @param userName the username of the user to retrieve
   * @return the {@link UserRepresentation} object representing the user with the given username
   */
  public UserRepresentation getUserRepresentationByName(String userName) {
    String accessToken = processAdminAccessToken(adminName, adminPass);

    URI uri = UriComponentsBuilder.fromUriString(adminUsersUri)
        .queryParam(SsoConstants.User.USER_NAME, userName)
        .queryParam(SsoConstants.ENABLE, true)
        .queryParam(SsoConstants.User.EXACT, true)
        .build().toUri();

    UserRepresentation user = searchUser(uri, accessToken);
    //user.getAttributes().computeIfAbsent("cliqConsent", k -> new ArrayList<>()).add("false"); //Hussein: mock the attribute

    return user;
  }

  /**
   * Process revoking user token.
   *
   * @param request The KeycloakRequest object containing the necessary information for revoking the user token.
   * @throws NotFoundException If sessions for the user with the given ID are not found.
   * @throws RuntimeException  If an error occurs while processing the response or making the request.
   */
  @SuppressWarnings("unchecked")
  public void processRevokeUserToken(KeycloakRequest request) {
    log.info("Process revoke user token for clientId {}", request.getClientId());
    String accessToken = processAdminAccessToken(adminName, adminPass);

    URI uri = UriComponentsBuilder.fromUriString(adminUsersUri)
        .pathSegment(request.getUserId(), "sessions")
        .build().toUri();

    ResponseEntity<String> sessionsEntity = operationService.makeRequest(uri, HttpMethod.GET, accessToken, String.class);

    if (!sessionsEntity.getStatusCode().equals(HttpStatus.OK) || sessionsEntity.getBody() == null) {
      throw new NotFoundException("Sessions for user with ID " + request.getUserId() + " not found");
    }

    try {
      mapper.readValue(sessionsEntity.getBody(), List.class).stream()
          .map((rep) -> this.mapper.convertValue(rep, UserSessionRepresentation.class))
          .filter(rep -> ((UserSessionRepresentation) rep).getClients().containsValue(request.getClientId()))
          .filter(rep -> ((UserSessionRepresentation) rep).getId().equals(request.getState()))
          .findFirst()
          .ifPresent(session -> {
            final URI sessionUri = UriComponentsBuilder.fromUriString(adminSessionsUri)
                .pathSegment(((UserSessionRepresentation) session).getId())
                .build().toUri();
            operationService.makeRequest(sessionUri, HttpMethod.DELETE, accessToken, String.class);
          });
    } catch (JsonProcessingException e) {
      log.error(e.getLocalizedMessage(), e);
      throw new RuntimeException(e.getLocalizedMessage(), e);
    }
  }

  /**
   * Retrieves the user representation from the specified URI.
   *
   * @param uri The URI from which to retrieve the user representation.
   * @return The {@link ResponseEntity} containing the user representation as a string.
   * @throws NotFoundException If the user is not found.
   */
  private ResponseEntity<String> getUserRepresentation(URI uri, String accessToken) {
    ResponseEntity<String> response = operationService.makeRequest(uri, HttpMethod.GET, accessToken, String.class);

    if (!response.getStatusCode().equals(HttpStatus.OK) || response.getBody() == null) {
      throw new NotFoundException("User not found");
    }

    return response;
  }

  /**
   * Finds a user representation by making a GET request to the specified URI and deserializing the response body into a UserRepresentation
   * object.
   *
   * @param uri the URI to make the GET request to
   * @return the {@link UserRepresentation} object representing the user
   * @throws RuntimeException if there is an error processing the JSON response
   */
  private UserRepresentation findUser(URI uri, String accessToken) {
    ResponseEntity<String> response = getUserRepresentation(uri, accessToken);

    try {
      return mapper.readValue(response.getBody(), UserRepresentation.class);
    } catch (JsonProcessingException e) {
      log.error(e.getLocalizedMessage(), e);
      log.error("Error getting UserRepresentation with response: {}", response.getBody());
      throw new RuntimeException(e.getLocalizedMessage(), e);
    }
  }

  /**
   * Searches for a user representation using the provided URI.
   *
   * @param uri the URI used to search for the user
   * @return the {@link UserRepresentation} if found, or null otherwise
   */
  @SuppressWarnings("unchecked")
  private UserRepresentation searchUser(URI uri, String accessToken) {

    ResponseEntity<String> response = getUserRepresentation(uri, accessToken);

    try {
      List<Map<String, Object>> users = mapper.readValue(response.getBody(), List.class);
      return mapper.convertValue(users.stream().findFirst(), UserRepresentation.class);
    } catch (JsonProcessingException e) {
      log.error(e.getLocalizedMessage(), e);
      log.error("Error getting UserRepresentation with response: {}", response.getBody());
      throw new RuntimeException(e.getLocalizedMessage(), e);
    }
  }

  /**
   * Processes the admin access token and returns the access token string.
   *
   * @return the access token string
   * @throws UnauthorizedException if there is an error parsing the token
   */
  public String processAdminAccessToken(String adminName, String adminPass) {
    String token = operationService.getAdminToken(adminName, adminPass);

    try {
      return JWTClaimsSet.parse(token).getStringClaim(SsoConstants.OAuth2.ACCESS_TOKEN);
    } catch (ParseException e) {
      throw new UnauthorizedException(e.getLocalizedMessage());
    }
  }
}
