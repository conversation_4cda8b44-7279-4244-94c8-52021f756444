package com.cboj.ciam.service.keycloak;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.config.CacheConfig;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.service.keycloak.representations.AccessTokenResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

import static com.cboj.ciam.consts.ErrorCodes.CONSENT_INVALID_LOGIN;

@Slf4j
@Component
@RequiredArgsConstructor
public class KeycloakClientService {

  @Value("${keycloak.uri.token-uri}")
  private String tokenUri;

  @Value("${keycloak.uri.mulesoft.token-uri}")
  private String muleTokenUri;

  @Value("${keycloak.uri.auth-uri}")
  private String authUri;

  @Value("${keycloak.uri.revoke}")
  private String revokeUri;

  private final RestTemplate restTemplate;

  /**
   * Retrieves the client token using the given KeycloakRequest.
   *
   * @param request The KeycloakRequest to be used for authentication.
   * @return The client token as a String.
   */
  @Cacheable(value = CacheConfig.MULE_TOKEN_CACHE_NAME)
  public AccessTokenResponse getMuleClientToken(KeycloakRequest request) {
    log.info("Get mulesoft token for clientId {}", request.getClientId());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.CLIENT_SECRET, request.getClientSecret());
    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.CLIENT_CREDENTIALS);

    return restTemplate.postForObject(muleTokenUri, new HttpEntity<>(map, null), AccessTokenResponse.class);
  }

  /**
   * Retrieves the client token using the given KeycloakRequest.
   *
   * @param request The KeycloakRequest to be used for authentication.
   * @return The client token as a String.
   */
  public AccessTokenResponse getClientToken(KeycloakRequest request) {
    log.info("Get client token for clientId {}", request.getClientId());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.CLIENT_SECRET, request.getClientSecret());
    map.add(SsoConstants.OAuth2.SCOPE, request.getScope());
    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.CLIENT_CREDENTIALS);

    return restTemplate.postForObject(tokenUri, new HttpEntity<>(map, null), AccessTokenResponse.class);
  }

  /**
   * Retrieves the user token by making a POST request to the tokenUri with the provided request information.
   *
   * @param request the KeycloakRequest object containing the necessary information to retrieve the user token
   * @return the user token as a string
   */
  public AccessTokenResponse getUserToken(KeycloakRequest request) {
    log.info("Get user token for clientId {}", request.getClientId());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.CLIENT_SECRET, request.getClientSecret());
    map.add(SsoConstants.OAuth2.REDIRECT_URI, request.getRedirectUri());
    map.add(SsoConstants.OAuth2.CODE, request.getAuthCode());
    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.AUTHORIZATION_CODE);

    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    headers.add(SsoConstants.Client.CONSENT_REF_HEADER, request.getConsentRef().toString());

    return restTemplate.postForObject(tokenUri, new HttpEntity<>(map, headers), AccessTokenResponse.class);
  }

  /**
   * Refreshes the user token by sending a request to the token URI with the provided request parameters.
   *
   * @param request the Keycloak request containing the necessary parameters for refreshing the token
   * @return the refreshed user token as a String
   */
  public AccessTokenResponse refreshUserToken(KeycloakRequest request) {
    log.info("Refresh user token for clientId {}", request.getClientId());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.CLIENT_SECRET, request.getClientSecret());
    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.REFRESH_TOKEN);
    map.add(SsoConstants.OAuth2.REFRESH_TOKEN, request.getRefreshToken());

    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    headers.add(SsoConstants.Client.CONSENT_REF_HEADER, request.getConsentRef().toString());

    return restTemplate.postForObject(tokenUri, new HttpEntity<>(map, headers), AccessTokenResponse.class);
  }

  /**
   * Retrieves the authorization form for a given user.
   *
   * @param request the Keycloak request object containing the client ID
   * @return a ResponseEntity containing the HTML form as a string
   */
  public ResponseEntity<String> getUserAuthForm(KeycloakRequest request) {
    log.info("Get user auth form for clientId {}", request.getClientId());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.REDIRECT_URI, request.getRedirectUri());
    map.add(SsoConstants.OAuth2.SCOPE, request.getScope());
    map.add(SsoConstants.OAuth2.STATE, request.getState());
    map.add(SsoConstants.OAuth2.RESPONSE_TYPE, SsoConstants.OAuth2.CODE);

    return restTemplate.postForEntity(authUri, new HttpEntity<>(map, null), String.class);
  }

  /**
   * Processes the user login using Keycloak authentication.
   *
   * @param request the KeycloakRequest object containing the necessary information for login
   * @return the authorization code extracted from the authentication code URL
   * @throws UnauthorizedException if the authentication code URL is empty or does not contain the code
   */
  public URI processUserLogin(KeycloakRequest request) {
    log.info("Process user login {}", request.getAuthUrl());
    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.USERNAME, request.getUserName());
    map.add(SsoConstants.OAuth2.PASSWORD, request.getPassword());
    map.add(SsoConstants.OAuth2.STATE, request.getState());
    map.add(SsoConstants.OAuth2.RESPONSE_TYPE, SsoConstants.OAuth2.CODE);

    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    headers.add(HttpHeaders.COOKIE, request.getSessionCookie());

    URI authCodeUrl = restTemplate.postForLocation(request.getAuthUrl(), new HttpEntity<>(map, headers), String.class);

    if (authCodeUrl == null) {
      throw new UnauthorizedException("Authorization url is empty", CONSENT_INVALID_LOGIN);
    }
    return UriComponentsBuilder.fromUri(authCodeUrl)
        .replaceQueryParam(SsoConstants.OAuth2.SESSION_STATE)
        .build().toUri();
  }

  /**
   * Processes the revocation of a user token.
   *
   * @param request the KeycloakRequest object containing the client ID, client secret, refresh token, and other related parameters.
   * @return a ResponseEntity object that represents the HTTP response containing the result of the token revocation.
   */
  public ResponseEntity<String> processRevokeUserToken(KeycloakRequest request) {
    log.info("Process revoke user token for clientId {}", request.getClientId());

    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add(SsoConstants.OAuth2.CLIENT_ID, request.getClientId());
    map.add(SsoConstants.OAuth2.CLIENT_SECRET, request.getClientSecret());
    map.add(SsoConstants.OAuth2.TOKEN, request.getRefreshToken());
    map.add(SsoConstants.OAuth2.TOKEN_TYPE, SsoConstants.OAuth2.REFRESH_TOKEN);

    map.add(SsoConstants.OAuth2.GRANT_TYPE, SsoConstants.OAuth2.CLIENT_CREDENTIALS);

    return restTemplate.postForEntity(revokeUri, new HttpEntity<>(map, null), String.class);
  }

}
