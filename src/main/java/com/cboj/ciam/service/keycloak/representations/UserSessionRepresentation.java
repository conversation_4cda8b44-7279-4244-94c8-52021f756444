package com.cboj.ciam.service.keycloak.representations;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class UserSessionRepresentation {

  private String id;
  private String username;
  private String userId;
  private String ipAddress;
  private long start;
  private long lastAccess;
  private Map<String, String> clients = new HashMap<>();

}
