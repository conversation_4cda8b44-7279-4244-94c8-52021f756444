package com.cboj.ciam.service.keycloak.representations;

import com.cboj.ciam.service.keycloak.serialization.StringListMapDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserRepresentation {

  protected String self;
  protected String id;
  protected String origin;
  protected Long createdTimestamp;
  protected String username;
  protected Boolean enabled;
  protected Boolean totp;
  protected Boolean emailVerified;
  protected String firstName;
  protected String lastName;
  protected String email;
  protected String federationLink;
  protected String serviceAccountClientId;
  protected Set<String> disableableCredentialTypes;
  protected List<String> requiredActions;
  protected List<String> realmRoles;
  protected Map<String, List<String>> clientRoles;
  protected Integer notBefore;
  protected List<String> groups;

  @JsonDeserialize(using = StringListMapDeserializer.class)
  protected Map<String, List<String>> attributes;

  private Map<String, Boolean> access;

  public String firstAttribute(String key) {
    return this.attributes == null ? null : (this.attributes.get(key) == null ? null
        : (this.attributes.get(key).isEmpty() ? null : (String) ((List<?>) this.attributes.get(key)).get(0)));
  }
}
