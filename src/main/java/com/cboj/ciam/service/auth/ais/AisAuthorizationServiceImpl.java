package com.cboj.ciam.service.auth.ais;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsentAccount;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.service.TokenClaimsUtils;
import com.cboj.ciam.service.data.DataService;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.web.model.AisConsentModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Handles authorization-related operations such as client token retrieval, user login and OTP verification.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AisAuthorizationServiceImpl implements AisAuthorizationService {

  private final AccountService accountService;
  private final AisConsentDataService aisConsentService;
  private final DataService<AisConsentAccount, ConsentAccountDto> aisAccountDataService;
  private final KeycloakAdminService keycloakAdminService;

  /**
   * Creates a consent for Account Information Services (AIS) based on the given consent data and the HTTP servlet request.
   *
   * @param consentDto The AIS consent data.
   * @param request    The HTTP servlet request.
   * @return Creating consent response.
   * @throws UnauthorizedException if the token validation fails.
   */
  @Override
  public CompletableFuture<AisOutputConsentDto> createConsent(AisConsentDto consentDto, HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    ClientRepresentation clientRepresentation = keycloakAdminService.getClientRepresentation(clientId);

    consentDto.setClientAppName(clientRepresentation.getName());
    consentDto.setClientAppId(clientId);

    return aisConsentService.createConsentResponse(consentDto, request);
  }

  /**
   * Approves the consent and updates the status of the AIS consent to ACTIVE.
   *
   * @param consentDto The user consent details containing the consent reference and selected accounts.
   * @return Result of updating AIS consent.
   */
  @Override
  public CompletableFuture<AisConsent> approveConsents(AisConsentModel consentDto) {
    Set<String> accountIds = consentDto.getAccounts().stream()
        .filter(ConsentAccountDto::isSelected)
        .map(ConsentAccountDto::getAccountRef)
        .collect(Collectors.toSet());
    return approveConsents(consentDto.getConsentRef(), accountIds);
  }

  /**
   * Approves consents for a given consent reference and set of account IDs.
   *
   * @param consentRef The consent reference UUID.
   * @param accountIds The set of account IDs to approve consents for.
   * @return Result of updating AIS consent.
   */
  @Override
  public CompletableFuture<AisConsent> approveConsents(UUID consentRef, Set<String> accountIds) {
    return aisConsentService.findByAttr(AisConsent_.CONSENT_REF, consentRef)
        .thenCompose(aisConsent -> {

          checkConsentExists(aisConsent, consentRef);

          List<ConsentAccountDto> accounts = accountService.getCustomerAccounts(aisConsent.getCustomerRef())
              .stream()
              .filter(a -> accountIds.contains(a.getAccountRef()))
              .collect(Collectors.toList());

          return processAisConsentAccounts(aisConsent, accounts);
        });
  }

  /**
   * Processes the consent accounts for the given AIS consent.
   *
   * @param aisConsent The AIS consent to process the accounts for.
   * @param accounts   The list of AIS consent account DTOs to process.
   * @return Result of updating AIS consent.
   */
  private CompletableFuture<AisConsent> processAisConsentAccounts(AisConsent aisConsent, List<ConsentAccountDto> accounts) {
    CompletableFuture<AisConsentAccount> chain = CompletableFuture.supplyAsync(() -> null);
    for (ConsentAccountDto accountDto : accounts) {
      accountDto.setConsentId(aisConsent.getId());

      chain = chain.thenCompose(unused -> aisAccountDataService.create(accountDto));
    }

    aisConsent.setStatus(ConsentStatus.ACTIVE);

    return chain.thenCompose(unused -> aisConsentService.update(aisConsent));
  }

  /**
   * Declines the consents with the given consent reference.
   *
   * @param consentRef The UUID of the consent to be declined.
   * @return String representing the redirect URL after declining the consent.
   */
  @Override
  public CompletableFuture<AisConsent> declineConsents(@NotNull UUID consentRef) {
    return aisConsentService.findByAttr(AisConsent_.CONSENT_REF, consentRef)
        .thenCompose(aisConsent -> {

          checkConsentExists(aisConsent, consentRef);

          aisConsent.setStatus(ConsentStatus.REJECTED);
          return aisConsentService.update(aisConsent);
        });
  }

  /**
   * Checks if the AisConsent exists for the given consentRef.
   *
   * @param aisConsent The AisConsent object to check.
   * @param consentRef The consentRef UUID to check.
   * @throws NotFoundException If the AisConsent is not found for the given consentRef.
   */
  private void checkConsentExists(AisConsent aisConsent, UUID consentRef) {
    if (aisConsent == null) {
      throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
    }
  }
}
