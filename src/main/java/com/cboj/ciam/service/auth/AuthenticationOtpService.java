package com.cboj.ciam.service.auth;

import com.cboj.ciam.web.model.BaseModel;
import com.cboj.ciam.web.model.OtpModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public interface AuthenticationOtpService {

  CompletableFuture<HttpSession> requestOtp(UUID consentRef, HttpServletRequest request);

  CompletableFuture<String> failedOtp(UUID consentRef, HttpServletRequest request);

  <T extends BaseModel> CompletableFuture<T> validateOtp(@NotNull OtpModel otpModel, HttpServletRequest request);
}
