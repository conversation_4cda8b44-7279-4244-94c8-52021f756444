package com.cboj.ciam.service.auth.ais;

import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.web.model.AisConsentModel;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Interface provides methods for authorization and user consent management.
 */
public interface AisAuthorizationService {

  CompletableFuture<AisOutputConsentDto> createConsent(@NotNull AisConsentDto consentDto, HttpServletRequest request);

  CompletableFuture<AisConsent> approveConsents(@NotNull AisConsentModel consentDto);

  CompletableFuture<AisConsent> approveConsents(@NotEmpty UUID consentRef, @NotNull Set<String> accountIds);

  CompletableFuture<AisConsent> declineConsents(@NotNull UUID consentRef);
}
