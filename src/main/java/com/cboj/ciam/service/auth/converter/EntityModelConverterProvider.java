package com.cboj.ciam.service.auth.converter;

import com.cboj.ciam.consts.SsoConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for providing the appropriate EntityModelConverter based on the given scope.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EntityModelConverterProvider {

  private final AisConsentModelConverter aisConverter;
  private final PisConsentModelConverter pisConverter;
  private final CafConsentModelConverter cafConverter;

  /**
   * Retrieves the appropriate EntityModelConverter based on the given scope.
   *
   * @param scope the scope for which to retrieve the converter.
   * @return the EntityModelConverter object for the given scope.
   * @throws IllegalArgumentException if the scope is not supported.
   */
  @SuppressWarnings("rawtypes")
  public EntityModelConverter getConverter(String scope) {
    if (scope == null) {
      throw new IllegalArgumentException("Scope can not be null");
    }
    switch (scope) {
      case SsoConstants.Scope.ACCOUNTS:
        return aisConverter;
      case SsoConstants.Scope.PAYMENTS:
        return pisConverter;
      case SsoConstants.Scope.FUNDS_CONFIRMATIONS:
        return cafConverter;
      default:
        throw new IllegalArgumentException("Unsupported scope " + scope);
    }
  }
}
