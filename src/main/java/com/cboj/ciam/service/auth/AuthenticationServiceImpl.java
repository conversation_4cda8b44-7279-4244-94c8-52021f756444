package com.cboj.ciam.service.auth;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.AuthorizationDto;
import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.jpa.*;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.crypto.EncryptionService;
import com.cboj.ciam.service.data.ConsentServiceProvider;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.cboj.ciam.service.keycloak.KeycloakRequest;
import com.cboj.ciam.service.keycloak.representations.AccessTokenResponse;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.cboj.ciam.service.keycloak.representations.UserRepresentation;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.service.mulesoft.OtpService;
import com.cboj.ciam.web.model.LoginModel;
import com.cboj.ciam.web.model.OtpModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

import static com.cboj.ciam.consts.ErrorCodes.CLIQ_CONSENT_REQUIRED;

/**
 * Handles authorization-related operations such as client token retrieval, user login, and OTP verification.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationServiceImpl implements AuthenticationService {

    @Value("${keycloak.uri.base-uri}")
    private String keycloakBaseUri;

    @Value("${keycloak.user.custom.attr.customer}")
    private String customerIdAttrName;

    @Value("${keycloak.user.custom.attr.mobile}")
    private String mobileAttrName;

    @Value("${keycloak.user.custom.attr.cliq.consent}")
    private String cliqConsentAttrName;

    @Value("${ciam.consent.login.ttl}")
    private Integer loginTtl;

    @Value("${ciam.encryption.enable}")
    private boolean encryptionEnabled;

    private final Pattern userNamePattern = Pattern.compile("^[a-zA-Z0-9]{7,20}$");

    private final OtpService otpService;
    private final KeycloakClientService keycloakClientService;
    private final KeycloakAdminService keycloakAdminService;
    private final ConsentServiceProvider consentServiceProvider;
    private final EncryptionService encryptionService;
    private final AccountService accountService;

    /**
     * Retrieves a client token using the provided client credentials.
     *
     * @param authorizationDto The client credentials used for authentication.
     * @return The client token obtained from the Keycloak service.
     */
    @Override
    public CompletableFuture<AccessTokenResponse> getClientToken(AuthorizationDto authorizationDto) {
        return CompletableFuture.supplyAsync(() -> keycloakClientService.getClientToken(KeycloakRequest.builder()
                .clientId(authorizationDto.getClientId())
                .clientSecret(authorizationDto.getClientSecret())
                .scope(authorizationDto.getScope())
                .build()));
    }

    /**
     * Constructs a login form based on the provided parameters.
     *
     * @param response_type The response type for the login form.
     * @param client_id     The ID of the client requesting the login form.
     * @param redirect_uri  The redirect URI for the login form.
     * @param scope         The scope for the login form.
     * @param state         The state for the login form.
     * @param consentRef    The consent reference for the login form.
     * @return LoginModel object representing the login form.
     * @throws RuntimeException If any of the parameters are invalid or if the client or consent do not exist.
     */
    @SuppressWarnings("unchecked")
    @Override
    public CompletableFuture<LoginModel> loginForm(String response_type,
                                                   String client_id,
                                                   String redirect_uri,
                                                   String scope,
                                                   String state,
                                                   UUID consentRef) {
        return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(scope))
                .thenCompose(dataService -> {
                    if (!SsoConstants.OAuth2.CODE.equals(response_type)) {
                        throw new RuntimeException("Wrong response_type " + response_type);
                    }
                    if (client_id == null) {
                        throw new RuntimeException("Client id is null");
                    }

                    ClientRepresentation clientRepresentation = keycloakAdminService.getClientRepresentation(client_id);
                    if (clientRepresentation == null) {
                        throw new RuntimeException("No client with id " + client_id);
                    }
                    if (clientRepresentation.getRedirectUris().stream().noneMatch(s -> s.equals(redirect_uri))) {
                        throw new RuntimeException("Wrong redirect_uri " + redirect_uri);
                    }
                    if (clientRepresentation.getOptionalClientScopes().stream().noneMatch(s -> s.equals(scope))) {
                        throw new RuntimeException("Wrong scope " + scope);
                    }

                    return dataService.findByAttr(Consent_.CONSENT_REF, consentRef);
                })
                .thenApply(consentObject -> {

                    Consent consent = (Consent) consentObject;

                    checkConsentExists(consent, consentRef);

                    return LoginModel.builder()
                            .consentRef(consent.getConsentRef())
                            .redirect_uri(redirect_uri)
                            .client_id(client_id)
                            .response_type(response_type)
                            .state(state)
                            .scope(scope)
                            .build();
                });
    }

    /**
     * Logs in a user and returns a UserConsentDto object.
     *
     * @param loginData The UserConsentDto object containing the login data.
     * @param request   The HTTP servlet request.
     * @return A CompletableFuture object that resolves to a UserConsentDto object upon successful login.
     */
    @SuppressWarnings("unchecked")
    @Override
    public CompletableFuture<OtpModel> login(LoginModel loginData, HttpServletRequest request) {
        return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(loginData.getScope()))
                .thenCompose(dataService -> {
                            if (encryptionEnabled) {
                                String decryptedUserName = encryptionService.decryptUserName(loginData);
                                loginData.setUserName(decryptedUserName);
                            }

                            if (!userNamePattern.matcher(loginData.getUserName()).matches()) {
                                throw new RuntimeException("The User name should contain from 7 to 20 alphanumeric characters without spaces");
                            }

                            // Find consent only in NEW status
                            Map<String, Object> attrsData = new HashMap<>();
                            attrsData.put(Consent_.CONSENT_REF, loginData.getConsentRef());
                            attrsData.put(Consent_.STATUS, ConsentStatus.NEW);

                            return dataService.findByAttrs(attrsData);
                        }
                )
                .thenApply(consent -> authorize((Consent) consent, loginData, request))
                .thenCompose(consentData -> {
                    Consent consent = (Consent) consentData;
                    UserRepresentation userRepresentation = getUserRepresentation(loginData, consent);

                    if (SsoConstants.Scope.PAYMENTS.equals(loginData.getScope())) {
                        checkCliqConsent((PisConsentEntity) consent, userRepresentation);
                    }

                    processConsentUserInfo(consent, userRepresentation, request);

                    return consentServiceProvider.getDataService(loginData.getScope()).update(consent)
                            .thenApply(updatedConsent -> {
                                checkDebtorAccountOwnership((Consent) updatedConsent, loginData.getScope());

                                return userRepresentation.firstAttribute(mobileAttrName);
                            });
                })
                .thenApply(mobileNumber -> otpService.send((String) mobileNumber, request))
                .thenApply(sessionObject -> {
                    HttpSession session = (HttpSession) sessionObject;
                    return OtpModel.builder()
                            .consentRef(loginData.getConsentRef())
                            .otpExpirationTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_EXPIRATION_TIME))
                            .otpRenewTime((Long) session.getAttribute(SessionConstants.OTP_SESSION_RENEW_TIME))
                            .otpRequestAttemptsLeft((Integer) session.getAttribute(SessionConstants.REQUEST_ATTEMPT))
                            .otpSubmitAttemptsLeft((Integer) session.getAttribute(SessionConstants.SUBMIT_ATTEMPT))
                            .build();
                });
    }

    /**
     * Logs in a user and returns a UserConsentDto object.
     *
     * @param loginData The UserConsentDto object containing the login data.
     * @param request   The HTTP servlet request.
     * @return UserConsentDto object upon successful login.
     */
    @SuppressWarnings("unchecked")
    @Override
    public CompletableFuture<String> mobileLogin(LoginModel loginData, HttpServletRequest request) {
        return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(loginData.getScope()))
                .thenCompose(dataService -> dataService.findByAttr(Consent_.CONSENT_REF, loginData.getConsentRef()))
                .thenApply(consent -> authorize((Consent) consent, loginData, request))
                .thenCompose(consent -> {
                    String authCode = (String) request.getSession(false).getAttribute(SessionConstants.AUTH_CODE);

                    Consent processed = processUserInfo(loginData, (Consent) consent, request);

                    checkDebtorAccountOwnership(processed, loginData.getScope());

                    processed.setAuthCode(authCode);

                    return consentServiceProvider.getDataService(loginData.getScope()).update(processed);
                })
                .thenApply(consent -> ((Consent) consent).getAuthCode());
    }

    /**
     * Retrieves the user token based on the provided authorization code and HTTP servlet request.
     *
     * @param authorizationDto the authorization data
     * @return User token
     * @throws UnauthorizedException if basic authorization is required but not provided, or if the authorization code is invalid, or if an
     *                               error occurs during the token retrieval process
     */
    @SuppressWarnings("unchecked")
    @Override
    public CompletableFuture<AccessTokenResponse> getUserToken(AuthorizationDto authorizationDto) {
        return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(authorizationDto.getScope()))
                .thenCompose(dataService -> {
                    if (StringUtils.isEmpty(authorizationDto.getRedirectUri())) {
                        throw new NotFoundException("Redirect URI expected but not provided");
                    }
                    return dataService.findByAttr(Consent_.AUTH_CODE, authorizationDto.getAuthCode());
                })
                .thenCompose(consentObject -> {
                    if (consentObject == null) {
                        throw new UnauthorizedException("Authorization code is not valid");
                    }

                    Consent consent = (Consent) consentObject;

                    if (!consent.getClientAppId().equals(authorizationDto.getClientId())) {
                        throw new UnauthorizedException(String.format("Consent does not belong to client %s", authorizationDto.getClientId()));
                    }

                    AccessTokenResponse token = keycloakClientService.getUserToken(KeycloakRequest.builder()
                            .clientId(authorizationDto.getClientId())
                            .clientSecret(authorizationDto.getClientSecret())
                            .redirectUri(authorizationDto.getRedirectUri())
                            .authCode(authorizationDto.getAuthCode())
                            .consentRef(consent.getConsentRef())
                            .build());

                    consent.setRefreshToken(token.getRefreshToken());

                    return consentServiceProvider.getDataService(authorizationDto.getScope()).update(consent)
                            .thenApply(updated -> token);
                });
    }

    /**
     * Refreshes the user token using the provided authorization details.
     *
     * @param authorizationDto The Authorization data.
     * @return Refreshed user token.
     * @throws NotFoundException     If no Consent is found for the given refresh token.
     * @throws UnauthorizedException If the refreshed token cannot be parsed or is invalid.
     */
    @SuppressWarnings("unchecked")
    @Override
    public CompletableFuture<AccessTokenResponse> refreshUserToken(AuthorizationDto authorizationDto) {
        return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(authorizationDto.getScope()))
                .thenCompose(dataService -> {
                    if (StringUtils.isEmpty(authorizationDto.getRefreshToken())) {
                        throw new NotFoundException("Refresh token expected but not provided");
                    }

                    Map<String, Object> attrsData = new HashMap<>();
                    attrsData.put(Consent_.REFRESH_TOKEN, authorizationDto.getRefreshToken());
                    attrsData.put(Consent_.STATUS, ConsentStatus.ACTIVE);

                    return dataService.findByAttrs(attrsData);
                })
                .thenApply(consentObject -> {
                    if (consentObject == null) {
                        throw new NotFoundException("Active Consent not found for given refresh-token");
                    }

                    Consent consent = (Consent) consentObject;

                    AccessTokenResponse token = keycloakClientService.refreshUserToken(KeycloakRequest.builder()
                            .clientId(authorizationDto.getClientId())
                            .clientSecret(authorizationDto.getClientSecret())
                            .consentRef(consent.getConsentRef())
                            .refreshToken(authorizationDto.getRefreshToken())
                            .build());

                    token.setRefreshToken(null);
                    token.setRefreshExpiresIn(0);

                    return token;
                });
    }

    /**
     * Authorizes the given Consent by obtaining an authorization code and updating the consent object.
     *
     * @param consent   The Consent object to be authorized.
     * @param loginData The user login data.
     * @param request   The HTTP servlet request.
     * @return The authorized Consent object.
     * @throws UnauthorizedException If there are any errors during the authorization process.
     */
    private <T extends Consent> T authorize(T consent, LoginModel loginData, HttpServletRequest request) {
        checkConsentExists(consent, loginData.getConsentRef());
        checkConsentExpiration(consent, loginData.getScope());

        // If authCode is present then the user is already authorized
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute(SessionConstants.AUTH_CODE) != null) {
            return consent;
        }

        ResponseEntity<String> userAuthForm = keycloakClientService.getUserAuthForm(KeycloakRequest.builder()
                .clientId(loginData.getClient_id())
                .redirectUri(loginData.getRedirect_uri())
                .scope(loginData.getScope())
                .state(loginData.getState())
                .build());

        String cookie = userAuthForm.getHeaders().getFirst(HttpHeaders.SET_COOKIE);

        if (StringUtils.isEmpty(cookie)) {
            log.error("Missed Header " + HttpHeaders.SET_COOKIE);
            throw new UnauthorizedException("Missed Header " + HttpHeaders.SET_COOKIE);
        }

        if (userAuthForm.getBody() == null) {
            processAuthenticatedConsent(userAuthForm, loginData, request);
        } else {
            processAuthentication(userAuthForm.getBody(), cookie, loginData, request);
        }

        return consent;
    }

    /**
     * Check if the expiration time for the given consent has been reached.
     *
     * @param consent The consent to check for expiration.
     * @param scope   The scope associated with the consent.
     * @throws LoginTimeoutException If the expiration time has been reached.
     */
    @SuppressWarnings("unchecked")
    private <T extends Consent> void checkConsentExpiration(T consent, String scope) {
        if (!LocalDateTime.now().isBefore(consent.getCreateDate().plusMinutes(loginTtl))) {
            consent.setStatus(ConsentStatus.REJECTED);
            consentServiceProvider.getDataService(scope).update(consent);
            throw new LoginTimeoutException("Login process time is up");
        }
    }

    /**
     * Processes the authenticated consent by extracting the authentication URL and authentication code from the response.
     *
     * @param userAuthForm The response entity containing the user authentication form.
     * @param loginData    The model containing login data.
     * @param request      The HttpServletRequest object.
     * @throws UnauthorizedException If the response body and HttpHeaders.LOCATION header are missing.
     * @throws UnauthorizedException If unable to process the authentication URI.
     */
    private void processAuthenticatedConsent(ResponseEntity<String> userAuthForm,
                                             LoginModel loginData,
                                             HttpServletRequest request) {
        Collection<String> locations = userAuthForm.getHeaders().get(HttpHeaders.LOCATION);
        if (CollectionUtils.isEmpty(locations)) {
            throw new UnauthorizedException("Missed response body and Header " + HttpHeaders.LOCATION);
        }
        String authUrl = locations.stream().findFirst()
                .orElseThrow(() -> new UnauthorizedException("Can't process authentication URI"));

        String authCode = UriComponentsBuilder.fromUriString(authUrl).build()
                .getQueryParams()
                .get(SsoConstants.OAuth2.CODE)
                .stream().findFirst()
                .orElseThrow(() -> new UnauthorizedException("Authorization code is empty"));

        processSession(loginData, request.getSession(true), authUrl, authCode);
    }

    /**
     * Processes the authentication request.
     *
     * @param body      The response body received from the server.
     * @param cookie    The cookie received from the server.
     * @param loginData The login data used for authentication.
     * @param request   The HTTP servlet request.
     * @throws UnauthorizedException If authentication fails.
     */
    private void processAuthentication(String body,
                                       String cookie,
                                       LoginModel loginData,
                                       HttpServletRequest request) {
        String authUriOriginal = Jsoup.parse(body)
                .select(RestConstants.Authorization.AUTH_URL_FORM)
                .attr(RestConstants.Authorization.AUTH_URL_ACTION);

        String authUriReal;
        try {
            URI baseUri = new URI(keycloakBaseUri);
            URI authUri = new URI(authUriOriginal);

            authUriReal = UriComponentsBuilder.fromUri(baseUri)
                    .path(authUri.getPath())
                    .replaceQuery(authUri.getQuery())
                    .build().toString();

            log.debug("baseurl: {}, authUrl: {}", baseUri, authUriReal);
        } catch (URISyntaxException e) {
            throw new UnauthorizedException(e.getMessage());
        }

        String sessionCookie = Arrays.stream(cookie.split(";"))
                .filter(c -> c.startsWith(RestConstants.Authorization.AUTH_SESSION_ID))
                .findFirst().orElseThrow(() -> {
                    log.error("Missed cookie with session ID");
                    return new UnauthorizedException("Missed cookie with session ID");
                });

        String password = encryptionEnabled
                ? preparePasswordWithKey(loginData.getPassword(), loginData.getPasswordKey()) : loginData.getPassword();

        URI authUri = keycloakClientService.processUserLogin(KeycloakRequest.builder()
                .userName(loginData.getUserName())
                .password(password)
                .state(loginData.getState())
                .authUrl(authUriReal)
                .sessionCookie(sessionCookie)
                .build());

        Collection<String> params = UriComponentsBuilder.fromUri(authUri).build()
                .getQueryParams()
                .get(SsoConstants.OAuth2.CODE);

        if (CollectionUtils.isEmpty(params)) {
            log.debug("Authorization URI - {}", authUri);
            throw new UnauthorizedException("Authorization code is empty");
        }

        String authCode = params.stream().findFirst()
                .orElseThrow(() -> new UnauthorizedException("Authorization code is empty"));

        processSession(loginData, request.getSession(true), authUri.toString(), authCode);
    }

    /**
     * Gets the user information for consent
     *
     * @param loginData The login data of the user
     * @param consent   The consent information of Consent
     * @return The user information
     */
    private UserRepresentation getUserRepresentation(LoginModel loginData, Consent consent) {
        log.info("Searching userRepresentation by {}",
                consent.getSsoCustomerRef() != null ? consent.getSsoCustomerRef() : loginData.getUserName());
        UserRepresentation userRepresentation = consent.getSsoCustomerRef() != null
                ? keycloakAdminService.getUserRepresentationById(consent.getSsoCustomerRef())
                : keycloakAdminService.getUserRepresentationByName(loginData.getUserName());

        log.info("Got user with attrs: {}", userRepresentation.getAttributes());

        return userRepresentation;
    }

    /**
     * Process user information.
     *
     * @param loginData The UserConsentDto object containing user login information.
     * @param consent   The Consent object representing the user's consent.
     * @param request   The HTTP servlet request.
     * @return Consent object.
     */
    private <T extends Consent> T processUserInfo(LoginModel loginData, T consent, HttpServletRequest request) {
        UserRepresentation userRepresentation = keycloakAdminService.getUserRepresentationByName(loginData.getUserName());
        return processConsentUserInfo(consent, userRepresentation, request);
    }

    /**
     * Process the consent user information. If the customer ID of the consent is null, it sets the customer ID and user ID using the provided
     * user representation and updates the consent using the consent service. Otherwise, it returns the consent
     *
     * @param consent            The consent to process.
     * @param userRepresentation The user representation containing user information.
     * @param request            The HTTP servlet request.
     * @return Updated consent or the original consent.
     */
    private <T extends Consent> T processConsentUserInfo(T consent,
                                                         UserRepresentation userRepresentation,
                                                         HttpServletRequest request) {
        String customerId = userRepresentation.firstAttribute(customerIdAttrName);

        HttpSession session = request.getSession(false);
        if (session != null) {
            session.setAttribute(SessionConstants.CUSTOMER_REF_ATTR, customerId);
            session.setAttribute(SessionConstants.SSO_CUSTOMER_REF_ATTR, userRepresentation.getId());
        }
        consent.setCustomerRef(customerId);
        consent.setSsoCustomerRef(userRepresentation.getId());
        return consent;
    }

    /**
     * Prepares the password by encoding it with the given key using Base64 encoding.
     *
     * @param password the encoded password to prepare
     * @param key      the key to use for encoding
     * @return the prepared password
     */
    private String preparePasswordWithKey(String password, String key) {
        return Base64.getEncoder().encodeToString((password + ":" + key).getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Checks if the Consent exists for the given consentRef.
     *
     * @param consent    The Consent object to check.
     * @param consentRef The consentRef UUID to check.
     * @throws NotFoundException If the Consent is not found for the given consentRef.
     */
    private void checkConsentExists(Consent consent, UUID consentRef) {
        if (consent == null) {
            throw new NotFoundException(String.format("Consent not found for consentRef %s", consentRef));
        }
    }

    /**
     * Processes the session by setting the necessary session attributes.
     *
     * @param loginData The LoginModel containing the login data.
     * @param session   The HttpSession object representing the session.
     * @param authUri   The authentication URI.
     * @param authCode  The authentication code.
     */
    private void processSession(LoginModel loginData,
                                HttpSession session,
                                String authUri,
                                String authCode) {
        session.setAttribute(SessionConstants.REDIRECT_URI, loginData.getRedirect_uri());
        session.setAttribute(SessionConstants.CURRENT_SCOPE_ATTR, loginData.getScope());
        session.setAttribute(SessionConstants.AUTH_URI, authUri);
        session.setAttribute(SessionConstants.AUTH_CODE, authCode);
    }

    /**
     * Checks for the ownership of debtor account by a customer in the given consent. If account not owned by a customer, consents' status
     * will change to Rejected
     *
     * @param consent the consent object to be checked for debtor account ownership
     * @param scope   the scope for data service
     */
    @SuppressWarnings("unchecked")
    private void checkDebtorAccountOwnership(Consent consent, String scope) {
        if (consent instanceof DebtorBound && StringUtils.isNotEmpty(consent.getCustomerRef())) {
            String debtorAccRef = ((DebtorBound) consent).getDebtorAccRef();
            if (StringUtils.isNotEmpty(debtorAccRef)) {

                final AccountScheme schema = ((DebtorBound) consent).getDebtorAccScheme();

                ConsentAccountDto account = accountService.getCustomerAccounts(consent.getCustomerRef())
                        .stream()
                        .filter(a -> {
                            if (AccountScheme.IBAN.equals(schema)) {
                                return a.getIbanNumber().equals(debtorAccRef);
                            } else {
                                return a.getAccountRef().equals(debtorAccRef);
                            }
                        })
                        .findAny()
                        .orElse(null);

                if (account == null) {
                    consent.setStatus(ConsentStatus.REJECTED);
                    consentServiceProvider.getDataService(scope).update(consent);
                    throw new AccountOwnershipException("Account with ID " + debtorAccRef + " not owned by the customer");
                }
            }
        }
    }

    /**
     * Checks if the provided PisConsent object has a local instrument of DOMESTIC_CLIQ and if the corresponding UserRepresentation object has
     * a cliqConsent attribute. If the local instrument is DOMESTIC_CLIQ and the cliqConsent attribute is not found in the UserRepresentation
     * object, a RuntimeException is thrown.
     *
     * @param pisConsentEntity   The PisConsent object.
     * @param userRepresentation The User representation.
     */
    private void checkCliqConsent(PisConsentEntity pisConsentEntity, UserRepresentation userRepresentation) {
        if (LocalInstrument.DOMESTIC_CLIQ.equals(pisConsentEntity.getLocalInstrument())) {
            String cliqConsent = userRepresentation.firstAttribute(cliqConsentAttrName);

            // Expected value is 'true'
            if (!Boolean.parseBoolean(cliqConsent)) {
                throw new ConsentException(CLIQ_CONSENT_REQUIRED, "the user dont grant cliq consent");
            }
        }
    }
}
