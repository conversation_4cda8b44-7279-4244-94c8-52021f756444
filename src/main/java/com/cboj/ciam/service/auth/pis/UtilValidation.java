package com.cboj.ciam.service.auth.pis;

public class UtilValidation {

    public static boolean isNullOrEmpty(String target) {
        if(target == null) return true;
        return target.isEmpty();
    }

    public static boolean isEnglishLetters(String str) {
        return str.matches("^[a-zA-Z]+$");
    }
    public static boolean isEnglishWithSpecialChar(String str) {
        return str.matches("^[a-zA-Z-:().'+ /]+$");
    }

    public static boolean isEnglishWithSpecialCharAndNumaric(String str) {
        return str.matches("^[a-zA-Z0-9-:().'+ /]+$");
    }


    public static boolean isEnglishWithSpecialCharAndSpace(String str) {
        return str.matches("^[a-zA-Z-:().'+ /]+$");
    }

    public static boolean isNumeric(String str) {
        return str != null && str.matches("[0-9]+");
    }

    public static boolean isAlphanumeric(String str) {
        return str != null && str.matches("^[a-zA-Z0-9]+$");
    }

}
