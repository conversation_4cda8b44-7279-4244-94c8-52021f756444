package com.cboj.ciam.service.auth;

import com.cboj.ciam.ApplicationHttpStatusCodeException;
import org.springframework.http.HttpStatus;

/**
 * Exception thrown when customer eligibility validation fails.
 */
public class EligibilityValidationException extends ApplicationHttpStatusCodeException {

  public EligibilityValidationException(String message) {
    super(HttpStatus.FORBIDDEN, message);
  }
}
