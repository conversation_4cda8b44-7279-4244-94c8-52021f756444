package com.cboj.ciam.service.auth.converter;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.api.ais.AisPermissionDto;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisPermission;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.web.model.AisConsentModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Converter class for converting AisConsent entity to AisConsentModel model object.
 */
@Component
@RequiredArgsConstructor
public class AisConsentModelConverter implements EntityModelConverter<AisConsent, AisConsentModel> {

  private final AccountService accountService;

  @Override
  public AisConsentModel apply(AisConsent entity, String customerName) {
    List<ConsentAccountDto> accounts = accountService.getCustomerAccounts(entity.getCustomerRef());

    return AisConsentModel.builder()
        .customerName(customerName)
        .accounts(accounts)
        .consentRef(entity.getConsentRef())
        .clientName(entity.getClientAppName())
        .permissions(entity.getPermissions().stream()
            .map(this::preparePermission)
            .collect(Collectors.toList()))
        .build();
  }

  private AisPermissionDto preparePermission(AisPermission permission) {
    AisPermissionDto dto = new AisPermissionDto();
    dto.setDescriptionEn(permission.getDescriptionEn());
    dto.setDescriptionAr(permission.getDescriptionAr());
    dto.setHandle(permission.getHandle());
    dto.setActive(permission.getActive());
    return dto;
  }
}
