package com.cboj.ciam.service.auth;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.Consent_;
import com.cboj.ciam.service.auth.converter.EntityModelConverterProvider;
import com.cboj.ciam.service.data.ConsentServiceProvider;
import com.cboj.ciam.service.data.SessionService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.representations.UserRepresentation;
import com.cboj.ciam.service.mulesoft.EligibilityService;
import com.cboj.ciam.service.mulesoft.OtpService;
import com.cboj.ciam.service.mulesoft.OtpValidationException;
import com.cboj.ciam.web.model.BaseModel;
import com.cboj.ciam.web.model.OtpModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationOtpServiceImpl implements AuthenticationOtpService {

  @Value("${keycloak.user.custom.attr.mobile}")
  private String mobileAttrName;

  private final OtpService otpService;
  private final SessionService sessionService;
  private final EligibilityService eligibilityService;
  private final ConsentServiceProvider consentServiceProvider;
  private final EntityModelConverterProvider converterProvider;
  private final KeycloakAdminService keycloakAdminService;



  /**
   * Requests OTP (One-Time Password) for the given consent reference.
   *
   * @param consentRef the consent reference
   * @param request    The HTTP servlet request.
   * @return OtpDto if the operation is successful
   * @throws NotFoundException if the AisConsent is not found for the given consent reference
   */
  @SuppressWarnings("unchecked")
  @Override
  public CompletableFuture<HttpSession> requestOtp(UUID consentRef, HttpServletRequest request) {
    return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(request))
        .thenCompose(dataService -> dataService.findByAttr(Consent_.CONSENT_REF, consentRef))
        .thenApply(consentObject -> {
          if (consentObject == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          String ssoCustomerRef = (String) request.getSession(false).getAttribute(SessionConstants.SSO_CUSTOMER_REF_ATTR);

          String mobileNumber = keycloakAdminService
              .getUserRepresentationById(ssoCustomerRef).firstAttribute(mobileAttrName);
          return otpService.send(mobileNumber, request);
        });
  }

  /**
   * This method handles the scenario when OTP verification fails for a given consent reference (consentRef).
   *
   * @param consentRef The UUID representing the consent reference.
   * @param request    The HTTP servlet request.
   * @return String representing the redirect URL associated with the failed consent.
   * @throws NotFoundException if the consent corresponding to the consentRef is not found.
   */
  @SuppressWarnings("unchecked")
  @Override
  public CompletableFuture<String> failedOtp(UUID consentRef, HttpServletRequest request) {
    return CompletableFuture.supplyAsync(() -> consentServiceProvider.getDataService(request))
        .thenCompose(dataService -> dataService.findByAttr(Consent_.CONSENT_REF, consentRef))
        .thenCompose(consentObject -> {
          if (consentObject == null) {
            throw new NotFoundException(String.format("AisConsent not found for consentRef %s", consentRef));
          }

          Consent consent = (Consent) consentObject;

          sessionService.deleteSession(request.getSession(false));

          consent.setStatus(ConsentStatus.REJECTED);
          return consentServiceProvider.getDataService(request).update(consent);
        })
        .thenApply(unused -> {
          HttpSession session = request.getSession(false);
          return session.getAttribute(SessionConstants.REDIRECT_URI);
        });
  }

  /**
   * Processes the OTP (One-Time Password) for user consent.
   *
   * @param otpModel The OTP information including reference number and OTP value.
   * @param request  The HTTP servlet request.
   * @return UserConsentDto if the OTP is validated and the consent is found.
   * @throws UnauthorizedException if OTP validation fails.
   * @throws NotFoundException     if the consent is not found.
   */
  @SuppressWarnings("unchecked")
  @Override
  public <T extends BaseModel> CompletableFuture<T> validateOtp(OtpModel otpModel, HttpServletRequest request) {
      CompletableFuture<T> res =
     CompletableFuture.supplyAsync(() -> otpService.validate(otpModel.getOtp(), request))
        .thenCompose(otpResult -> {
          if (!otpResult) {
            throw new OtpValidationException("OTP validation failed");
          }

          return consentServiceProvider.getDataService(request).findByAttr(Consent_.CONSENT_REF, otpModel.getConsentRef());
        })
        .thenCompose(consentObject -> {
          if (consentObject == null) {
            throw new NotFoundException(
                String.format("AisConsent not found for consentRef %s", otpModel.getConsentRef()));
          }

          HttpSession session = request.getSession(false);
          String customerRef = (String) session.getAttribute(SessionConstants.CUSTOMER_REF_ATTR);

          boolean eligibility = eligibilityService.checkCustomerEligibility(customerRef);

          if (!eligibility) {
            throw new EligibilityValidationException("Eligibility validation failed");
          }

          Consent consent = (Consent) consentObject;
          consent.setCustomerRef(customerRef);
          consent.setSsoCustomerRef((String) session.getAttribute(SessionConstants.SSO_CUSTOMER_REF_ATTR));
          consent.setAuthCode((String) session.getAttribute(SessionConstants.AUTH_CODE));

          return consentServiceProvider.getDataService(request).update(consent);
        }).thenApply(consentObject -> {
          Consent consent = (Consent) consentObject;

          String scope = (String) request.getSession(false).getAttribute(SessionConstants.CURRENT_SCOPE_ATTR);
          UserRepresentation userRepresentation = keycloakAdminService.getUserRepresentationById(consent.getSsoCustomerRef());

                Object apply = converterProvider.getConverter(scope).apply(consent, userRepresentation.getFirstName());
                return apply;
            });
      return res;
  }
}
