package com.cboj.ciam.service.auth.caf;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.caf.CafConsentDto;
import com.cboj.ciam.api.caf.CafOutputConsentDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.jpa.caf.CafConsent_;
import com.cboj.ciam.service.TokenClaimsUtils;
import com.cboj.ciam.service.data.caf.CafConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.cboj.ciam.service.mulesoft.AccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class CafAuthorizationServiceImpl implements CafAuthorizationService {

  private final CafConsentDataService consentService;
  private final KeycloakAdminService keycloakAdminService;
  private final AccountService accountService;

  /**
   * Creates a consent for Account Information Services (AIS) based on the given consent data and the HTTP servlet request.
   *
   * @param consentDto The AIS consent data.
   * @param request    The HTTP servlet request.
   * @return Creating consent response.
   * @throws UnauthorizedException if the token validation fails.
   */
  @Override
  public CompletableFuture<CafOutputConsentDto> createConsent(CafConsentDto consentDto, HttpServletRequest request) {
    String clientId = TokenClaimsUtils.extractClientId(request);

    ClientRepresentation clientRepresentation = keycloakAdminService.getClientRepresentation(clientId);

    consentDto.setClientAppName(clientRepresentation.getName());
    consentDto.setClientAppId(clientId);

    return consentService.createConsentResponse(consentDto, request);
  }

  /**
   * Approves consents for a given consent reference and set of account IDs.
   *
   * @param consentRef The consent reference UUID.
   * @return Result of updating AIS consent.
   */
  @Override
  public CompletableFuture<CafConsent> approveConsents(UUID consentRef, String accountId) {
    return consentService.findByAttr(CafConsent_.CONSENT_REF, consentRef)
        .thenCompose(consent -> {

          checkConsentExists(consent, consentRef);

          if (StringUtils.isEmpty(consent.getDebtorAccRef())) {
            accountService.getCustomerAccounts(consent.getCustomerRef())
                .stream()
                .filter(a -> a.getAccountRef().equals(accountId))
                .findAny()
                .orElseThrow(() -> new NotFoundException("Account ID " + accountId + " not found"));

            if (StringUtils.isNotEmpty(accountId)) {
              consent.setDebtorAccRef(accountId);
              consent.setDebtorAccScheme(AccountScheme.ACCOUNT_NUMBER);
            }
          }

          consent.setStatus(ConsentStatus.ACTIVE);
          return consentService.update(consent);
        });
  }

  /**
   * Declines the consents with the given consent reference.
   *
   * @param consentRef The UUID of the consent to be declined.
   * @return String representing the redirect URL after declining the consent.
   */
  @Override
  public CompletableFuture<CafConsent> declineConsents(@NotNull UUID consentRef) {
    return consentService.findByAttr(CafConsent_.CONSENT_REF, consentRef)
        .thenCompose(consent -> {

          checkConsentExists(consent, consentRef);

          consent.setStatus(ConsentStatus.REJECTED);
          return consentService.update(consent);
        });
  }

  /**
   * Checks if the PisConsent exists for the given consentRef.
   *
   * @param consent    The PisConsent object to check.
   * @param consentRef The consentRef UUID to check.
   * @throws NotFoundException If the PisConsent is not found for the given consentRef.
   */
  private void checkConsentExists(CafConsent consent, UUID consentRef) {
    if (consent == null) {
      throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
    }
  }
}
