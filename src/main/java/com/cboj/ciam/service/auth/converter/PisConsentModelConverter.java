package com.cboj.ciam.service.auth.converter;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.auth.pis.PisAuthorizationServiceImpl;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.web.model.PisConsentOutputModel;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Converter class for converting PisConsent entity to ConsentModel model object.
 */
@Component
@RequiredArgsConstructor
public class PisConsentModelConverter implements EntityModelConverter<PisConsentEntity, PisConsentOutputModel> {

  private final AccountService accountService;
  private final PisAuthorizationServiceImpl pisAuthorizationService;

  @Override
  public PisConsentOutputModel apply(PisConsentEntity entity, String customerName) {
    List<ConsentAccountDto> accounts;
    boolean debtorAccountAvailableInConsent = false;
    if (StringUtils.isEmpty(entity.getDebtorAccRef())) {
      accounts = accountService.getCustomerAccounts(entity.getCustomerRef());
    } else {
      debtorAccountAvailableInConsent = true;
      ConsentAccountDto dto = ConsentAccountDto.builder()
          .accountRef(entity.getDebtorAccRef())
          .listViewEn(entity.getDebtorAccRef())
          .build();
      accounts = Collections.singletonList(dto);
    }


//    if( !debtorAccountAvailableInConsent && entity.getLocalInstrument().equals(LocalInstrument.INTERNAL)) {
//
//      List<ConsentAccountDto> filteredAccounts = accounts.stream()
//              .filter(account -> accountService.isInternalDebitorAccountTypeAllowed(account.getCategory()) &&
//                      (account.getCurrency().equals(AppConstants.Currencies.JOD) || account.getCurrency().equals("USD")) &&
//                      pisAuthorizationService.eligibleLimitForInternalTransfer(account.getCategory(), entity.getAmount()))
//              .collect(Collectors.toList());
//
//      return PisConsentOutputModel.builder()
//              .customerName(customerName)
//              .accounts(filteredAccounts)
//              .consentRef(entity.getConsentRef())
//              .clientName(entity.getClientAppName())
//              .paymentType(entity.getPaymentType().getValue())
//              .build();
//
//    }
//


    return PisConsentOutputModel.builder()
        .customerName(customerName)
        .accounts(accounts)
        .consentRef(entity.getConsentRef())
        .clientName(entity.getClientAppName())
        .paymentType(entity.getPaymentType().getValue())
        .build();
  }
}
