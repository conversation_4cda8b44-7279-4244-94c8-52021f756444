package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;

import javax.validation.constraints.NotNull;

/**
 * Interface to validate PIS (Payment Initiation Service) consent information.
 */
public interface PisConsentValidator {

  void validateConsent(PisConsentDto consentDto);

  LocalInstrument getType();

  /**
   * This method prepares a valid account ID from a raw account ID by either trimming the input to the required length or leaving it
   * unchanged if it is already shorter or equal to the required length.
   *
   * @param rawAccountId The raw account ID to be processed.
   * @return The prepared account ID
   */
  default String prepareAccountId(@NotNull String rawAccountId) {
    return rawAccountId.length() > AppConstants.AccountLength.ACCOUNT_NUMBER
        ? rawAccountId.substring(rawAccountId.length() - AppConstants.AccountLength.ACCOUNT_NUMBER)
        : rawAccountId;
  }

  /**
   * Check if the given account format is correct based on the account schema and account number length.
   *
   * @param accountSchema The schema of the account.
   * @param accountNumber The account number to be validated.
   * @return true if the account format is correct based on the schema and length, false otherwise.
   */
  default boolean isAccountFormatCorrect(@NotNull String accountSchema, String accountNumber) {
    if (AccountScheme.ACCOUNT_NUMBER.getValue().equals(accountSchema)
        && (accountNumber == null || accountNumber.length() != AppConstants.AccountLength.ACCOUNT_NUMBER)) {
      return false;
    }

    return !AccountScheme.IBAN.getValue().equals(accountSchema) || accountNumber.length() == AppConstants.AccountLength.IBAN;
  }

}
