package com.cboj.ciam.service.auth.converter;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.web.model.CafConsentOutputModel;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Converter class for converting CafConsent entity to ConsentModel model object.
 */
@Component
@RequiredArgsConstructor
public class CafConsentModelConverter implements EntityModelConverter<CafConsent, CafConsentOutputModel> {

  private final AccountService accountService;

  @Override
  public CafConsentOutputModel apply(CafConsent entity, String customerName) {
    List<ConsentAccountDto> accounts;
    if (StringUtils.isEmpty(entity.getDebtorAccRef())) {
      accounts = accountService.getCustomerAccounts(entity.getCustomerRef());
    } else {
      ConsentAccountDto dto = ConsentAccountDto.builder()
          .accountRef(entity.getDebtorAccRef())
          .listViewEn(entity.getDebtorAccRef())
          .build();
      accounts = Collections.singletonList(dto);
    }

    return CafConsentOutputModel.builder()
        .customerName(customerName)
        .consentRef(entity.getConsentRef())
        .clientName(entity.getClientAppName())
        .accounts(accounts)
        .expirationDateTime(entity.getExpiryDate())
        .build();
  }
}
