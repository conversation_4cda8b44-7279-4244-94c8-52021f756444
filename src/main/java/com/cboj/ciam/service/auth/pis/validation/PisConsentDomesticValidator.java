package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.config.CountryConfig;
import com.cboj.ciam.config.PaymentServiceProperties;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PaymentPurposeEntity;
import com.cboj.ciam.service.data.pis.PaymentPurposeCodeService;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.service.mulesoft.SwiftRefService;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoResponse;
import com.cboj.ciam.service.mulesoft.pis.ExchangeAmountDto;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.ValidationException;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.cboj.ciam.service.auth.pis.UtilValidation.*;

@Slf4j
@Component
public class PisConsentDomesticValidator extends PisConsentBaseValidator implements PisConsentValidator {

    private final AccountService accountService;
    private final SwiftRefService swiftRefService;
    private final PaymentPurposeCodeService purposeCodeService;
    private final PaymentServiceProperties paymentServiceProperties;
    private final TransactionInfoService transactionInfoService;

    private Set<String> currenciesSet;
    private final List<String> countryCodes;
    private final Mapper<PisConsentDto, TransactionInfoRequest> transactionInfoMapper;

    public PisConsentDomesticValidator(TransactionInfoService transactionInfoService,
                                       AccountService accountService,
                                       SwiftRefService swiftRefService,
                                       PaymentPurposeCodeService purposeCodeService,
                                       PaymentServiceProperties paymentServiceProperties,
                                       CountryConfig countryConfig,
                                       Mapper<PisConsentDto, TransactionInfoRequest> transactionInfoMapper) {
        super(transactionInfoService);
        this.accountService = accountService;
        this.swiftRefService = swiftRefService;
        this.purposeCodeService = purposeCodeService;
        this.paymentServiceProperties = paymentServiceProperties;
        this.countryCodes = countryConfig.getCodes();
        this.transactionInfoService = transactionInfoService;
        this.transactionInfoMapper = transactionInfoMapper;
    }

    @PostConstruct
    public void init() {
        sameCurrencyLimit = paymentServiceProperties.getLimitJodTransfer();
        fxLimit = paymentServiceProperties.getLimitFxTransfer();
        currenciesSet = processPropertiesCollection(paymentServiceProperties.getDomesticCurrencies());
    }

    @Override
    public void validateConsent(PisConsentDto pisConsentDto) {
        // Only IBAN scheme is acceptable
        if (!AccountScheme.IBAN.getValue().equals(pisConsentDto.getCreditorAccScheme())) {
            throw new ValidationException("Creditor account scheme is not " + AccountScheme.IBAN.getValue());
        }

        validateAmount(pisConsentDto.getCurrency(), pisConsentDto.getAmount());

        String instructedAmountCurrency = pisConsentDto.getCurrency();

        // Validate whether currency is allowed
        // If nothing is configured then all currencies are allowed.
        if (currenciesSet != null && !currenciesSet.contains(instructedAmountCurrency)) {
            log.error("InstructedAmount currency {} not allowed", instructedAmountCurrency);
            throw new ValidationException("Currency not allowed");
        }

        String preparedDebtorAccountId = prepareAccountId(pisConsentDto.getDebtorAccRef());
        AccountDto debAccountInfo = accountService.getAccountDetails(preparedDebtorAccountId);

        if (debAccountInfo == null) {
            throw new ValidationException("Debtor Account not found");
        }


        if (!isAmountWithinLimitDomestic(debAccountInfo.getCurrency(), instructedAmountCurrency, pisConsentDto.getAmount())) {
            log.error("Transfer amount greater than transfer limit");
            throw new ValidationException("Transaction limit exceeded");
        }


        String creditorIban = pisConsentDto.getCreditorAccRef();
        if (StringUtils.isEmpty(creditorIban)) {
            throw new ValidationException("Creditor account ref is empty");
        }

        boolean ibanValidation = swiftRefService.validateIban(pisConsentDto);
        if (!ibanValidation) {
            throw new ValidationException("IBAN validation failed");
        }

        // Validate Beneficiary data
        validateBeneficiaryData(pisConsentDto);

        if (isNullOrEmpty(pisConsentDto.getChargeType())) {
            throw new ValidationException("Charge Type is mandatory");
        }
        if (!pisConsentDto.getChargeType().trim().equals("sender"))
            throw new ValidationException("Charge Type should always equals sender");

        String agentBicCode = pisConsentDto.getAgentIdentification();

        String ibanBicCode = swiftRefService.getBicCode(pisConsentDto.getCreditorAccRef());
        if (StringUtils.isEmpty(ibanBicCode)) {
            throw new NotFoundException("BIC code not found");
        }
        if (ibanBicCode.equals(AppConstants.CAPITAL_BIC_CODE)) {
            throw new NotFoundException("The creditor account is belong to Capital bank");
        }

        if (StringUtils.isEmpty(agentBicCode)) {
            pisConsentDto.setAgentIdentification(ibanBicCode);
            pisConsentDto.setAgentSchema(AppConstants.BIC_CODE);
        } else if (!agentBicCode.equals(ibanBicCode)) {
            log.error("BIC from IBAN {} does not match with provided BIC from Agent {}", ibanBicCode, agentBicCode);
            throw new ValidationException("BIC does not match");
        }

        isCategoryPurposeCodeValid(pisConsentDto);

        //Hussein: to check the payment against T24, to check if non-STP, exceed limit or rejected for any other then to reject also from Open Banking side
        validatePaymentAgainstCore(pisConsentDto);
    }

    private void validatePaymentAgainstCore(PisConsentDto pisConsentDto) {

        TransactionInfoRequest transactionInfoRequest = transactionInfoMapper.apply(pisConsentDto);

        try {
            TransactionInfoResponse transactionInfoResponse = accountService.validatePaymentAgainstCore(transactionInfoRequest);
//            if ("INAU INAO".contains(transactionInfoResponse.getTransactionCharges().getRecordStatus())) {
//                throw new ValidationException("The transaction validation against core failed");
//            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public LocalInstrument getType() {
        return LocalInstrument.DOMESTIC;
    }

    /**
     * Check whether purpose code and category code is available in the database .
     *
     * @param consentDto The PisConsentDto
     */
    private void isCategoryPurposeCodeValid(PisConsentDto consentDto) {
        String categoryCode = consentDto.getCategoryPurposeCode();
        String purposeCode = consentDto.getPaymentPurposeCode();
        log.debug("isCategoryPurposeCodeValid: Category  code {} and purpose code {} ", categoryCode, purposeCode);

        if ((categoryCode == null || !categoryCode.equals(categoryCode.trim())) //Hussein: what is this conditions! why compare with same variable with trim?!
                || (purposeCode == null || !purposeCode.equals(purposeCode.trim()))) {
            log.error("payment category code {} and purpose code {} not correct ", categoryCode, purposeCode);
            throw new ValidationException("categoryPurposeCode and paymentPurposeCode mandatory");
        }

        Optional<PaymentPurposeEntity> result = purposeCodeService.getByCategoryAndPurpose(categoryCode, purposeCode);
        /// Check categoryPurposeCode and paymentPurposeCode combination is allowed
        if (!result.isPresent()) {
            log.error("payment category code {} and purpose code {} not correct ", categoryCode, purposeCode);
            throw new ValidationException("Invalid categoryPurposeCode/paymentPurposeCode or Combination");
        }
        consentDto.setPaymentPurposeDesc(result.get().getDescriptionPaymentPurpose());
    }

    private void validateBeneficiaryData(@NotNull PisConsentDto consentDto) {

        String firstName = consentDto.getBeneficiaryFirstName();
        String middleName = consentDto.getBeneficiaryMiddleName();
        String lastName = consentDto.getBeneficiaryLastName();

        if (isNullOrEmpty(firstName) || isNullOrEmpty(middleName)
                || isNullOrEmpty(lastName)) {
            throw new ValidationException("Beneficiary Name First, Middle & Last name is mandatory for Domestic payment");
        }

        if (!isEnglishWithSpecialChar(firstName + middleName + lastName)) {
            throw new ValidationException("Beneficiary name should only in English letters or / -: (). ‘ +");
        }

        if (firstName.length() < 3) {
            throw new ValidationException("Beneficiary FirstName should be more than 3 characters");
        }
        if (middleName.length() < 3) {
            throw new ValidationException("Beneficiary MiddleName should be more than 3 characters");
        }
        if (lastName.length() < 3) {
            throw new ValidationException("Beneficiary LastName should be more than 3 characters");
        }

        if ((firstName.length() + middleName.length() + lastName.length()) > 60) {
            throw new ValidationException("Beneficiary names should not exceed 60 characters");
        }


        //validate Street Name
        if (isNullOrEmpty(consentDto.getStreetName()))
            throw new ValidationException("Beneficiary StreetName is mandatory");

        if (consentDto.getStreetName().length() > 15)
            throw new ValidationException("Beneficiary StreetName should not exceed 15");

        if (!isEnglishWithSpecialCharAndNumaric(consentDto.getStreetName()))
            throw new ValidationException("Beneficiary StreetName accept alphanumeric");


        if (!isNullOrEmpty(consentDto.getBuildingNumber())) {
            if (!isEnglishWithSpecialCharAndNumaric(consentDto.getBuildingNumber())) {
                throw new ValidationException("BuildingNumber should be Alphanumeric");
            }
            if (consentDto.getBuildingNumber().length() > 15) {
                throw new ValidationException("BuildingNumber should not exceed 15 digit");
            }
        }

        //validate Town Name
        if (isNullOrEmpty(consentDto.getTownName())) {
            throw new ValidationException("Beneficiary townName is mandatory");
        }
        if (!isEnglishWithSpecialCharAndNumaric(consentDto.getTownName())) {
            throw new ValidationException("Beneficiary townName should be Alphanumeric");
        }
        if (consentDto.getTownName().length() >= 28) {
            throw new ValidationException("Town Name should not exceed 28 digit");
        }

        //validate beneficiary country code
        if (isNullOrEmpty(consentDto.getCreditorCountryCode())) {
            throw new ValidationException("Beneficiary residenceCountry.countryCode is mandatory");
        }
//        log.info("countryCodes" + countryCodes);
//        log.info("consentDto.getCreditorCountryCode()):" + consentDto.getCreditorCountryCode());
        if (!countryCodes.contains(consentDto.getCreditorCountryCode()))
            throw new ValidationException("Beneficiary residenceCountry.countryCode not valid");

        //validate Payment reason
        if (!isNullOrEmpty(consentDto.getPaymentReason())) {
            if (consentDto.getPaymentReason().length() > 95)
                throw new ValidationException("Payment reason should not exceed 95 characters");

            if (!isEnglishWithSpecialCharAndSpace(consentDto.getPaymentReason()))
                throw new ValidationException("Payment reason is should be English or /-:().'+ ");

        }
    }

    protected boolean isAmountWithinLimitDomestic(String debitAccountCurrency, String instructedAmountCurrency, double instructedAmount) {
        PaymentCurrency debitAccountCurrencyValue = PaymentCurrency.valueOf(debitAccountCurrency);
        PaymentCurrency instructedAmountCurrencyValue = PaymentCurrency.valueOf(instructedAmountCurrency);
        boolean isConvertToJODNeeded = !instructedAmountCurrencyValue.equals(PaymentCurrency.JOD);
        boolean isFXTransfer = !instructedAmountCurrencyValue.equals(debitAccountCurrencyValue);
        Integer transferLimit = isFXTransfer ? fxLimit : sameCurrencyLimit;

        double amountInJOD= 0 ;
        if (isConvertToJODNeeded) {
            ExchangeAmountDto exchangeAmountDto = transactionInfoService.getExchangeRate(PaymentCurrency.JOD.name(),
                    instructedAmountCurrencyValue.name(),
                    instructedAmount,
                    instructedAmountCurrencyValue.name());
            amountInJOD = exchangeAmountDto.getExchangedAmount();
        }

        log.debug("isAmountWithinLimitDomestic() : instructedAmount {} debitAccountCurrencyValue {} and transferLimit {} amountInJod {}",
                instructedAmount, debitAccountCurrencyValue, transferLimit, amountInJOD);

        return transferLimit >= amountInJOD;
    }
}
