package com.cboj.ciam.service.auth.pis;

import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.jpa.pis.PisConsentEntity;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Interface provides methods for authorization and user consent management.
 */
public interface PisAuthorizationService {

  CompletableFuture<PisOutputConsentDto> createConsent(@NotNull PisConsentDto consentDto, HttpServletRequest request);

  CompletableFuture<PisConsentEntity> approveConsents(@NotEmpty UUID consentRef, String accountId);

  CompletableFuture<PisConsentEntity> declineConsents(@NotNull UUID consentRef);
}
