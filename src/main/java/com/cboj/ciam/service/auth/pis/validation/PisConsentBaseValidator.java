package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.config.MulesoftServiceProperties;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.service.keycloak.KeycloakClientService;
import com.cboj.ciam.service.mulesoft.MulesoftService;
import com.cboj.ciam.service.mulesoft.pis.ExchangeAmountDto;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.validation.ValidationException;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public abstract class PisConsentBaseValidator {

    private final TransactionInfoService transactionInfoService;

    protected Integer sameCurrencyLimit;
    protected Integer fxLimit;


    /**
     * Processes a comma-separated property collection and creates a set of trimmed strings from it.
     *
     * @param propertyCollection the comma-separated string containing properties
     * @return a Set of trimmed strings extracted from propertyCollection, or null if propertyCollection is empty or null
     */
    protected Set<String> processPropertiesCollection(String propertyCollection) {
        return StringUtils.isNotEmpty(propertyCollection)
                ? Arrays.stream(propertyCollection.split(","))
                .map(String::trim)
                .collect(Collectors.toSet())
                : null;
    }

    /**
     * Check whether transfer is possible for the specified category and transfer amount .
     *
     * @param debitAccountCurrency  Currency from
     * @param creditAccountCurrency Currency to
     * @param transferAmount        amount to be transferred.
     * @return boolean true if transferAmount is less than the limit.
     */
    protected boolean isAmountWithinLimit(String debitAccountCurrency, String creditAccountCurrency, double transferAmount) {
        PaymentCurrency paymentInboundCurrency = PaymentCurrency.valueOf(debitAccountCurrency);
        PaymentCurrency paymentOutboundCurrency = PaymentCurrency.valueOf(creditAccountCurrency);

        Integer transferLimit = paymentOutboundCurrency.equals(paymentInboundCurrency) ? sameCurrencyLimit : fxLimit;
        double fxAmount = transferAmount;

        if (!PaymentCurrency.JOD.equals(paymentInboundCurrency)) {
            ExchangeAmountDto exchangeAmountDto = transactionInfoService.getExchangeRate(paymentInboundCurrency.name(), PaymentCurrency.JOD.name(),
                    transferAmount, PaymentCurrency.JOD.name());
            fxAmount = exchangeAmountDto.getExchangedAmount();
        }

        log.debug("eligibleLimitForInternalTransfer: Transfer amount {} currency {} fx amount {} and Limit {}",
                transferAmount, paymentInboundCurrency, fxAmount, transferLimit);

        return transferLimit >= fxAmount;
    }

    protected void validateAmount(String currency, double amount) {
        if (amount <= 0) {
            throw new ValidationException("Amount must be greater than 0");
        }

    }
}
