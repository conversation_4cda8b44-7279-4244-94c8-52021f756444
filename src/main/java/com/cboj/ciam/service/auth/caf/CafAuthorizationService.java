package com.cboj.ciam.service.auth.caf;

import com.cboj.ciam.api.caf.CafConsentDto;
import com.cboj.ciam.api.caf.CafOutputConsentDto;
import com.cboj.ciam.jpa.caf.CafConsent;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Interface provides methods for authorization and user consent management.
 */
public interface CafAuthorizationService {

  CompletableFuture<CafOutputConsentDto> createConsent(@NotNull CafConsentDto consentDto, HttpServletRequest request);

  CompletableFuture<CafConsent> approveConsents(@NotEmpty UUID consentRef, String accountId);

  CompletableFuture<CafConsent> declineConsents(@NotNull UUID consentRef);
}
