package com.cboj.ciam.service.auth.pis;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.TokenClaimsUtils;
import com.cboj.ciam.service.auth.pis.validation.PisConsentValidationService;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import com.cboj.ciam.service.keycloak.KeycloakAdminService;
import com.cboj.ciam.service.keycloak.representations.ClientRepresentation;
import com.cboj.ciam.service.mulesoft.AccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class PisAuthorizationServiceImpl implements PisAuthorizationService {

  private final PisConsentDataService consentService;
  private final KeycloakAdminService keycloakAdminService;
  private final AccountService accountService;
  private final PisConsentValidationService consentValidationService;

  /**
   * Creates a consent for Account Information Services (AIS) based on the given consent data and the HTTP servlet request.
   *
   * @param pisConsentDto The AIS consent data.
   * @param request    The HTTP servlet request.
   * @return Creating consent response.
   * @throws UnauthorizedException if the token validation fails.
   */
  @Override
  public CompletableFuture<PisOutputConsentDto> createConsent(PisConsentDto pisConsentDto, HttpServletRequest request) {
    LocalInstrument instrument = LocalInstrument.getFromValue(pisConsentDto.getLocalInstrument());

    // validate consent contents before proceeding consent creation.
    consentValidationService.validateConsent(instrument, pisConsentDto);

    String clientId = TokenClaimsUtils.extractClientId(request);

    ClientRepresentation clientRepresentation = keycloakAdminService.getClientRepresentation(clientId);

    pisConsentDto.setClientAppName(clientRepresentation.getName());
    pisConsentDto.setClientAppId(clientId);

    return consentService.createConsentResponse(pisConsentDto, request);
  }

  /**
   * Approves the consents for a given consent reference and account ID.
   *
   * @param consentRef The unique identifier for the consent.
   * @param accountId  The identifier for the account.
   * @return Representing the approval of the consents.
   * @throws NotFoundException If the account ID is not found.
   */
  @Override
  public CompletableFuture<PisConsentEntity> approveConsents(UUID consentRef, String accountId) {
    return consentService.findByAttr(PisConsentEntity_.CONSENT_REF, consentRef)
        .thenCompose(consent -> {

          checkConsentExists(consent, consentRef);

          if (StringUtils.isEmpty(consent.getDebtorAccRef())) {
            accountService.getCustomerAccounts(consent.getCustomerRef())
                .stream()
                .filter(a -> a.getAccountRef().equals(accountId))
                .findAny()
                .orElseThrow(() -> new NotFoundException("Account ID " + accountId + " not found"));

            if (StringUtils.isNotEmpty(accountId)) {
              consent.setDebtorAccRef(accountId);
              consent.setDebtorAccScheme(AccountScheme.ACCOUNT_NUMBER);
            }
          }

          consent.setStatus(ConsentStatus.ACTIVE);
          return consentService.update(consent);
        });
  }

  /**
   * Declines the consents with the given consent reference.
   *
   * @param consentRef The UUID of the consent to be declined.
   * @return String representing the redirect URL after declining the consent.
   */
  @Override
  public CompletableFuture<PisConsentEntity> declineConsents(@NotNull UUID consentRef) {
    return consentService.findByAttr(PisConsentEntity_.CONSENT_REF, consentRef)
        .thenCompose(consent -> {

          checkConsentExists(consent, consentRef);

          consent.setStatus(ConsentStatus.REJECTED);
          return consentService.update(consent);
        });
  }

  /**
   * Checks if the PisConsent exists for the given consentRef.
   *
   * @param consent    The PisConsent object to check.
   * @param consentRef The consentRef UUID to check.
   * @throws NotFoundException If the PisConsent is not found for the given consentRef.
   */
  private void checkConsentExists(PisConsentEntity consent, UUID consentRef) {
    if (consent == null) {
      throw new NotFoundException(String.format("PisConsent not found for consentRef %s", consentRef));
    }
  }
}
