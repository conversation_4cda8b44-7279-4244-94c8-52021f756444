package com.cboj.ciam.service.auth;

import com.cboj.ciam.api.AuthorizationDto;
import com.cboj.ciam.service.keycloak.representations.AccessTokenResponse;
import com.cboj.ciam.web.model.LoginModel;
import com.cboj.ciam.web.model.OtpModel;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public interface AuthenticationService {

  CompletableFuture<AccessTokenResponse> getClientToken(@NotNull AuthorizationDto authorizationDto);

  CompletableFuture<LoginModel> loginForm(String response_type, String client_id, String redirect_uri, String scope, String state,
                                          UUID consentRef);

  CompletableFuture<String> mobileLogin(@NotNull LoginModel loginData, HttpServletRequest request);

  CompletableFuture<OtpModel> login(@NotNull LoginModel loginData, HttpServletRequest request);

  CompletableFuture<AccessTokenResponse> getUserToken(@NotNull AuthorizationDto authorizationDto);

  CompletableFuture<AccessTokenResponse> refreshUserToken(@NotNull AuthorizationDto authorizationDto);

}
