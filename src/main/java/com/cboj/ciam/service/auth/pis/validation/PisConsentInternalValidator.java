package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.config.PaymentServiceProperties;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PaymentPurposeEntity;
import com.cboj.ciam.service.data.pis.PaymentPurposeCodeService;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoRequest;
import com.cboj.ciam.service.mulesoft.dto.TransactionInfoResponse;
import com.cboj.ciam.service.mulesoft.pis.ExchangeAmountDto;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.ValidationException;
import javax.validation.constraints.NotNull;
import java.util.Optional;
import java.util.Set;

import static com.cboj.ciam.service.auth.pis.UtilValidation.isNullOrEmpty;

@Slf4j
@Component
public class PisConsentInternalValidator extends PisConsentBaseValidator implements PisConsentValidator {

    private final AccountService accountService;
    private final PaymentPurposeCodeService purposeCodeService;
    private final PaymentServiceProperties paymentServiceProperties;

    private Set<String> currenciesSet;
    private Set<String> allowedCreditorAccountTypes;
    private Set<String> allowedDebtorAccountTypes;
    private final TransactionInfoService transactionInfoService;
    private final Mapper<PisConsentDto, TransactionInfoRequest> transactionInfoMapper;

    public PisConsentInternalValidator(AccountService accountService,
                                       PaymentPurposeCodeService purposeCodeService,
                                       PaymentServiceProperties paymentServiceProperties,
                                       TransactionInfoService transactionInfoService,
                                       Mapper<PisConsentDto, TransactionInfoRequest> transactionInfoMapper) {
        super(transactionInfoService);
        this.accountService = accountService;
        this.purposeCodeService = purposeCodeService;
        this.paymentServiceProperties = paymentServiceProperties;
        this.transactionInfoService = transactionInfoService;
        this.transactionInfoMapper = transactionInfoMapper;
    }

    @PostConstruct
    public void init() {
        sameCurrencyLimit = paymentServiceProperties.getLimitJodTransfer();
        fxLimit = paymentServiceProperties.getLimitFxTransfer();
        currenciesSet = processPropertiesCollection(paymentServiceProperties.getInternalCurrencies());
        allowedCreditorAccountTypes = processPropertiesCollection(paymentServiceProperties.getInternalCreditorAccountType());
        allowedDebtorAccountTypes = processPropertiesCollection(paymentServiceProperties.getInternalDebtorAccountType());
    }

    @Override
    public void validateConsent(PisConsentDto pisConsentDto) {

        String instructedAmountCurrency = pisConsentDto.getCurrency();

        // Validate whether currency is allowed
        // If nothing is configured then all currencies are allowed.
        if (currenciesSet != null && !currenciesSet.contains(instructedAmountCurrency)) {
            log.error("InstructedAmount currency {} not allowed", instructedAmountCurrency);
            throw new ValidationException("Currency not allowed");
        }

        validateAccountFormatAndScheme(pisConsentDto);

        // validateAmount
        validateAmount(pisConsentDto.getCurrency(), pisConsentDto.getAmount());

        String preparedCreditorAccountId = prepareAccountId(pisConsentDto.getCreditorAccRef());
        String preparedDebtorAccountId = prepareAccountId(pisConsentDto.getDebtorAccRef());

        if (preparedCreditorAccountId.equals(preparedDebtorAccountId)) {
            log.error("Debtor account {} and Creditor account {} same ", pisConsentDto.getDebtorAccRef(), pisConsentDto.getCreditorAccRef());
            throw new ValidationException("Same Creditor and Debtor Account");
        }

        validatePurposeOfPayment(pisConsentDto);

        /// Check Creditor Account
        AccountDto creditAccountDto = accountService.getAccountDetails(preparedCreditorAccountId);
        validateCreditorAccount(creditAccountDto);

        //Set creditor account holder name
        pisConsentDto.setCreditorNameEn(creditAccountDto.getAccountHolderNameEn());
        pisConsentDto.setCreditorNameAr(creditAccountDto.getAccountHolderNameAr());

        /// Check Debtor Account
        AccountDto debtorAccountInfo = accountService.getAccountDetails(preparedDebtorAccountId);
        validateDebtorAccount(pisConsentDto, debtorAccountInfo);


        if (!isAmountWithinLimitInternal(debtorAccountInfo.getCurrency(), creditAccountDto.getCurrency(), instructedAmountCurrency, pisConsentDto.getAmount())) {
            log.error("Transfer amount greater than transfer limit");
            throw new ValidationException("Internal Transfer, Transaction amount limit exceeded");
        }

        pisConsentDto.setCreditorCurrency(creditAccountDto.getCurrency());

        validateAgainstCore(pisConsentDto);
    }

    private void validateAgainstCore(PisConsentDto pisConsentDto) {
        TransactionInfoRequest transactionInfoRequest = transactionInfoMapper.apply(pisConsentDto);

        log.info(transactionInfoRequest.toString());

        try {
            TransactionInfoResponse transactionInfoResponse = accountService.validatePaymentAgainstCore(transactionInfoRequest);
//            if (!isNullOrEmpty(transactionInfoResponse.getTransactionCharges().getRecordStatus())
//                    && "INAU INAO".contains(transactionInfoResponse.getTransactionCharges().getRecordStatus())) {
//                throw new ValidationException("The transaction validation against core failed");
//            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public LocalInstrument getType() {
        return LocalInstrument.INTERNAL;
    }

    /**
     * Check whether purpose code and category code is available in the database .
     *
     * @param pisConsentDto
     */
    private void validatePurposeOfPayment(PisConsentDto pisConsentDto) {
        String categoryCode = pisConsentDto.getCategoryPurposeCode();
        String purposeCode = pisConsentDto.getPaymentPurposeCode();
        log.debug("isCategoryPurposeCodeValid: Category  code {} and purpose code {} ", categoryCode, purposeCode);

        if (!categoryCode.equals(categoryCode.trim()) || !purposeCode.equals(purposeCode.trim())) {
            log.error("payment category code {} and purpose code {} not correct ", categoryCode, purposeCode);
            throw new ValidationException("Invalid categoryPurposeCode/paymentPurposeCode or Combination");
        }

        Optional<PaymentPurposeEntity> result = purposeCodeService.getByCategoryAndPurpose(categoryCode, purposeCode);
        /// Check categoryPurposeCode and paymentPurposeCode combination is allowed
        if (!result.isPresent()) {
            log.error("payment category code {} and purpose code {} not correct ", categoryCode, purposeCode);
            throw new ValidationException("Invalid categoryPurposeCode/paymentPurposeCode or Combination");
        }

        pisConsentDto.setPaymentPurposeDesc(result.get().getDescriptionPaymentPurpose());
    }

    /**
     * Validates the account format and scheme for the given PisConsentDto object.
     *
     * @param consentDto The PisConsentDto object containing the account information
     */
    private void validateAccountFormatAndScheme(PisConsentDto consentDto) {
        String creditorAccSchema = consentDto.getCreditorAccScheme();

        if (!AccountScheme.IBAN.getValue().equals(creditorAccSchema)
                && !AccountScheme.ACCOUNT_NUMBER.getValue().equals(creditorAccSchema)) {
            log.error("Wrong creditor scheme {}  ", creditorAccSchema);
            throw new ValidationException("Wrong Creditor account scheme");
        }

        String debtorAccSchema = consentDto.getDebtorAccScheme();

        if (!AccountScheme.IBAN.getValue().equals(debtorAccSchema)
                && !AccountScheme.ACCOUNT_NUMBER.getValue().equals(debtorAccSchema)) {
            log.error("Wrong Debtor scheme {}  ", debtorAccSchema);
            throw new ValidationException("Wrong Debtor account scheme");
        }

        if (!isAccountFormatCorrect(debtorAccSchema, consentDto.getDebtorAccRef())) {
            log.error("Wrong Debtor account {}  ", consentDto.getDebtorAccRef());
            throw new ValidationException("Wrong Debtor account");
        }

        if (!isAccountFormatCorrect(creditorAccSchema, consentDto.getCreditorAccRef())) {
            log.error("Wrong Creditor account {}  ", consentDto.getCreditorAccRef());
            throw new ValidationException("Wrong Creditor account");
        }
    }

    /**
     * Validates the debtor account information against the consent DTO.
     *
     * @param consentDto     The consent DTO containing the currency and other related information
     * @param debAccountInfo The debtor account information to be validated
     */
    private void validateDebtorAccount(@NotNull PisConsentDto consentDto, AccountDto debAccountInfo) {
        /// Check Debtor Account
        if (debAccountInfo == null) {
            throw new ValidationException("Debtor Account not found");
        }

        // Ensure debtor account currency is matching with instructed currency
        if (!debAccountInfo.getCurrency().equals(consentDto.getCurrency())) {
            throw new ValidationException("Debtor currency not matching with requested instructedAmount currency");
        }

        // Category check
        if (allowedDebtorAccountTypes != null && !allowedDebtorAccountTypes.contains(debAccountInfo.getCategory())) {
            log.error("Debtor Account Category {} not allowed for internal payment ", debAccountInfo.getCategory());
            throw new ValidationException("Debtor account Category not eligible");
        }
    }

    /**
     * Validates the creditor account existence and its category is allowed.
     *
     * @param credAccountDto The AccountDto of the creditor account to be validated.
     */
    private void validateCreditorAccount(AccountDto credAccountDto) {
        /// Check Creditor Account
        if (credAccountDto == null) {
            throw new ValidationException("Creditor Account not found");
        }

        // If nothing is configured then all category type is allowed.
        if (allowedCreditorAccountTypes != null && !allowedCreditorAccountTypes.contains(credAccountDto.getCategory())) {
            log.error("Creditor Account Category {} not allowed for internal payment ", credAccountDto.getCategory());
            throw new ValidationException("Creditor account Category not eligible");
        }
    }

    protected boolean isAmountWithinLimitInternal(String debtorAccountCurrency, String creditorAccountCurrency, String instructedAmountCurrency, double instructedAmount) {
        PaymentCurrency debtorAccountCurrencyValue = PaymentCurrency.valueOf(debtorAccountCurrency);
        PaymentCurrency creditorAccountCurrencyValue = PaymentCurrency.valueOf(creditorAccountCurrency);
        PaymentCurrency instructedAmountCurrencyValue = PaymentCurrency.valueOf(instructedAmountCurrency);

        boolean isFx = !debtorAccountCurrencyValue.equals(creditorAccountCurrencyValue);
        Integer transferLimit = isFx ? fxLimit : sameCurrencyLimit;

        if (!instructedAmountCurrencyValue.name().equals(AppConstants.Currencies.JOD)) {
            ExchangeAmountDto exchangeAmountDto = transactionInfoService.getExchangeRate(
                    AppConstants.Currencies.JOD,
                    instructedAmountCurrencyValue.name(),
                    instructedAmount,
                    instructedAmountCurrencyValue.name());
            instructedAmount = exchangeAmountDto.getExchangedAmount();
        }

        log.debug("isAmountWithinLimitDomestic() : instructedAmount {} debitAccountCurrencyValue {} and transferLimit {}",
                instructedAmount, debtorAccountCurrencyValue, transferLimit);

        return transferLimit >= instructedAmount;
    }
}
