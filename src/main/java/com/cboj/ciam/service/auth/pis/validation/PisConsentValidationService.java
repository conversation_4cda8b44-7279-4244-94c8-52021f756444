package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PisConsentValidationService {

  private final Map<LocalInstrument, PisConsentValidator> validators = new HashMap<>();

  public PisConsentValidationService(List<PisConsentValidator> validatorsList) {
    validatorsList.forEach(v -> validators.put(v.getType(), v));
  }

  /**
   * Validates consent based on the provided LocalInstrument type.
   *
   * @param instrument the LocalInstrument type to determine the validation logic
   * @param consent    the PisConsentDto object to be validated
   */
  public void validateConsent(@NotNull LocalInstrument instrument, @NotNull PisConsentDto consent) {
    log.debug("PisConsentValidationService.validateConsent(): consent : {}", consent);
//    log.info("PisConsentValidationService.validateConsent(): consent : {}", consent);
    PisConsentValidator validator = validators.get(instrument);
    if (validator == null) {
      throw new NotFoundException("Unknown instrument: " + instrument);
    }
    validator.validateConsent(consent);
  }
}
