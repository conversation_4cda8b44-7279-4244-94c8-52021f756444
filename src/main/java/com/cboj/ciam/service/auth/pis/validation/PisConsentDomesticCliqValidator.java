package com.cboj.ciam.service.auth.pis.validation;

import com.cboj.ciam.api.AccountDto;
import com.cboj.ciam.api.CliqAccountDto;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.consts.AppConstants;
import com.cboj.ciam.jpa.AccountScheme;
import com.cboj.ciam.jpa.pis.LocalInstrument;
import com.cboj.ciam.jpa.pis.PaymentCurrency;
import com.cboj.ciam.jpa.pis.PisCliqPurposeCodesLimits;
import com.cboj.ciam.service.data.pis.PisCliqPurposeCodesLimitsStorage;
import com.cboj.ciam.service.mulesoft.AccountService;
import com.cboj.ciam.service.mulesoft.SwiftRefService;
import com.cboj.ciam.service.mulesoft.pis.TransactionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;
import javax.validation.constraints.NotNull;

import static com.cboj.ciam.service.auth.pis.UtilValidation.*;

@Slf4j
@Component
public class PisConsentDomesticCliqValidator extends PisConsentBaseValidator implements PisConsentValidator {

  private final AccountService accountService;
  private final PisCliqPurposeCodesLimitsStorage pisCliqPurposeCodesLimitsStorage;
  private final SwiftRefService swiftRefService;

  public PisConsentDomesticCliqValidator(TransactionInfoService transactionInfoService,
                                         AccountService accountService,
                                         PisCliqPurposeCodesLimitsStorage pisCliqPurposeCodesLimitsStorage,
                                         SwiftRefService swiftRefService) {
    super(transactionInfoService);
    this.accountService = accountService;
    this.pisCliqPurposeCodesLimitsStorage = pisCliqPurposeCodesLimitsStorage;
    this.swiftRefService = swiftRefService;
  }

  @Override
  public void validateConsent(PisConsentDto consentDto) {
    PaymentCurrency currency;
    try {
      currency = PaymentCurrency.valueOf(consentDto.getCurrency());
    } catch (IllegalArgumentException e) {
      throw new ValidationException("Currency '" + consentDto.getCurrency() + "' is not supported");
    }
    validateAmount(consentDto.getCurrency(), consentDto.getAmount());

    if (!PaymentCurrency.JOD.equals(currency)) {
      throw new ValidationException("Only JOD currency is supported");
    }

    AccountScheme accountScheme = AccountScheme.getFromValue(consentDto.getCreditorAccScheme());

    switch (accountScheme) {
      case IBAN:
        validateCliqIbanRequest(consentDto);
        break;
      case ALIAS:
      case MOBILE:
        validateAliasMobileSchema(consentDto, accountScheme);
        break;
      case ACCOUNT_NUMBER:
        throw new ValidationException(String.format("Account Scheme %s is not supported", AccountScheme.ACCOUNT_NUMBER.getValue()));
    }
  }

  @Override
  public LocalInstrument getType() {
    return LocalInstrument.DOMESTIC_CLIQ;
  }

  private void validateCliqIbanRequest(@NotNull PisConsentDto pisConsentDto) {

    validateBeneficiaryData(pisConsentDto);


    PisCliqPurposeCodesLimits pisCliqPurposeCodesLimits =
            pisCliqPurposeCodesLimitsStorage.findByCodes(AppConstants.PisCliqTypes.IBAN, pisConsentDto.getPaymentPurposeCode());

    /// Check category and paymentPurposeCode combination is allowed
    if (pisCliqPurposeCodesLimits == null) {
      log.error("Payment category code {} and purpose code {} not correct", AppConstants.PisCliqTypes.IBAN, pisConsentDto.getPaymentPurposeCode());
      throw new ValidationException("Invalid categoryPurposeCode/paymentPurposeCode or Combination");
    }

    if (pisCliqPurposeCodesLimits.getLimit() < pisConsentDto.getAmount()) {
      log.error("Transfer amount greater than transfer limit");
      throw new ValidationException("Transaction limit exceeded");
    }

    if (!pisConsentDto.getCreditorAccRef().startsWith("JO")) {
      throw new ValidationException("IBAN validation failed. Only JO accounts are supported.");
    }

    boolean ibanValidation = swiftRefService.validateIban(pisConsentDto);
    String bicCode = swiftRefService.getBicCode(pisConsentDto.getCreditorAccRef());
    if (!ibanValidation || isNullOrEmpty(bicCode)) {
      throw new ValidationException("IBAN validation failed");
    }
    pisConsentDto.setBeneficiaryBicCode(bicCode);
  }

  private void validateBeneficiaryData(@NotNull PisConsentDto consentDto) {
    String firstName = consentDto.getBeneficiaryFirstName();
    String middleName = consentDto.getBeneficiaryMiddleName();
    String lastName = consentDto.getBeneficiaryLastName();

    if(isNullOrEmpty(firstName) || isNullOrEmpty(middleName)
            || isNullOrEmpty(lastName)){
      throw new ValidationException("Beneficiary Name First, Middle & Last name is mandatory for Cliq payment with IBAN");
    }

    if(!isEnglishWithSpecialChar(firstName + middleName + lastName)){
      throw new ValidationException("Beneficiary name should only in English letters or / -: (). ‘ +");
    }

    if(firstName.length() < 3){
      throw new ValidationException("Beneficiary FirstName should be more than 3 characters");
    }
    if(middleName.length() < 3){
      throw new ValidationException("Beneficiary MiddleName should be more than 3 characters");
    }
    if(lastName.length() < 3){
      throw new ValidationException("Beneficiary LastName should be more than 3 characters");
    }

    if ((firstName.length() + middleName.length() + lastName.length()) > 60 ){
      throw new ValidationException("Beneficiary names should not exceed 60 characters");
    }

    if(isNullOrEmpty(consentDto.getBeneficiaryAddressLine())){
      throw new ValidationException("Beneficiary Address Line is mandatory for Cliq payment with IBAN");
    }

    if(consentDto.getBeneficiaryAddressLine().length() > 35){
      throw new ValidationException("Beneficiary Address Line should not exceed 35 length");
    }

    if(!isEnglishWithSpecialChar(consentDto.getBeneficiaryAddressLine())){
      throw new ValidationException("Beneficiary AddressLine should only in English letters or / -: (). ‘ +");
    }

  }



  private void validateAliasMobileSchema(@NotNull PisConsentDto pisConsentDto,
                                         @NotNull AccountScheme accountScheme) {
    if (!isAccountFormatCorrect(pisConsentDto.getDebtorAccScheme(), pisConsentDto.getDebtorAccRef())) {
      log.error("Wrong Debtor account {}  ", pisConsentDto.getDebtorAccRef());
      throw new ValidationException("Wrong Debtor account");
    }

    String preparedDebtorAccountId = prepareAccountId(pisConsentDto.getDebtorAccRef());

    /// Check Debtor Account
    AccountDto debtorAccount = accountService.getAccountDetails(preparedDebtorAccountId);
    if (debtorAccount == null) {
      throw new ValidationException("Debtor Account not found");
    }

    String customerId = debtorAccount.getCustomerId();
    String aliasId = pisConsentDto.getCreditorAccRef();

    CliqAccountDto creditorCLiqAccount = accountService.getCustomerAliasAccounts(customerId, aliasId, accountScheme);

    pisConsentDto.setCreditorNameEn(creditorCLiqAccount.getCustomerName());
    pisConsentDto.setBeneficiaryAddressLine(creditorCLiqAccount.getCustomerAddress());
    pisConsentDto.setBeneficiaryBicCode(creditorCLiqAccount.getBic());
    pisConsentDto.setSecondaryIdentificationSchemeName(AccountScheme.IBAN.getValue());
    pisConsentDto.setSecondaryIdentification(creditorCLiqAccount.getIban());

    String customerType = creditorCLiqAccount.getCustomerType();
    String accountType = creditorCLiqAccount.getType();

    if (StringUtils.isEmpty(customerType) || StringUtils.isEmpty(accountType)) {
      throw new ValidationException("Customer type or alias account type is null");
    }

    String paymentCategoryCode = accountType;

    if (accountType.equals(AppConstants.AccountTypes.DFLT)) {
      if (customerType.equals(AppConstants.CustomerTypes.INDIVIDUAL)) {
        paymentCategoryCode = AppConstants.PisCliqTypes.DFLT_INDIVIDUAL;
      } else {
        paymentCategoryCode = AppConstants.PisCliqTypes.DFLT_LEGAL;
      }
    }

    String purposeCode = pisConsentDto.getPaymentPurposeCode();

    log.info("paymentCategoryCode : " + paymentCategoryCode);
    log.info("purposeCode : " +purposeCode);

    PisCliqPurposeCodesLimits pisCliqPurposeCodesLimits = pisCliqPurposeCodesLimitsStorage.findByCodes(paymentCategoryCode, purposeCode);

    /// Check category and paymentPurposeCode combination is allowed
    if (pisCliqPurposeCodesLimits == null) {
      log.error("Payment category code {} and purpose code {} not correct", AppConstants.PisCliqTypes.IBAN, purposeCode);
      throw new ValidationException("Invalid categoryPurposeCode/paymentPurposeCode or Combination");
    }
    pisConsentDto.setPaymentPurposeDesc(pisCliqPurposeCodesLimits.getDescription());

    if (pisCliqPurposeCodesLimits.getLimit() < pisConsentDto.getAmount()) {
      log.error("Transfer amount greater than transfer limit");
      throw new ValidationException("Transaction limit exceeded");
    }
  }
}
