package com.cboj.ciam.service.data;

import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.consts.SessionConstants;
import com.cboj.ciam.service.data.ais.AisConsentDataService;
import com.cboj.ciam.service.data.caf.CafConsentDataService;
import com.cboj.ciam.service.data.pis.PisConsentDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * Class is responsible for retrieving the appropriate ConsentDataService based on the provided scope.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsentServiceProvider {

  private final AisConsentDataService aisConsentService;
  private final PisConsentDataService pisConsentService;
  private final CafConsentDataService cafConsentService;

  /**
   * Retrieves the appropriate DataService based on the provided scope.
   *
   * @param scope the scope of the DataService
   * @return the DataService object based on the provided scope.
   * @throws IllegalArgumentException if the scope is null or not supported.
   */
  @SuppressWarnings("rawtypes")
  public DataService getDataService(String scope) {
    if (scope == null) {
      throw new IllegalArgumentException("Scope can not be null");
    }
    switch (scope) {
      case SsoConstants.Scope.ACCOUNTS:
        return aisConsentService;
      case SsoConstants.Scope.PAYMENTS:
        return pisConsentService;
      case SsoConstants.Scope.FUNDS_CONFIRMATIONS:
        return cafConsentService;
      default:
        throw new IllegalArgumentException("Unsupported scope " + scope);
    }
  }

  /**
   * Retrieves the appropriate DataService based on the provided scope.
   *
   * @param request the HttpServletRequest object to get the scope from the session attribute
   * @return the DataService object based on the provided scope.
   * @throws IllegalArgumentException if the scope is null or not supported.
   */
  @SuppressWarnings("rawtypes")
  public DataService getDataService(HttpServletRequest request) {
    String scope = (String) request.getSession(false).getAttribute(SessionConstants.CURRENT_SCOPE_ATTR);
    return getDataService(scope);
  }
}
