package com.cboj.ciam.service.data;

import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.Consent_;
import com.cboj.ciam.jpa.DataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class AsyncConsentStorage<T extends Consent> extends AsyncStorage<T> {

  public AsyncConsentStorage(DataRepository<T> repository) {
    super(repository);
  }

  @Override
  public CompletableFuture<Page<T>> search(Integer page, Integer perPage) {
    Specification<T> finalSpecification = processSortByCreateDate(null);

    return super.search(finalSpecification, page, perPage);
  }

  @Override
  public CompletableFuture<Page<T>> search(@Nullable Specification<T> specification, Integer page, Integer perPage) {
    Specification<T> finalSpecification = processSortByCreateDate(specification);

    return super.search(finalSpecification, page, perPage);
  }

  @Override
  public CompletableFuture<List<T>> search(@Nullable Specification<T> specification) {
    Specification<T> finalSpecification = processSortByCreateDate(specification);

    return super.search(finalSpecification);
  }

  /**
   * Processes sorting by create date for the given specification.
   *
   * @param specification the base specification to be extended with sorting by create date
   * @return the specification with sorting by create date applied, or a new specification with sorting by create date if the input
   * specification is null
   */
  protected Specification<T> processSortByCreateDate(Specification<T> specification) {
    Specification<T> sortByCreateDate = (root, query, cb) -> {
      query.orderBy(cb.desc(root.get(Consent_.CREATE_DATE)));
      return cb.conjunction();
    };

    return specification == null ? sortByCreateDate : specification.and(sortByCreateDate);
  }
}
