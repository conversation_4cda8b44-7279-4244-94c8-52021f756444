package com.cboj.ciam.service.data;

import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.ConsentAccountType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConsentAccountTypeStorageImpl extends AsyncStorage<ConsentAccountType> implements ConsentAccountTypeStorage {

  @Autowired
  public ConsentAccountTypeStorageImpl(DataRepository<ConsentAccountType> repository) {
    super(repository);
  }
}
