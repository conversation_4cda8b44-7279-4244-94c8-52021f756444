package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.ais.AisConsentAccount;
import com.cboj.ciam.service.data.AsyncStorage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AisConsentAccountStorageImpl extends AsyncStorage<AisConsentAccount> implements AisConsentAccountStorage {

  @Autowired
  public AisConsentAccountStorageImpl(DataRepository<AisConsentAccount> repository) {
    super(repository);
  }
}
