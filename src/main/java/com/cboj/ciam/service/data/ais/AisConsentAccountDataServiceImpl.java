package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.api.ConsentAccountDto;
import com.cboj.ciam.jpa.ais.AisConsentAccount;
import com.cboj.ciam.service.data.AsyncService;
import com.cboj.ciam.service.data.DataService;
import org.springframework.stereotype.Service;

import com.cboj.ciam.api.Mapper;

/**
 * Service implementation for managing AisConsentAccount asynchronously.
 */
@Service
public class AisConsentAccountDataServiceImpl
    extends AsyncService<AisConsentAccount, ConsentAccountDto>
    implements DataService<AisConsentAccount, ConsentAccountDto> {

  protected AisConsentAccountDataServiceImpl(AisConsentAccountStorage storage,
                                             Mapper<ConsentAccountDto, AisConsentAccount> mapper) {
    super(storage, mapper);
  }
}
