package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.api.ais.AisPermissionDto;
import com.cboj.ciam.jpa.ais.AisPermission;
import com.cboj.ciam.service.data.AsyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

/**
 * Service implementation for managing AisPermissions asynchronously.
 */
@Service
public class AisPermissionDataServiceImpl
    extends AsyncService<AisPermission, AisPermissionDto>
    implements AisPermissionDataService {

  private final AisPermissionStorage permissionStorage;

  @Autowired
  protected AisPermissionDataServiceImpl(AisPermissionStorage storage,
                                         Mapper<AisPermissionDto, AisPermission> mapper) {
    super(storage, mapper);
    this.permissionStorage = storage;
  }

  /**
   * Searches for AisPermissions with the given codes.
   *
   * @param codes the collection of codes to search for
   * @return a CompletableFuture that resolves to a list of AisPermissions
   */
  @Override public CompletableFuture<List<AisPermission>> searchByCodes(Collection<String> codes) {
    return permissionStorage.searchByCodes(codes);
  }
}
