package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.data.AsyncConsentStorage;
import com.fasterxml.uuid.Generators;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
public class PisConsentStorageImpl extends AsyncConsentStorage<PisConsentEntity> implements PisConsentStorage {

  @Autowired
  public PisConsentStorageImpl(DataRepository<PisConsentEntity> repository) {
    super(repository);
  }

  @Override public CompletableFuture<PisConsentEntity> create(PisConsentEntity entity) {
    entity.setConsentRef(Generators.timeBasedGenerator().generate());
    entity.setStatus(ConsentStatus.NEW);
    return super.create(entity);
  }

  @Override protected CompletableFuture<PisConsentEntity> getExistEntity(PisConsentEntity entity) {
    if (entity.getId() != null) {
      return super.getExistEntity(entity);
    }

    if (entity.getConsentRef() == null) {
      throw new NotFoundException(String.format("Entity with id %d not found", entity.getId()));
    }

    return super.search(Specifications.consentRefEquals(entity.getConsentRef()))
        .thenApply(PisConsents -> PisConsents.stream().findFirst().orElseThrow(
            () -> new NotFoundException(String.format("Entity with id %s not found", entity.getConsentRef()))));
  }

  @Override
  public CompletableFuture<List<PisConsentEntity>> searchByStatusesAndClientId(String clientId,
                                                                               ConsentStatus... statuses) {
    Specification<PisConsentEntity> spec = Specifications.clientIdEquals(clientId).and(Specifications.statusIn(statuses));
    return super.search(spec);
  }

  public static class Specifications {

    public static Specification<PisConsentEntity> consentRefEquals(UUID consentRef) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PisConsentEntity_.CONSENT_REF), consentRef);
    }

    public static Specification<PisConsentEntity> clientIdEquals(String clientId) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PisConsentEntity_.CLIENT_APP_ID), clientId);
    }

    public static Specification<PisConsentEntity> statusIn(ConsentStatus... statuses) {
      return (root, query, criteriaBuilder) -> {
        CriteriaBuilder.In<ConsentStatus> consentIn = criteriaBuilder.in(root.get(PisConsentEntity_.STATUS));
        Arrays.stream(statuses).forEach(consentIn::value);
        return consentIn;
      };
    }
  }
}
