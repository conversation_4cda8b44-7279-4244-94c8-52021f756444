package com.cboj.ciam.service.data.caf;

import com.cboj.ciam.api.caf.CafConsentDto;
import com.cboj.ciam.api.caf.CafOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.jpa.caf.CafConsent_;
import com.cboj.ciam.service.data.AsyncService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

/**
 * Service class for managing PisConsentData entities.
 */
@Service
public class CafConsentDataServiceImpl
    extends AsyncService<CafConsent, CafConsentDto>
    implements CafConsentDataService {

  @Value("${ciam.base.url}")
  private String baseUrl;

  private final CafConsentStorage cafConsentStorage;
  private final Mapper<CafConsent, CafOutputConsentDto> outputMapper;

  protected CafConsentDataServiceImpl(CafConsentStorage storage,
                                      Mapper<CafConsentDto, CafConsent> mapper,
                                      Mapper<CafConsent, CafOutputConsentDto> outputMapper) {
    super(storage, mapper);
    this.cafConsentStorage = storage;
    this.outputMapper = outputMapper;
  }

  /**
   * Creates a consent response based on the provided PisConsentDto and HttpServletRequest.
   *
   * @param dto     The PisConsentDto object containing the consent data.
   * @param request The HttpServletRequest object to construct the login URL.
   * @return A CompletableFuture of PisConsentDto.
   */
  @Override
  public CompletableFuture<CafOutputConsentDto> createConsentResponse(CafConsentDto dto,
                                                                      HttpServletRequest request) {
    CafConsent consent = mapper.apply(dto);
    return super.create(consent)
        .thenApply(created -> {
          CafOutputConsentDto output = outputMapper.apply(created);
          output.setConsentRef(created.getConsentRef().toString());
          output.setLoginUrls(getLoginUris(consent));
          return output;
        });
  }

  /**
   * Deletes the specified PisConsent entity by setting its status to REVOKED.
   *
   * @param entity The PisConsent entity to be deleted.
   * @return A CompletableFuture of ResponseEntity<Boolean> indicating the success of the deletion operation.
   */
  @Override
  public CompletableFuture<ResponseEntity<Boolean>> delete(CafConsent entity) {
    entity.setStatus(ConsentStatus.REVOKED);
    return cafConsentStorage.update(entity).thenApply(unused -> ResponseEntity.ok(true));
  }

  /**
   * Searches for PisConsent entities by client ID and consent statuses.
   *
   * @param clientId The client ID to search by.
   * @param statuses The consent statuses to search for.
   * @return list containing the matching entities.
   */
  @Override
  public CompletableFuture<List<CafConsent>> searchByStatusesAndClientId(@NotNull String clientId,
                                                                         ConsentStatus... statuses) {
    return cafConsentStorage.searchByStatusesAndClientId(clientId, statuses);
  }

  /**
   * Searches for expired CafConsent entities based on the specified expiry date.
   *
   * @param expiryDate The LocalDateTime representing the expiry date.
   * @return List<CafConsent> containing the expired CafConsent entities.
   */
  @Override
  public CompletableFuture<List<CafConsent>> searchExpired(@NotNull LocalDateTime expiryDate) {
    return cafConsentStorage.searchExpiryDateBefore(expiryDate);
  }

  /**
   * Retrieves the login URIs for the given consent.
   *
   * @param consent The Consent object for which to retrieve the login URIs.
   * @return A set of login URIs for the specified consent.
   */
  private Set<String> getLoginUris(Consent consent) {
    String uri = UriComponentsBuilder.fromUriString(baseUrl)
        .path(RestConstants.Paths.WEB.LOGIN)
        .queryParam(CafConsent_.CONSENT_REF, consent.getConsentRef())
        .build().toString();
    return Collections.singleton(uri);
  }
}
