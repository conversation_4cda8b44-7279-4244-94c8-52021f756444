package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.pis.PisCliqPurposeCodesLimits;
import com.cboj.ciam.jpa.pis.PisCliqPurposeCodesLimits_;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

@Service
@RequiredArgsConstructor
public class PisCliqPurposeCodesLimitsStorageImpl implements PisCliqPurposeCodesLimitsStorage {

  private final DataRepository<PisCliqPurposeCodesLimits> repository;

  @Override
  public PisCliqPurposeCodesLimits findByCodes(@NotNull String paymentCategoryCode,
                                               @NotNull String paymentPurposeCode) {
    Specification<PisCliqPurposeCodesLimits> spec = Specifications.paymentPurposeCodeEquals(paymentPurposeCode)
        .and(Specifications.paymentCategoryCodeEquals(paymentCategoryCode));

    return repository.findAll(spec).stream()
        .findFirst()
        .orElse(null);
  }

  public static class Specifications {

    public static Specification<PisCliqPurposeCodesLimits> paymentCategoryCodeEquals(String paymentCategoryCode) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PisCliqPurposeCodesLimits_.PAYMENT_CATEGORY_CODE), paymentCategoryCode);
    }

    public static Specification<PisCliqPurposeCodesLimits> paymentPurposeCodeEquals(String paymentPurposeCode) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PisCliqPurposeCodesLimits_.PAYMENT_PURPOSE_CODE), paymentPurposeCode);
    }
  }
}
