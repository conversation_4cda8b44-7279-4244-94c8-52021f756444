package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.data.DataService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface PisConsentDataService extends DataService<PisConsentEntity, PisConsentDto> {

  CompletableFuture<PisOutputConsentDto> createConsentResponse(PisConsentDto dto, HttpServletRequest request);

  CompletableFuture<List<PisConsentEntity>> searchByStatusesAndClientId(@NotNull String clientId, ConsentStatus... statuses);

}
