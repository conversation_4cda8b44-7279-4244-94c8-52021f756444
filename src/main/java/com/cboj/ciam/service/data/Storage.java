package com.cboj.ciam.service.data;

import com.cboj.ciam.jpa.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * This interface represents a storage for entities of type T, where T extends BaseEntity.
 *
 * @param <T> the type of entities stored in the storage
 */
public interface Storage<T extends BaseEntity> {

  /**
   * Creates a new entity asynchronously in the storage.
   *
   * @param entity the entity to be created
   * @return created entity
   */
  CompletableFuture<T> create(T entity);

  /**
   * Updates an entity asynchronously in the storage.
   *
   * @param entity the entity to be updated
   * @return updated entity
   */
  CompletableFuture<T> update(T entity);

  /**
   * Updates all entities in the storage asynchronously.
   *
   * @param collection the collection of entities to be updated
   * @return List containing the updated entities
   * @throws IllegalArgumentException if the collection of entities is empty
   */
  CompletableFuture<List<T>> updateAll(@NotEmpty Collection<T> collection);

  /**
   * Finds an entity with the given ID asynchronously.
   *
   * @param id the ID of the entity to be found
   * @return Found entity, or an empty Optional if no entity is found
   */
  CompletableFuture<Optional<T>> find(Long id);

  /**
   * Searches for entities asynchronously based on the specified page and perPage values.
   *
   * @param page    the page number to retrieve
   * @param perPage the number of entities per page
   * @return Page containing the search results
   */
  CompletableFuture<Page<T>> search(Integer page, Integer perPage);

  /**
   * Searches for entities asynchronously based on the specified specification, page, and perPage values.
   *
   * @param specification the specification for filtering the search results
   * @param page          the page number to retrieve
   * @param perPage       the number of entities per page
   * @return Page containing the search results
   */
  CompletableFuture<Page<T>> search(@Nullable Specification<T> specification, Integer page, Integer perPage);

  /**
   * Searches for entities asynchronously based on the specified specification.
   *
   * @param specification the specification for filtering the search results (not null)
   * @return a CompletableFuture that completes with a List containing the search results
   */
  CompletableFuture<List<T>> search(@Nullable Specification<T> specification);

  /**
   * Searches for entities asynchronously based on the specified attribute name and value.
   *
   * @param attrName  the name of the attribute to search by (not null)
   * @param attrValue the value of the attribute to search for (not null)
   * @return List containing the search results
   */
  CompletableFuture<List<T>> searchByAttr(@NotNull String attrName, @NotNull Object attrValue);

  /**
   * Searches for entities asynchronously based on the specified attribute name and value, with optional pagination.
   *
   * @param attrName  the name of the attribute to search by (not null)
   * @param attrValue the value of the attribute to search for (not null)
   * @param page      the page number to retrieve, null for no pagination
   * @param perPage   the number of entities per page, null for no pagination
   * @return Page containing the search results
   */
  CompletableFuture<Page<T>> searchByAttr(@NotNull String attrName,
                                          @NotNull Object attrValue,
                                          Integer page,
                                          Integer perPage);

  CompletableFuture<Page<T>> searchByAttrs(@NotEmpty Map<String, Object> data,
                                           Integer page,
                                           Integer perPage);

  /**
   * Deletes an entity with the given ID asynchronously from the storage.
   *
   * @param id the ID of the entity to be deleted
   */
  void delete(Long id);
}
