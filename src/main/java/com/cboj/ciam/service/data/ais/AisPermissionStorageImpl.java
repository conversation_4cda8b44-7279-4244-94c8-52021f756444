package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.ais.AisPermission;
import com.cboj.ciam.jpa.ais.AisPermission_;
import com.cboj.ciam.service.data.AsyncStorage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public class AisPermissionStorageImpl extends AsyncStorage<AisPermission> implements AisPermissionStorage {

  @Autowired
  public AisPermissionStorageImpl(DataRepository<AisPermission> repository) {
    super(repository);
  }

  @Override public CompletableFuture<List<AisPermission>> searchByCodes(Collection<String> codes) {
    return search(Specifications.codeIn(codes));
  }

  public static class Specifications {

    public static Specification<AisPermission> codeIn(Collection<String> codes) {
      return (root, query, criteriaBuilder) -> {
        CriteriaBuilder.In<String> in = criteriaBuilder.in(root.get(AisPermission_.HANDLE));
        codes.forEach(in::value);
        return in;
      };
    }
  }
}
