package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.service.data.Storage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface AisConsentStorage extends Storage<AisConsent> {

  CompletableFuture<List<AisConsent>> searchExpiryDateBefore(LocalDateTime expiryDate);

  CompletableFuture<List<AisConsent>> searchByStatusesAndClientId(String clientId, ConsentStatus... statuses);
}
