package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.service.data.Storage;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface PisConsentStorage extends Storage<PisConsentEntity> {

  CompletableFuture<List<PisConsentEntity>> searchByStatusesAndClientId(String clientId, ConsentStatus... statuses);
}
