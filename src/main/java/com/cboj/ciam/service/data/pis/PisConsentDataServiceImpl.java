package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.api.Mapper;
import com.cboj.ciam.api.pis.PisConsentDto;
import com.cboj.ciam.api.pis.PisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.pis.PisConsentEntity;
import com.cboj.ciam.jpa.pis.PisConsentEntity_;
import com.cboj.ciam.service.data.AsyncService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Service class for managing PisConsentData entities.
 */
@Service
public class PisConsentDataServiceImpl
    extends AsyncService<PisConsentEntity, PisConsentDto>
    implements PisConsentDataService {

  @Value("${ciam.base.url}")
  private String baseUrl;

  private final PisConsentStorage pisConsentStorage;
  private final Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper;

  protected PisConsentDataServiceImpl(PisConsentStorage storage,
                                      Mapper<PisConsentDto, PisConsentEntity> mapper,
                                      Mapper<PisConsentEntity, PisOutputConsentDto> outputMapper) {
    super(storage, mapper);
    this.pisConsentStorage = storage;
    this.outputMapper = outputMapper;
  }

  /**
   * Creates a consent response based on the provided PisConsentDto and HttpServletRequest.
   *
   * @param pisConsentDto     The PisConsentDto object containing the consent data.
   * @param request The HttpServletRequest object to construct the login URL.
   * @return A CompletableFuture of PisConsentDto.
   */
  @Override
  public CompletableFuture<PisOutputConsentDto> createConsentResponse(PisConsentDto pisConsentDto,
                                                    HttpServletRequest request) {
    PisConsentEntity pisConsentEntity = mapper.apply(pisConsentDto);
    return super.create(pisConsentEntity)
        .thenApply(created -> {
          PisOutputConsentDto output = outputMapper.apply(pisConsentEntity);
          output.setConsentRef(created.getConsentRef().toString());
          output.setLoginUrls(getLoginUris(pisConsentEntity));
          return output;
        });
  }

  /**
   * Deletes the specified PisConsent entity by setting its status to REVOKED.
   *
   * @param entity The PisConsent entity to be deleted.
   * @return A CompletableFuture of ResponseEntity<Boolean> indicating the success of the deletion operation.
   */
  @Override
  public CompletableFuture<ResponseEntity<Boolean>> delete(PisConsentEntity entity) {
    entity.setStatus(ConsentStatus.REVOKED);
    return pisConsentStorage.update(entity).thenApply(unused -> ResponseEntity.ok(true));
  }

  /**
   * Searches for PisConsent entities by client ID and consent statuses.
   *
   * @param clientId The client ID to search by.
   * @param statuses The consent statuses to search for.
   * @return list containing the matching entities.
   */
  @Override
  public CompletableFuture<List<PisConsentEntity>> searchByStatusesAndClientId(@NotNull String clientId,
                                                                               ConsentStatus... statuses) {
    return pisConsentStorage.searchByStatusesAndClientId(clientId, statuses);
  }

  /**
   * Retrieves the login URIs for the given consent.
   *
   * @param consent The Consent object for which to retrieve the login URIs.
   * @return A set of login URIs for the specified consent.
   */
  private Set<String> getLoginUris(Consent consent) {
    String uri = UriComponentsBuilder.fromUriString(baseUrl)
        .path(RestConstants.Paths.WEB.LOGIN)
        .queryParam(PisConsentEntity_.CONSENT_REF, consent.getConsentRef())
        .build().toString();
    return Collections.singleton(uri);
  }
}
