package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.api.ais.AisPermissionDto;
import com.cboj.ciam.jpa.ais.AisPermission;
import com.cboj.ciam.service.data.DataService;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface AisPermissionDataService extends DataService<AisPermission, AisPermissionDto> {

  CompletableFuture<List<AisPermission>> searchByCodes(Collection<String> codes);
}
