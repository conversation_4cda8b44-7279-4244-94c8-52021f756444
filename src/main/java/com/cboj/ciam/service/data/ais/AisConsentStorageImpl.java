package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.service.data.AsyncConsentStorage;
import com.fasterxml.uuid.Generators;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
public class AisConsentStorageImpl extends AsyncConsentStorage<AisConsent> implements AisConsentStorage {

  @Value("${ciam.consent.expired}")
  private Integer days;

  @Autowired
  public AisConsentStorageImpl(DataRepository<AisConsent> repository) {
    super(repository);
  }

  @Override public CompletableFuture<AisConsent> create(AisConsent entity) {
    entity.setConsentRef(Generators.timeBasedGenerator().generate());
    entity.setStatus(ConsentStatus.NEW);
    entity.setExpiryDate(LocalDateTime.now().plusDays(days));
    return super.create(entity);
  }

  @Override protected CompletableFuture<AisConsent> getExistEntity(AisConsent entity) {
    if (entity.getId() != null) {
      return super.getExistEntity(entity);
    }

    if (entity.getConsentRef() == null) {
      throw new NotFoundException(String.format("Entity with id %d not found", entity.getId()));
    }

    return super.search(Specifications.consentRefEquals(entity.getConsentRef()))
        .thenApply(aisConsents -> aisConsents.stream().findFirst().orElseThrow(
            () -> new NotFoundException(String.format("Entity with id %s not found", entity.getConsentRef()))));
  }

  @Override public CompletableFuture<List<AisConsent>> searchExpiryDateBefore(LocalDateTime expiryDate) {
    return super.search(Specifications.expiryDateBefore(expiryDate).and(Specifications.statusIn(ConsentStatus.NEW, ConsentStatus.ACTIVE)));
  }

  @Override
  public CompletableFuture<List<AisConsent>> searchByStatusesAndClientId(String clientId,
                                                                         ConsentStatus... statuses) {
    Specification<AisConsent> spec = Specifications.clientIdEquals(clientId).and(Specifications.statusIn(statuses));
    return super.search(spec);
  }

  public static class Specifications {

    public static Specification<AisConsent> consentRefEquals(UUID consentRef) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(AisConsent_.CONSENT_REF), consentRef);
    }

    public static Specification<AisConsent> expiryDateBefore(LocalDateTime expiryDate) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(AisConsent_.EXPIRY_DATE), expiryDate);
    }

    public static Specification<AisConsent> clientIdEquals(String clientId) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(AisConsent_.CLIENT_APP_ID), clientId);
    }

    public static Specification<AisConsent> statusIn(ConsentStatus... statuses) {
      return (root, query, criteriaBuilder) -> {
        CriteriaBuilder.In<ConsentStatus> consentIn = criteriaBuilder.in(root.get(AisConsent_.STATUS));
        Arrays.stream(statuses).forEach(consentIn::value);
        return consentIn;
      };
    }
  }
}
