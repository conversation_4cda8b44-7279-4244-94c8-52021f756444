package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.service.data.DataService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface AisConsentDataService extends DataService<AisConsent, AisConsentDto> {

  CompletableFuture<AisOutputConsentDto> createConsentResponse(AisConsentDto dto,
                                                               HttpServletRequest request);

  CompletableFuture<List<AisConsent>> searchExpired(@NotNull LocalDateTime expiryDate);

  CompletableFuture<List<AisConsent>> searchByStatusesAndClientId(@NotNull String clientId, ConsentStatus... statuses);

}
