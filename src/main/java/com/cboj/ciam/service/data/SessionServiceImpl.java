package com.cboj.ciam.service.data;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.session.jdbc.JdbcIndexedSessionRepository;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;

@Slf4j
@Service
@RequiredArgsConstructor
public class SessionServiceImpl implements SessionService {

  private final JdbcIndexedSessionRepository sessionRepository;

  public void deleteSession(HttpSession session) {
    if (session == null) {
      log.error("Session is null");
      return;
    }
    sessionRepository.deleteById(session.getId());
  }
}
