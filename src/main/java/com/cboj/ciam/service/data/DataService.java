package com.cboj.ciam.service.data;

import com.cboj.ciam.api.BaseDto;
import com.cboj.ciam.jpa.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * This interface defines methods for managing entities asynchronously.
 *
 * @param <T> the type of entity being managed
 * @param <K> the type of DTO used to create or update the entity
 */
public interface DataService<T extends BaseEntity, K extends BaseDto> {

  /**
   * Finds an entity by its ID asynchronously.
   *
   * @param id  the ID of the entity to find
   * @param <T> the type of entity being searched for
   * @return Found entity, or an empty Optional if no entity is found
   */
  CompletableFuture<Optional<T>> find(Long id);

  /**
   * Executes a search for entities of type T asynchronously. The search is performed with pagination based on the provided page and perPage
   * parameters.
   *
   * @param page    the page number for the search results
   * @param perPage the number of entities per page
   * @return Page<T> object that represents the search results
   * @see Page
   * @see CompletableFuture
   */
  CompletableFuture<Page<T>> search(Integer page, Integer perPage);

  /**
   * Executes a search for entities of type T asynchronously, based on the given specification.
   *
   * @param specification the specification used to filter the search results
   * @return List of entities that match the specification
   * @see CompletableFuture
   * @see Specification
   */
  CompletableFuture<List<T>> search(@NotNull Specification<T> specification);

  /**
   * Finds an entity asynchronously by the given attribute name and value.
   *
   * @param attrName  the name of the attribute to search for
   * @param attrValue the value of the attribute to search for
   * @param <T>       the type of entity being searched for
   * @return Found entity, or an empty Optional if no entity is found
   */
  CompletableFuture<T> findByAttr(@NotNull String attrName, @NotNull Object attrValue);

  /**
   * Finds an entity asynchronously by the given attribute names and values.
   *
   * @param data names and values of the attributes to find for
   * @param <T>  the type of entity being searched for
   * @return Found entity, or an empty Optional if no entity is found
   */
  CompletableFuture<T> findByAttrs(@NotEmpty Map<String, Object> data);

  /**
   * Searches for entities of type T asynchronously based on the given attribute name and value.
   *
   * @param attrName  the name of the attribute to search for
   * @param attrValue the value of the attribute to search for
   * @param <T>       the type of entity being searched for
   * @return aList of entities that match the attribute name and value
   */
  CompletableFuture<List<T>> searchByAttr(@NotNull String attrName, @NotNull Object attrValue);

  /**
   * Executes a search for entities of type T asynchronously, based on the given attribute name and value.
   *
   * @param attrName  the name of the attribute to search for
   * @param attrValue the value of the attribute to search for
   * @param page      the page number for the search results (optional)
   * @param perPage   the number of entities per page (optional)
   * @param <T>       the type of entity being searched for
   * @return Page<T> object that represents the search results
   * @see Page
   * @see CompletableFuture
   */
  CompletableFuture<Page<T>> searchByAttr(@NotNull String attrName,
                                          @NotNull Object attrValue,
                                          Integer page,
                                          Integer perPage);

  /**
   * Executes a search for entities of type T asynchronously, based on the given attribute names and values.
   *
   * @param data    names and values of the attributes to search for
   * @param page    the page number for the search results (optional)
   * @param perPage the number of entities per page (optional)
   * @return Page<T> object that represents the search results
   * @see Page
   * @see CompletableFuture
   */
  CompletableFuture<Page<T>> searchByAttrs(@NotEmpty Map<String, Object> data,
                                           Integer page,
                                           Integer perPage);

  /**
   * Creates an entity asynchronously based on the given DTO.
   *
   * @param dto the data transfer object used to create the entity
   * @param <T> the type of entity being created
   * @param <K> the type of DTO used to create the entity
   * @return created entity
   * @see CompletableFuture
   */
  CompletableFuture<T> create(K dto);

  /**
   * Creates an entity asynchronously based on the given entity.
   *
   * @param entity the entity to be created
   * @return created entity
   */
  CompletableFuture<T> create(T entity);

  /**
   * Updates an entity asynchronously based on the given DTO.
   *
   * @param dto the data transfer object used to update the entity
   * @param <T> the type of entity being updated
   * @param <K> the type of DTO used to update the entity
   * @return updated entity
   * @see CompletableFuture
   */
  CompletableFuture<T> update(K dto);

  /**
   * Updates multiple entities asynchronously. Important! This method doesn't actualize objects before updating. All fields will be
   * replacing by new
   *
   * @param collection the collection of entities to be updated.
   * @return List of updated entities.
   */
  CompletableFuture<List<T>> updateAll(@NotEmpty Collection<T> collection);

  /**
   * Updates an entity asynchronously based on the given entity.
   *
   * @param entity the entity to be updated
   * @param <T>    the type of entity being updated
   * @return a CompletableFuture containing the updated entity
   * @see CompletableFuture
   */
  CompletableFuture<T> update(T entity);

  /**
   * Deletes an entity with the specified ID.
   *
   * @param id the ID of the entity to delete
   * @return Boolean value indicating whether the deletion was successful or not
   */
  ResponseEntity<Boolean> delete(Long id);

  /**
   * Deletes an entity asynchronously.
   *
   * @param entity the entity to delete
   * @param <T>    the type of entity being deleted
   * @return Boolean value indicating whether the deletion was successful or not
   * @see CompletableFuture
   * @see ResponseEntity
   */
  CompletableFuture<ResponseEntity<Boolean>> delete(T entity);
}
