package com.cboj.ciam.service.data;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.jpa.BaseEntity;
import com.cboj.ciam.jpa.DataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.beans.FeatureDescriptor;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * Class that provides basic asynchronous CRUD operations for a specific entity type.
 *
 * @param <T> the type of entity that this class operates on. T must extend BaseEntity.
 */
@Slf4j
@Async
@RequiredArgsConstructor
public abstract class AsyncStorage<T extends BaseEntity> {

  protected final DataRepository<T> repository;

  public static final int DEFAULT_PAGE_NUMBER = 0;
  public static final int DEFAULT_PER_PAGE = 50;

  public CompletableFuture<T> create(T entity) {
    LocalDateTime dateTime = LocalDateTime.now();
    entity.setCreateDate(dateTime);
    entity.setUpdateDate(dateTime);
    return CompletableFuture.supplyAsync(() -> {
      T created = repository.save(entity);
      log.info("CREATE operation: {} with id {}", created.getClass().getSimpleName(), created.getId());
      return created;
    });
  }

  public CompletableFuture<T> update(T entity) {
    entity.setUpdateDate(LocalDateTime.now());
    return getExistEntity(entity)
        .thenCompose(preparedEntity -> {
          copyNonNullProperties(entity, preparedEntity);
          return CompletableFuture.supplyAsync(() -> {
            T updated = repository.save(preparedEntity);
            log.info("UPDATE operation: {} with id {}", updated.getClass().getSimpleName(), updated.getId());
            return updated;
          });
        });
  }

  public CompletableFuture<List<T>> updateAll(@NotEmpty Collection<T> collection) {
    return CompletableFuture.supplyAsync(() -> {
      collection.stream().findFirst().ifPresent(first -> log.info("UPDATE ALL operation: " + first.getClass().getSimpleName()));
      return repository.saveAllAndFlush(collection);
    });
  }

  protected CompletableFuture<T> getExistEntity(T entity) {
    return CompletableFuture.supplyAsync(() -> repository.findById(entity.getId()))
        .thenApply(preparedEntityOptional -> {
          if (!preparedEntityOptional.isPresent()) {
            throw new NotFoundException(String.format("Entity with id %d not found", entity.getId()));
          }
          return preparedEntityOptional.get();
        });
  }

  public CompletableFuture<Optional<T>> find(Long id) {
    return CompletableFuture.supplyAsync(() -> {
      log.info("FIND operation: {} by identifier {}", this.getClass().getSimpleName(), id);
      return repository.findById(id);
    });
  }

  public CompletableFuture<Page<T>> search(Integer page, Integer perPage) {
    int finalPage = page == null ? DEFAULT_PAGE_NUMBER : page;
    int finalPerPage = perPage == null || perPage == 0 ? DEFAULT_PER_PAGE : perPage;

    log.info("SEARCH operation: {} paging request: {}", this.getClass().getSimpleName(), PageRequest.of(finalPage, finalPerPage));

    return CompletableFuture.supplyAsync(() -> repository.findAll(PageRequest.of(finalPage, finalPerPage)));
  }

  public CompletableFuture<Page<T>> search(@Nullable Specification<T> specification, Integer page, Integer perPage) {
    int finalPage = page == null ? DEFAULT_PAGE_NUMBER : page;
    int finalPerPage = perPage == null || perPage == 0 ? DEFAULT_PER_PAGE : perPage;

    log.info("SEARCH operation: {} by specification and paging request: {}", this.getClass().getSimpleName(),
        PageRequest.of(finalPage, finalPerPage));

    return CompletableFuture.supplyAsync(() -> repository.findAll(specification, PageRequest.of(finalPage, finalPerPage)));
  }

  public CompletableFuture<List<T>> search(@Nullable Specification<T> specification) {
    return CompletableFuture.supplyAsync(() -> {
      log.info("SEARCH {} by specification", this.getClass().getSimpleName());
      return repository.findAll(specification);
    });
  }

  public void delete(Long id) {
    repository.deleteById(id);
    log.info("DELETE operation: {} by identifier: {}", this.getClass().getSimpleName(), id);
  }

  public CompletableFuture<List<T>> searchByAttr(@NotNull String attrName,
                                                 @NotNull Object attrValue) {
    Specification<T> specification = (root, query, cb) -> cb.equal(root.get(attrName), attrValue);
    return search(specification);
  }

  public CompletableFuture<Page<T>> searchByAttr(@NotNull String attrName,
                                                 @NotNull Object attrValue,
                                                 Integer page,
                                                 Integer perPage) {
    Specification<T> specification = (root, query, cb) -> cb.equal(root.get(attrName), attrValue);
    return search(specification, page, perPage);
  }

  public CompletableFuture<Page<T>> searchByAttrs(@NotEmpty Map<String, Object> data,
                                                  Integer page,
                                                  Integer perPage) {
    Specification<T> specification = null;
    for (Map.Entry<String, Object> pair : data.entrySet()) {
      if (pair.getKey() == null || pair.getValue() == null) {
        continue;
      }
      if (specification == null) {
        specification = (root, query, cb) -> cb.equal(root.get(pair.getKey()), pair.getValue());
      } else {
        specification = specification.and((root, query, cb) -> cb.equal(root.get(pair.getKey()), pair.getValue()));
      }
    }
    return search(specification, page, perPage);
  }

  /**
   * Copies non-null properties from the source object to the target object.
   *
   * @param src    the source object from which to copy properties
   * @param target the target object to which properties are copied
   */
  private void copyNonNullProperties(Object src, Object target) {
    BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
  }

  /**
   * Retrieves the names of the properties that are null in the given object.
   *
   * @param source The object from which to retrieve the null property names.
   * @return An array of strings representing the names of the null properties.
   */
  private String[] getNullPropertyNames(Object source) {
    final BeanWrapper wrappedSource = new BeanWrapperImpl(source);
    return Stream.of(wrappedSource.getPropertyDescriptors())
        .map(FeatureDescriptor::getName)
        .filter(propertyName -> wrappedSource.getPropertyValue(propertyName) == null)
        .toArray(String[]::new);
  }

}
