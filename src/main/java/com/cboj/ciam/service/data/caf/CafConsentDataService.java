package com.cboj.ciam.service.data.caf;

import com.cboj.ciam.api.caf.CafConsentDto;
import com.cboj.ciam.api.caf.CafOutputConsentDto;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.service.data.DataService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface CafConsentDataService extends DataService<CafConsent, CafConsentDto> {

  CompletableFuture<CafOutputConsentDto> createConsentResponse(CafConsentDto dto, HttpServletRequest request);

  CompletableFuture<List<CafConsent>> searchExpired(@NotNull LocalDateTime expiryDate);

  CompletableFuture<List<CafConsent>> searchByStatusesAndClientId(@NotNull String clientId, ConsentStatus... statuses);

}
