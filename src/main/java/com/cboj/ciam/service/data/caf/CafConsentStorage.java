package com.cboj.ciam.service.data.caf;

import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.service.data.Storage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface CafConsentStorage extends Storage<CafConsent> {

  CompletableFuture<List<CafConsent>> searchExpiryDateBefore(LocalDateTime expiryDate);

  CompletableFuture<List<CafConsent>> searchByStatusesAndClientId(String clientId, ConsentStatus... statuses);
}
