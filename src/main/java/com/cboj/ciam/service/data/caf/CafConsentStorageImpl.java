package com.cboj.ciam.service.data.caf;

import com.cboj.ciam.NotFoundException;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.DataRepository;
import com.cboj.ciam.jpa.caf.CafConsent;
import com.cboj.ciam.jpa.caf.CafConsent_;
import com.cboj.ciam.service.data.AsyncConsentStorage;
import com.fasterxml.uuid.Generators;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
public class CafConsentStorageImpl extends AsyncConsentStorage<CafConsent> implements CafConsentStorage {

  @Value("${ciam.consent.expired}")
  private Integer days;

  @Autowired
  public CafConsentStorageImpl(DataRepository<CafConsent> repository) {
    super(repository);
  }

  @Override
  public CompletableFuture<CafConsent> create(CafConsent entity) {
    entity.setConsentRef(Generators.timeBasedGenerator().generate());
    entity.setStatus(ConsentStatus.NEW);
    entity.setExpiryDate(LocalDateTime.now().plusDays(days));
    return super.create(entity);
  }

  @Override
  protected CompletableFuture<CafConsent> getExistEntity(CafConsent entity) {
    if (entity.getId() != null) {
      return super.getExistEntity(entity);
    }

    if (entity.getConsentRef() == null) {
      throw new NotFoundException(String.format("Entity with id %d not found", entity.getId()));
    }

    return super.search(Specifications.consentRefEquals(entity.getConsentRef()))
        .thenApply(CafConsents -> CafConsents.stream().findFirst().orElseThrow(
            () -> new NotFoundException(String.format("Entity with id %s not found", entity.getConsentRef()))));
  }

  @Override
  public CompletableFuture<List<CafConsent>> searchByStatusesAndClientId(String clientId,
                                                                         ConsentStatus... statuses) {
    Specification<CafConsent> spec = Specifications.clientIdEquals(clientId).and(Specifications.statusIn(statuses));
    return super.search(spec);
  }

  @Override
  public CompletableFuture<List<CafConsent>> searchExpiryDateBefore(LocalDateTime expiryDate) {
    return super.search(Specifications.expiryDateBefore(expiryDate).and(Specifications.statusIn(ConsentStatus.NEW, ConsentStatus.ACTIVE)));
  }

  public static class Specifications {

    public static Specification<CafConsent> consentRefEquals(UUID consentRef) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(CafConsent_.CONSENT_REF), consentRef);
    }

    public static Specification<CafConsent> clientIdEquals(String clientId) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(CafConsent_.CLIENT_APP_ID), clientId);
    }

    public static Specification<CafConsent> expiryDateBefore(LocalDateTime expiryDate) {
      return (root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(CafConsent_.EXPIRY_DATE), expiryDate);
    }

    public static Specification<CafConsent> statusIn(ConsentStatus... statuses) {
      return (root, query, criteriaBuilder) -> {
        CriteriaBuilder.In<ConsentStatus> consentIn = criteriaBuilder.in(root.get(CafConsent_.STATUS));
        Arrays.stream(statuses).forEach(consentIn::value);
        return consentIn;
      };
    }
  }
}
