package com.cboj.ciam.service.data.ais;

import com.cboj.ciam.api.ais.AisConsentDto;
import com.cboj.ciam.api.ais.AisOutputConsentDto;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.jpa.Consent;
import com.cboj.ciam.jpa.ConsentStatus;
import com.cboj.ciam.jpa.ais.AisConsent;
import com.cboj.ciam.jpa.ais.AisConsent_;
import com.cboj.ciam.jpa.ais.AisPermission;
import com.cboj.ciam.service.data.AsyncService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

/**
 * Service class for managing AisConsentData entities.
 */
@Service
public class AisConsentDataServiceImpl
    extends AsyncService<AisConsent, AisConsentDto>
    implements AisConsentDataService {

  @Value("${ciam.base.url}")
  private String baseUrl;

  private final AisConsentStorage aisConsentStorage;
  private final AisPermissionStorage aisPermissionStorage;
  private final Mapper<AisConsent, AisOutputConsentDto> outputMapper;

  @Autowired
  protected AisConsentDataServiceImpl(AisConsentStorage storage,
                                      AisPermissionStorage aisPermissionStorage,
                                      Mapper<AisConsentDto, AisConsent> mapper,
                                      Mapper<AisConsent, AisOutputConsentDto> outputMapper) {
    super(storage, mapper);
    this.aisPermissionStorage = aisPermissionStorage;
    this.aisConsentStorage = storage;
    this.outputMapper = outputMapper;
  }

  /**
   * Creates a consent response based on the provided AisConsentDto and HttpServletRequest.
   *
   * @param dto     The AisConsentDto object containing the consent data.
   * @param request The HttpServletRequest object to construct the login URL.
   * @return A CompletableFuture of AisConsentDto.
   */
  public CompletableFuture<AisOutputConsentDto> createConsentResponse(AisConsentDto dto,
                                                                      HttpServletRequest request) {
    AisConsent consent = mapper.apply(dto);
    return aisPermissionStorage.searchByCodes(dto.getPermissions())
        .thenCompose(aisPermissions -> {
          if (!CollectionUtils.isEmpty(aisPermissions) && consent.getPermissions() == null) {
            consent.setPermissions(new LinkedHashSet<>(aisPermissions.size()));
          }

          for (AisPermission permission : aisPermissions) {
            consent.getPermissions().add(permission);
          }
          return super.create(consent);
        })
        .thenApply(created -> {
          AisOutputConsentDto response = outputMapper.apply(created);
          response.setConsentRef(created.getConsentRef().toString());
          response.setLoginUrls(getLoginUris(created));
          return response;
        });
  }

  /**
   * Searches for expired AisConsent entities based on the specified expiry date.
   *
   * @param expiryDate The LocalDateTime representing the expiry date.
   * @return A CompletableFuture of List<AisConsent> containing the expired AisConsent entities.
   */
  @Override
  public CompletableFuture<List<AisConsent>> searchExpired(@NotNull LocalDateTime expiryDate) {
    return aisConsentStorage.searchExpiryDateBefore(expiryDate);
  }

  /**
   * Deletes the specified AisConsent entity by setting its status to REVOKED.
   *
   * @param entity The AisConsent entity to be deleted.
   * @return A CompletableFuture of ResponseEntity<Boolean> indicating the success of the deletion operation.
   */
  @Override
  public CompletableFuture<ResponseEntity<Boolean>> delete(AisConsent entity) {
    entity.setStatus(ConsentStatus.REVOKED);
    return aisConsentStorage.update(entity).thenApply(unused -> ResponseEntity.ok(true));
  }

  /**
   * Searches for AisConsent entities by client ID and consent statuses.
   *
   * @param clientId The client ID to search by.
   * @param statuses The consent statuses to search for.
   * @return A CompletableFuture of List&lt;AisConsent&gt; containing the matching AisConsent entities.
   */
  @Override
  public CompletableFuture<List<AisConsent>> searchByStatusesAndClientId(@NotNull String clientId,
                                                                         ConsentStatus... statuses) {
    return aisConsentStorage.searchByStatusesAndClientId(clientId, statuses);
  }

  /**
   * Retrieves the login URIs for the given consent.
   *
   * @param consent The Consent object for which to retrieve the login URIs.
   * @return A set of login URIs for the specified consent.
   */
  private Set<String> getLoginUris(Consent consent) {
    String uri = UriComponentsBuilder.fromUriString(baseUrl)
        .path(RestConstants.Paths.WEB.LOGIN)
        .queryParam(AisConsent_.CONSENT_REF, consent.getConsentRef())
        .build().toString();
    return Collections.singleton(uri);
  }
}
