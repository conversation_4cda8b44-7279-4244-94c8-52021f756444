package com.cboj.ciam.service.data.pis;

import com.cboj.ciam.jpa.pis.PaymentPurposeEntity;
import com.cboj.ciam.jpa.pis.PaymentPurposeCodeRepository;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
public class PaymentPurposeCodeService {

  private final PaymentPurposeCodeRepository repository;

  public PaymentPurposeCodeService(PaymentPurposeCodeRepository repository) {
    this.repository = repository;
  }

  public Optional<PaymentPurposeEntity> getByCategoryAndPurpose(String categoryCode, String purposeCode) {
    return repository.findByPaymentCategoryCodeAndPaymentPurposeCode(categoryCode, purposeCode);
  }
}
