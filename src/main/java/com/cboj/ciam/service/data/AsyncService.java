package com.cboj.ciam.service.data;

import com.cboj.ciam.api.BaseDto;
import com.cboj.ciam.jpa.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import com.cboj.ciam.api.Mapper;

/**
 * Class that provides asynchronous operations for managing entities of type T using a storage and a mapper.
 *
 * @param <T> the type of entity being managed
 * @param <K> the type of DTO used to create or update the entity
 */
public abstract class AsyncService<T extends BaseEntity, K extends BaseDto> {

  protected final Storage<T> storage;
  protected final Mapper<K, T> mapper;

  protected AsyncService(Storage<T> storage, Mapper<K, T> mapper) {
    this.storage = storage;
    this.mapper = mapper;
  }

  public CompletableFuture<Optional<T>> find(Long id) {
    return storage.find(id);
  }

  public CompletableFuture<Page<T>> search(Integer page, Integer perPage) {
    return storage.search(page, perPage);
  }

  public CompletableFuture<List<T>> search(@NotNull Specification<T> specification) {
    return storage.search(specification);
  }

  public CompletableFuture<T> create(K dto) {
    CompletableFuture<T> chain = CompletableFuture.supplyAsync(() -> mapper.apply(dto));
    return chain.thenCompose(storage::create);
  }

  public CompletableFuture<T> update(K dto) {
    CompletableFuture<T> chain = CompletableFuture.supplyAsync(() -> mapper.apply(dto));
    return chain.thenCompose(storage::update);
  }

  public CompletableFuture<T> create(T entity) {
    return storage.create(entity);
  }

  public CompletableFuture<T> update(T entity) {
    return storage.update(entity);
  }

  public CompletableFuture<List<T>> updateAll(@NotEmpty Collection<T> collection) {
    return storage.updateAll(collection);
  }

  public ResponseEntity<Boolean> delete(Long id) {
    storage.delete(id);
    return ResponseEntity.ok(true);
  }

  public CompletableFuture<ResponseEntity<Boolean>> delete(T entity) {
    storage.delete(entity.getId());
    return CompletableFuture.supplyAsync(() -> ResponseEntity.ok(true));
  }

  public CompletableFuture<T> findByAttr(@NotNull String attrName,
                                         @NotNull Object attrValue) {
    return searchByAttr(attrName, attrValue)
        .thenApply(
            result -> result.stream().findFirst().orElse(null)
        );
  }

  public CompletableFuture<T> findByAttrs(@NotEmpty Map<String, Object> data) {
    return searchByAttrs(data, null, null)
        .thenApply(
            result -> result.stream().findFirst().orElse(null)
        );
  }

  public CompletableFuture<List<T>> searchByAttr(@NotNull String attrName,
                                                 @NotNull Object attrValue) {
    return storage.searchByAttr(attrName, attrValue);
  }

  public CompletableFuture<Page<T>> searchByAttr(@NotNull String attrName,
                                                 @NotNull Object attrValue,
                                                 Integer page,
                                                 Integer perPage) {
    return storage.searchByAttr(attrName, attrValue, page, perPage);
  }

  public CompletableFuture<Page<T>> searchByAttrs(@NotEmpty Map<String, Object> data,
                                                  Integer page,
                                                  Integer perPage) {
    return storage.searchByAttrs(data, page, perPage);
  }
}
