package com.cboj.ciam.service.crypto;

import com.cboj.ciam.web.model.LoginModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JceOpenSSLPKCS8DecryptorProviderBuilder;
import org.bouncycastle.operator.InputDecryptorProvider;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStreamReader;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.util.Base64;

/**
 * Class provides methods for encrypting and decrypting data using specific algorithms.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EncryptionService {

  private final ResourceLoader resourceLoader;
  private final EncryptionKeysLoader keysLoader;

  @Value("${ciam.private.key.password}")
  private String password;

  private PrivateKey privateKey;

  @PostConstruct
  public void init() {
    Security.addProvider(new BouncyCastleProvider());
  }


  public String decryptUserName(LoginModel loginModel) {
    return decryptUserName(loginModel.getUserName(), loginModel.getUserNameKey());
  }

  /**
   * Decrypts the given username using RSA algorithm and AES decryption.
   *
   * @param userName The encrypted username to be decrypted.
   * @param key      The RSA private key used for decryption of the AES key.
   * @return The decrypted username.
   * @throws RuntimeException If there was an error decrypting the username.
   */
  public String decryptUserName(String userName, String key) {
    try {
      PrivateKey privateKey = loadPrivateKey();
      byte[] bytes = Base64.getDecoder().decode(key);

      Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding", "BC");
      cipher.init(Cipher.DECRYPT_MODE, privateKey);
      byte[] decryptedAesKey = cipher.doFinal(bytes);

      return decryptData(userName, decryptedAesKey);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Decrypts the given encrypted data using the AES algorithm.
   *
   * @param encryptedDataBase64 The encrypted data in Base64 format.
   * @param aesKey              The AES key used for decryption.
   * @return The decrypted data as a string.
   * @throws Exception If there is an error during decryption.
   */
  public String decryptData(String encryptedDataBase64, byte[] aesKey) throws Exception {
    byte[] encryptedData = Base64.getDecoder().decode(encryptedDataBase64);
    byte[] decryptedAesKey = Base64.getDecoder().decode(aesKey);
    byte[] iv = new byte[16]; // Ensure the IV is empty as used in encryption

    Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding", "BC");
    cipher.init(Cipher.DECRYPT_MODE,
        new SecretKeySpec(decryptedAesKey, "AES"),
        new GCMParameterSpec(128, iv));
    byte[] decryptedText = cipher.doFinal(encryptedData);
    return new String(decryptedText);
  }

  /**
   * Loads the public key resource.
   *
   * @return KeyResponse object containing the loaded public key information
   */
  public KeyResponse loadPublicKeyResource() {
    PublicKey key;
    String externalKey;
    try {
      key = keysLoader.loadPublicKey();
      externalKey = keysLoader.loadExternalPublicKey();
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
      return KeyResponse.builder()
          .success(false)
          .errorMessage(e.getLocalizedMessage())
          .errorCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
          .build();
    }

    return KeyResponse.builder()
        .success(true)
        .content(KeyResponse.Content.builder()
            .userKey(Base64.getEncoder().encodeToString(key.getEncoded()))
            .passKey(externalKey)
            .build())
        .build();
  }

  /**
   * Load the private key from the provided file path. The private key must be in the PEM format.
   *
   * @return The loaded private key.
   * @throws Exception if an error occurs while loading the private key.
   */
  private PrivateKey loadPrivateKey() throws Exception {
    if (privateKey != null) {
      return privateKey;
    }

    Resource resource = resourceLoader.getResource("classpath:" + "certs/ciam_private_key_encrypted.pem");
    try (PEMParser pemParser = new PEMParser(new InputStreamReader(resource.getInputStream()))) {
      Object object = pemParser.readObject();
      JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");

      if (object instanceof PKCS8EncryptedPrivateKeyInfo) {
        // The key is encrypted using PKCS8EncryptedPrivateKeyInfo
        InputDecryptorProvider decProv = new JceOpenSSLPKCS8DecryptorProviderBuilder()
            .setProvider("BC")
            .build(password.toCharArray());

        PrivateKeyInfo info = ((PKCS8EncryptedPrivateKeyInfo) object).decryptPrivateKeyInfo(decProv);
        privateKey = converter.getPrivateKey(info);
      } else if (object instanceof PEMKeyPair) {
        // The key is not encrypted
        PEMKeyPair keyPair = (PEMKeyPair) object;
        privateKey = converter.getPrivateKey(keyPair.getPrivateKeyInfo());
      } else {
        throw new IllegalArgumentException("Invalid PEM file");
      }
    }
    return privateKey;
  }

  @Data
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  @Builder
  @Schema(description = "Key resource response")
  public static class KeyResponse {
    private Content content;
    private String errorMessage;
    private Integer errorCode;
    private boolean success;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(
        ignoreUnknown = true
    )
    @Builder
    @Schema(description = "Content")
    private static class Content {
      private String userKey;
      private String passKey;
    }
  }
}
