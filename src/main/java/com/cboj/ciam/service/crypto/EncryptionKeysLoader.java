package com.cboj.ciam.service.crypto;

import com.cboj.ciam.config.CacheConfig;
import com.cboj.ciam.config.EncryptionServiceProperties;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * Class is responsible for loading encryption keys used by the application.
 */
@Slf4j
@Component
public class EncryptionKeysLoader {

  private final EncryptionServiceProperties properties;
  private final ResourceLoader resourceLoader;
  private final RestTemplate restTemplate;

  private final MultiValueMap<String, String> headers;
  private final String externalKeyUrl;

  public EncryptionKeysLoader(@Value("${ciam.codebase.key_url}") String externalKeyUrl,
                              EncryptionServiceProperties properties,
                              ResourceLoader resourceLoader,
                              RestTemplate restTemplate) {
    this.properties = properties;
    this.resourceLoader = resourceLoader;
    this.restTemplate = restTemplate;
    this.headers = createHeaders();
    this.externalKeyUrl = externalKeyUrl;
  }

  interface Headers {
    String CHANNEL_ID = "ChannelId";
    String BANK_ID = "BankId";
    String COUNTRY_CODE = "CountryCode";
    String IP = "IP";
    String DEVICE_ID = "DeviceID";
    String LATITUDE = "Latitude";
    String LONGITUDE = "Longitude";
    String PLATFORM = "Platform";
    String MOBILE_MODEL = "MobileModel";
    String APP_VERSION = "AppVersion";
    String IS_REFRESH_TOKEN = "IsRefreshToken";
    String KEY = "Key";
  }

  interface Payload {
    String PARENT_VERSION = "parentVersion";
    String VERSION = "version";
    String PLATFORM = "platform";
  }

  /**
   * Loads the public key from a PEM file located at "certs/ciam_public_key.pem".
   *
   * @return the loaded public key
   * @throws Exception if an error occurs while loading the public key
   */
  public PublicKey loadPublicKey() throws Exception {
    Resource resource = resourceLoader.getResource("classpath:" + "certs/ciam_public_key.pem");
    try (PemReader pemReader = new PemReader(new InputStreamReader(resource.getInputStream()))) {
      PemObject pemObject = pemReader.readPemObject();
      byte[] pemContent = pemObject.getContent();

      return KeyFactory
          .getInstance("RSA")
          .generatePublic(new X509EncodedKeySpec(pemContent));
    }
  }

  /**
   * Retrieves the external key from the processed request.
   *
   * @return the external key retrieved from the response body.
   */
  @Cacheable(value = CacheConfig.EXTERNAL_PUBLIC_KEY_CACHE_NAME)
  public String loadExternalPublicKey() {
    ResponseEntity<AppVersionResponse> responseEntity = processAppVersionRequest();

    if (!responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getBody() == null) {
      String message = String.format("Response does not have body or failed with code %s", responseEntity.getStatusCode());
      throw new RuntimeException(message);
    }

    AppVersionResponse response = processResponse(responseEntity.getBody());
    return response.getContent().getKey();
  }

  /**
   * Processes an HTTP request.
   *
   * @return the HTTP response received from the request
   */
  private ResponseEntity<AppVersionResponse> processAppVersionRequest() {
    log.info("Processing request for {}", externalKeyUrl);

    Map<String, String> payload = new HashMap<>();
    payload.put(Payload.PARENT_VERSION, properties.getParentVersion());
    payload.put(Payload.VERSION, properties.getVersion());
    payload.put(Payload.PLATFORM, properties.getPlatformPayload());

    return restTemplate.exchange(externalKeyUrl, HttpMethod.POST, new HttpEntity<>(payload, headers), AppVersionResponse.class);
  }

  /**
   * Process the given AppVersionResponse object and performs necessary checks.
   *
   * @param response The AppVersionResponse object that needs to be processed.
   * @return The processed AppVersionResponse object.
   * @throws RuntimeException if the response or status is null, or if the status isSuccess flag is false.
   */
  private AppVersionResponse processResponse(AppVersionResponse response) {
    if (response.getContent() == null || response.getStatus() == null || !response.getStatus().getIsSuccess()) {
      String message;
      if (response.getContent() == null) {
        message = "Empty response Content!";
      } else if (response.getStatus() == null) {
        message = "Empty response Status!";
      } else {
        message = String.format("Error response: code=%s message=%s", response.getStatus().getCode(),
            response.getStatus().getStatusMessage());
      }
      throw new RuntimeException(message);
    }
    return response;
  }

  /**
   * Creates and returns the HTTP headers for a request. The method creates a MultiValueMap object to store the headers. It adds the
   * necessary headers for encryption and authentication.
   *
   * @return The MultiValueMap object containing the created headers.
   */
  private MultiValueMap<String, String> createHeaders() {
    MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
    headers.add(Headers.CHANNEL_ID, properties.getChannelId());
    headers.add(Headers.BANK_ID, properties.getBankId());
    headers.add(Headers.COUNTRY_CODE, properties.getCountryCode());
    headers.add(Headers.IP, properties.getIp());
    headers.add(Headers.DEVICE_ID, properties.getDeviceId());
    headers.add(Headers.LATITUDE, properties.getLatitude());
    headers.add(Headers.LONGITUDE, properties.getLongitude());
    headers.add(Headers.PLATFORM, properties.getPlatform());
    headers.add(Headers.MOBILE_MODEL, properties.getMobileModel());
    headers.add(Headers.APP_VERSION, properties.getAppVersion());
    headers.add(Headers.IS_REFRESH_TOKEN, properties.getIsRefreshToken());
    headers.add(Headers.KEY, properties.getKey());
    return headers;
  }
}
