package com.cboj.ciam.service.crypto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Data
public class AppVersionResponse {

  private Status status;
  private Content content;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  @Data
  public static class Content {

    private Boolean isSuccess;
    private String message;
    private String key;
  }

  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonIgnoreProperties(
      ignoreUnknown = true
  )
  @Data
  public static class Status {

    private Boolean isSuccess;
    private String code;
    private String severity;
    private String statusMessage;
  }
}
