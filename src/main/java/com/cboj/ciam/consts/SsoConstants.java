package com.cboj.ciam.consts;

/**
 * Interface defines constant values used in Single Sign-On (SSO) operations.
 */
public interface SsoConstants {

  String ENABLE = "enable";
  String TOKEN_TYPE_BEARER = "Bearer";

  interface Client {
    String CLIENT_ID = "clientId";
    String CONSENT_REF_HEADER = "x-consent-id";
  }

  interface User {
    String SID_CLAIM_NAME = "sid";
    String SUB_CLAIM_NAME = "sub";
    String AZP_CLAIM_NAME = "azp";
    String USER_NAME = "username";
    String USER_PASS = "password";
    String EXACT = "exact";
  }

  interface OAuth2 {
    String CODE = "code";
    String TOKEN = "token";
    String CLIENT_ID = "client_id";
    String CLIENT_SECRET = "client_secret";
    String REDIRECT_URI = "redirect_uri";
    String SCOPE = "scope";
    String STATE = "state";
    String GRANT_TYPE = "grant_type";
    String RESPONSE_TYPE = "response_type";
    String ACCESS_TOKEN = "access_token";
    String TOKEN_TYPE = "token_type";
    String REFRESH_TOKEN = "refresh_token";
    String AUTHORIZATION_CODE = "authorization_code";
    String USERNAME="username";
    String PASSWORD = "password";
    String CLIENT_CREDENTIALS = "client_credentials";
    String SESSION_STATE = "session_state";
  }

  interface Scope {
    String ACCOUNTS = "accounts";
    String PAYMENTS = "payments";
    String FUNDS_CONFIRMATIONS = "fundsconfirmations";
    String ADMIN_API = "admin_api";
    String MOBILE_API = "mobile_api";
    String MULE_API = "mule_api";
  }
}
