package com.cboj.ciam.consts;

public interface SecurityConstants {

  /**
   * OpenAPI definition of OAuth2 security schema with the client credentials flow.
   */
  String OAUTH_CLIENT_CRED_SCHEME = "OAuth2 Client Credentials";

  /**
   * OpenAPI definition of OAuth2 security schema with the authorization code flow.
   */
  String OAUTH_AUTH_CODE_SCHEME = "OAuth2 Authorization Code";

  /**
   * OpenAPI definition of the bearer token authentication for admin APIschema.
   */
  String ADMIN_API_BEARER_TOKEN = "Admin API Bearer Token";

  /**
   * OpenAPI definition of the bearer token authentication for mobile API schema.
   */
  String MOBILE_API_BEARER_TOKEN = "Mobile API Bearer Token";

  /**
   * OpenAPI definition of the bearer token authentication for mule API schema.
   */
  String MULE_API_BEARER_TOKEN = "Mule API Bearer Token";
}
