package com.cboj.ciam.consts;

/**
 * Interface defines constants used in REST API endpoints and related logic.
 */
public interface RestConstants {
  String STRING_SPACE = " ";
  interface Authorization {
    String BASIC_AUTH_TYPE = "BASIC";
    String AUTH_SESSION_ID = "AUTH_SESSION_ID";
    String AUTH_URL_FORM = "form";
    String AUTH_URL_ACTION = "action";
  }

  interface Paths {
    String CERTS = "/oauth2/certs";
    String TOKEN = "/oauth2/token";
    String ADMIN_COMMON = "/admin";
    String ADMIN_AIS_CONSENTS = "/admin/ais/consents";
    String ADMIN_PIS_CONSENTS = "/admin/pis/consents";
    String ADMIN_CAF_CONSENTS = "/admin/caf/consents";
    String API_AIS_ACCOUNT_ACCESS_CONSENTS = "/api/ais/account-access-consents";
    String API_CAF_FUNDS_CONFIRMATION_CONSENTS = "/api/caf/funds-confirmation-consents";
    String API_PIS_DOMESTIC_PAYMENT_CONSENTS = "/api/pis/domestic-payment-consents";
    String API_PIS_INTERNAL_PAYMENT_CONSENTS = "/api/pis/internal-payment-consents";
    String MOBILE_COMMON = "/mobile";
    String MOBILE_AIS_CONSENTS = "/mobile/ais/consents";
    String MOBILE_PIS_CONSENTS = "/mobile/pis/consents";
    String MOBILE_CAF_CONSENTS = "/mobile/caf/consents";
    String MULE_AIS_CONSENTS = "/mule/ais/consents";
    String MULE_PIS_CONSENTS = "/mule/pis/consents";
    String MULE_CAF_CONSENTS = "/mule/caf/consents";

    interface WEB {
      String PREFIX = "/ob/web";
      String LOGIN = PREFIX + "/login";
      String SET_LOCALE = PREFIX + "/set_locale";
      String LOGIN_OTP = PREFIX + "/login/otp";
      String LOGIN_FAILED_OTP = PREFIX + "/login/failed_otp";
      String LOGIN_SUBMIT_OTP = PREFIX + "/login/submit_otp";
      String LOGIN_REQUEST_OTP = PREFIX + "/login/request_otp";
      String AIS_UPDATE_CONSENT = PREFIX + "/ais/update_consent";
      String PIS_UPDATE_CONSENT = PREFIX + "/pis/update_consent";
      String CAF_UPDATE_CONSENT = PREFIX + "/caf/update_consent";
      String PIS_TRANSACTION_INFO = PREFIX + "/pis/transaction_info";
      String PUBLIC_KEYS = PREFIX + "/public_keys";
    }
  }

  interface ErrorPage {
    String ACCOUNT_OWNERSHIP_EX = "accountOwnershipEx";
  }

  interface LoginPage {
    String ENCRYPTION_ENABLED = "encryptionEnabled";
    String DATA_ATTR = "data";
    String ERROR = "error";
    String CONSENT_ERROR = "consentError";
  }

  interface Templates {
    String LOGIN_PAGE = "login_page";
    String CONSENT_PAGE_AIS = "consent_page_ais";
    String CONSENT_PAGE_CAF = "consent_page_caf";
    String CONSENT_PAGE_PIS = "consent_page_pis";
    String OTP_PAGE = "otp_page";
    String ERROR_PAGE = "error";
  }

  interface AccountResponse {
    String RESPONSE = "response";
    String ACCOUNTS_BLOCK = "accounts";
    String CUSTOMERS_BLOCK = "customers";
    String CUSTOMER_ID = "customerId";
    String ACCOUNT_TITLE_BLOCK = "accountTitle";
    String ACCOUNT_TYPE = "accountType";
    String CATEGORY = "category";
    String ACCOUNT_ID = "accountId";
    String ACCOUNT_IBAN_NUMBER = "ibanNumber";
    String ACCOUNT_IBAN = "iban";
    String ACCOUNT_TITLE_EN = "en";
    String ACCOUNT_TITLE_AR = "ar";
    String CURRENCY = "currency";
    String STATUS = "status";
    String CATEGORY_DESCRIPTION = "categoryDescription";
    String SEPARATOR_FOR_VIEW = "-";
    String ACCOUNT_HOLDER_NAME = "accountHolderName";
    String EN = "en";
    String AR = "ar";

  }

  interface CustomerAliasAccountsResponse {
    String CUSTOMER_TYPE = "customerType";
    String TYPE = "type";
    String IBAN = "iban";
    String CURRENCY = "currency";
    String BIC = "bic";
    String ADDITIONAL_DETAILS = "additionalDetails";
  }

  interface ExchangeRateRequest {
    String FROM_CURRENCY = "fromCurrency";
    String TO_CURRENCY = "toCurrency";
    String CURRENCY_AMOUNT = "currencyAmount";
    String AMOUNT_CCY = "amtCCy";
  }

  interface ExchangeRateResponse {
    String RESPONSE_BLOCK = "response";
    String UTILITY = "utilities";
    String EXCHANGE_AMOUNT = "exchangedAmount";
    String SELL_RATE = "sellRate";
  }

  interface EligibilityResponse {
    String ELIGIBILITY = "eligibility";
  }

  interface Otp {
    String MESSAGE = "message";
    String MOBILE_NUMBER = "mobileNumber";
    String OTP = "otp";
    String OTP_REFERENCE_NUMBER = "otpReferenceNumber";
  }

  interface TransactionInfo {
    String CUSTOMER_REF_PAYLOAD_ATTR = "customerRef";
    String INITIATION_PAYLOAD_ATTR = "initiation";
  }

  interface Iban {
    String RESPONSE_BLOCK = "response";
    String IBAN_VALID_VALUE = "IVAL";
    String VALIDITY = "validity";
    String BIC = "bic";
  }
}
