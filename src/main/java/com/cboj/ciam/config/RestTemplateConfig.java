package com.cboj.ciam.config;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

/**
 * Configuration class for RestTemplate and TaskScheduler beans.
 */
@Configuration
public class RestTemplateConfig {

  /**
   * Creates a new instance of RestTemplate.
   *
   * @return a new RestTemplate instance
   */
  @Bean
  public RestTemplate restTemplate() {
    // Create a trust strategy that trusts all certificates
    TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

    // Build an SSL context using the trust strategy
    SSLContext sslContext;
    try {
      sslContext = SSLContextBuilder.create()
          .loadTrustMaterial(null, acceptingTrustStrategy)
          .build();
    } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
      throw new RuntimeException(e);
    }

    RequestConfig requestConfig =  RequestConfig.custom()
            .setConnectTimeout(10000)
            .setSocketTimeout(60000)
            .build();

    // Create an HttpClient that uses the custom SSL context and disables hostname verification
    HttpClient httpClient = HttpClients.custom()
        .setSSLContext(sslContext)
        .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
        .setDefaultRequestConfig(requestConfig)
        .build();


    // Create a RestTemplate using the custom HttpClient
    return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
  }

}
