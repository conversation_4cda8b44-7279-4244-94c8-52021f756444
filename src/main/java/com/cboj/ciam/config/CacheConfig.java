package com.cboj.ciam.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
public class CacheConfig {

  public static final String ACCOUNTS_CACHE_NAME = "accounts";
  public static final String CLIENTS_CACHE_NAME = "clients";
  public static final String ADMIN_TOKEN_CACHE_NAME = "admin-token";
  public static final String MULE_TOKEN_CACHE_NAME = "mule-token";
  public static final String EXTERNAL_PUBLIC_KEY_CACHE_NAME = "external-public-key";

  public static final String SINGLE_ACCOUNT_CACHE_NAME = "account";

  @Value("${ciam.cache.ttl.accounts}")
  Integer accountsTtl;
  @Value("${ciam.cache.ttl.clients}")
  Integer clientsTtl;
  @Value("${ciam.cache.ttl.admin-token}")
  Integer adminTokenTtl;
  @Value("${ciam.cache.ttl.mule-token}")
  Integer muleTokenTtl;
  @Value("${ciam.cache.ttl.external-public-key}")
  Integer externalPublicKeyTtl;

  @Bean
  public CacheManager cacheManager() {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();
    cacheManager.registerCustomCache(
        ACCOUNTS_CACHE_NAME,
        Caffeine.newBuilder()
            .expireAfterWrite(accountsTtl, TimeUnit.MINUTES)
            .build());

    cacheManager.registerCustomCache(
        CLIENTS_CACHE_NAME,
        Caffeine.newBuilder()
            .expireAfterWrite(clientsTtl, TimeUnit.MINUTES)
            .build());

    cacheManager.registerCustomCache(
        ADMIN_TOKEN_CACHE_NAME,
        Caffeine.newBuilder()
            .expireAfterWrite(adminTokenTtl, TimeUnit.MINUTES)
            .build());

    cacheManager.registerCustomCache(
        MULE_TOKEN_CACHE_NAME,
        Caffeine.newBuilder()
            .expireAfterWrite(muleTokenTtl, TimeUnit.HOURS)
            .build());

    cacheManager.registerCustomCache(
        EXTERNAL_PUBLIC_KEY_CACHE_NAME,
        Caffeine.newBuilder()
            .expireAfterWrite(externalPublicKeyTtl, TimeUnit.MINUTES)
            .build());

    cacheManager.registerCustomCache(
            SINGLE_ACCOUNT_CACHE_NAME,
            Caffeine.newBuilder()
                    .expireAfterWrite(accountsTtl, TimeUnit.MINUTES)
                    .build());

    return cacheManager;
  }

}
