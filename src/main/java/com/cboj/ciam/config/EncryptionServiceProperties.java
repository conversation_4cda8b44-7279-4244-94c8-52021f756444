package com.cboj.ciam.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "ciam.codebase")
public class EncryptionServiceProperties {

  // Headers
  private String channelId;
  private String bankId;
  private String countryCode;
  private String ip;
  private String deviceId;
  private String latitude;
  private String longitude;
  private String platform;
  private String mobileModel;
  private String appVersion;
  private String isRefreshToken;
  private String key;

  //Payload
  private String parentVersion;
  private String version;
  private String platformPayload;

}
