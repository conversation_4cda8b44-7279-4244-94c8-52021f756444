package com.cboj.ciam.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "ciam.payment")
public class PaymentServiceProperties {

  private Integer limitJodTransfer;
  private Integer limitFxTransfer;
  private String internalCurrencies;
  private String domesticCurrencies;
  private String internalCreditorAccountType;
  private String internalDebtorAccountType;
}
