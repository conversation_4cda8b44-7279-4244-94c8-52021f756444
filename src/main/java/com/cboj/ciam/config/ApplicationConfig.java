package com.cboj.ciam.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

@Configuration
public class ApplicationConfig implements WebMvcConfigurer {

  /**
   * Adds an interceptor to the interceptor registry.
   *
   * @param registry the interceptor registry to which the interceptor should be added
   */
  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(localeChangeInterceptor());
  }

  /**
   * Creates a LocaleChangeInterceptor instance and sets the paramName property to "lang".
   *
   * @return the LocaleChangeInterceptor instance
   */
  @Bean
  public LocaleChangeInterceptor localeChangeInterceptor() {
    LocaleChangeInterceptor lci = new LocaleChangeInterceptor();
    lci.setParamName("lang");
    return lci;
  }

  /**
   * Returns the locale resolver used in the application.
   *
   * @return the locale resolver
   */
  @Bean
  public LocaleResolver localeResolver() {
    return new SessionLocaleResolver();
  }

}
