package com.cboj.ciam.config.security.providers;

import com.cboj.ciam.config.security.tokens.MuleAuthenticationToken;
import com.cboj.ciam.service.keycloak.KeycloakTokenValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MuleValidateAuthenticationProvider extends ScopeValidateAuthenticationProvider {

  public MuleValidateAuthenticationProvider(@Value("${ciam.api.scope.mulesoft}")
                                            String scope,
                                            KeycloakTokenValidator tokenValidator) {
    super(scope, tokenValidator);
  }

  @Override
  public boolean supports(Class<?> authentication) {
    return MuleAuthenticationToken.class.isAssignableFrom(authentication);
  }
}
