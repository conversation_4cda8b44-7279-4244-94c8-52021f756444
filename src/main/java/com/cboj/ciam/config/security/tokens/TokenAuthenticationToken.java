package com.cboj.ciam.config.security.tokens;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public abstract class TokenAuthenticationToken extends AbstractAuthenticationToken {

  public TokenAuthenticationToken(Collection<? extends GrantedAuthority> authorities, String token) {
    super(authorities);
    super.setDetails(token);
  }

  @Override
  public Object getCredentials() {
    return null;
  }

  @Override
  public Object getPrincipal() {
    return null;
  }
}
