package com.cboj.ciam.config.security.tokens;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Represents a token for client token authentication.
 */
public class ClientTokenAuthenticationToken extends TokenAuthenticationToken {

  private static final long serialVersionUID = 5258322268692857510L;

  public ClientTokenAuthenticationToken(Collection<? extends GrantedAuthority> authorities, String token) {
    super(authorities, token);
  }
}
