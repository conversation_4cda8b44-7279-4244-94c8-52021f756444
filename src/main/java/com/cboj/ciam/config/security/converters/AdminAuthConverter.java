package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.config.security.tokens.AdminAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.List;

/**
 * Convert HttpServletRequest object to Authentication object for admin API.
 */
public class AdminAuthConverter extends TokenAuthConverter {

  @Override
  protected Authentication createAuthentication(List<GrantedAuthority> authorities, String token) {
    return new AdminAuthenticationToken(authorities, token);
  }
}
