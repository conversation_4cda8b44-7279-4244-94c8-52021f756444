package com.cboj.ciam.config.security.providers;

import com.cboj.ciam.JwtValidationException;
import com.cboj.ciam.service.keycloak.KeycloakTokenValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.util.CollectionUtils;


/**
 * An abstract class that provides authentication functionality and validates given scope.
 */
@Slf4j
public abstract class ScopeValidateAuthenticationProvider implements AuthenticationProvider {

  private final String scope;
  private final KeycloakTokenValidator tokenValidator;

  public ScopeValidateAuthenticationProvider(String scope, KeycloakTokenValidator tokenValidator) {
    this.scope = scope;
    this.tokenValidator = tokenValidator;
  }

  /**
   * Authenticates the user using the given authentication object.
   *
   * @param authentication The Authentication object containing the user's authorities.
   * @return The authenticated Authentication object.
   * @throws AuthenticationException if the authentication fails.
   */
  @Override
  public Authentication authenticate(Authentication authentication) throws AuthenticationException {
    if (CollectionUtils.isEmpty(authentication.getAuthorities())) {
      throw new AuthenticationCredentialsNotFoundException("No authorities found");
    }

    try {
      String token = (String) authentication.getDetails();

      boolean valid = tokenValidator.introspectToken(token);

      if (!valid) {
        throw new JwtValidationException("Invalid token");
      }
    } catch (JwtValidationException e) {
      log.error(e.getLocalizedMessage(), e);
      throw new UnauthenticatedException(e.getLocalizedMessage());
    }

    authentication.getAuthorities().stream()
        .filter(a -> a.getAuthority().equals(scope))
        .findFirst()
        .orElseThrow(() -> new UnauthenticatedException(String.format("No scope %s found", scope)));

    authentication.setAuthenticated(true);
    return authentication;
  }
}
