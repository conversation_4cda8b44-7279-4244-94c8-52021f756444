package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.config.security.tokens.MobileAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.List;

/**
 * Convert HttpServletRequest object to Authentication object for mobile API.
 */
public class MobileAuthConverter extends TokenAuthConverter {

  @Override
  protected Authentication createAuthentication(List<GrantedAuthority> authorities, String token) {
    return new MobileAuthenticationToken(authorities, token);
  }
}
