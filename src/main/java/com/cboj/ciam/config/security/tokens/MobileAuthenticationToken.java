package com.cboj.ciam.config.security.tokens;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Represents a token for mobile authentication.
 */
public class MobileAuthenticationToken extends TokenAuthenticationToken {

  public MobileAuthenticationToken(Collection<? extends GrantedAuthority> authorities, String token) {
    super(authorities, token);
  }
}
