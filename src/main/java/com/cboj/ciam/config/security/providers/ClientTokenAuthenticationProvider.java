package com.cboj.ciam.config.security.providers;

import com.cboj.ciam.JwtValidationException;
import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.config.security.tokens.ClientTokenAuthenticationToken;
import com.cboj.ciam.consts.SsoConstants;
import com.cboj.ciam.service.keycloak.KeycloakTokenValidator;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.text.ParseException;

@Slf4j
@Component
@RequiredArgsConstructor
public class ClientTokenAuthenticationProvider implements AuthenticationProvider {

  private final KeycloakTokenValidator tokenValidator;

  /**
   * Authenticates the provided authentication object by validating the token using the token validator.
   *
   * @param authentication The authentication object to authenticate, which must be an instance of {@link org.springframework.security.authentication.AbstractAuthenticationToken}.
   *                       The authentication object should contain the token as the details.
   * @return The authenticated authentication object with the 'authenticated' property set to 'true'.
   * @throws AuthenticationException If the authentication fails, either due to an invalid token or an error during token validation.
   */
  @Override public Authentication authenticate(Authentication authentication) throws AuthenticationException {
    try {
      String token = (String) authentication.getDetails();

      boolean valid = tokenValidator.introspectToken(token);

      if (!valid) {
        throw new JwtValidationException("Invalid token");
      }

      String clientId;
      try {
        JWTClaimsSet claims = JWTParser.parse(token).getJWTClaimsSet();
        clientId = claims.getStringClaim(SsoConstants.Client.CLIENT_ID);
      } catch (ParseException ex) {
        throw new UnauthorizedException(ex.getLocalizedMessage());
      }

      if (StringUtils.isEmpty(clientId)) {
        log.error("Invalid token. Client id is required");
        throw new UnauthenticatedException("Invalid token");
      }

      authentication.setAuthenticated(true);
    } catch (JwtValidationException e) {
      log.error(e.getLocalizedMessage(), e);
      throw new UnauthenticatedException(e.getLocalizedMessage());
    }
    return authentication;
  }

  @Override public boolean supports(Class<?> authentication) {
    return ClientTokenAuthenticationToken.class.isAssignableFrom(authentication);
  }
}
