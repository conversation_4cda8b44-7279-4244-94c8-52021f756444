package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.config.security.tokens.SessionAuthentication;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Collections;

public class SessionAuthConverter implements AuthenticationConverter {

  /**
   * Converts a HttpServletRequest into an Authentication object.
   *
   * @param request the HttpServletRequest to be converted
   * @return an Authentication object representing the session authentication
   * @throws UnauthorizedException if no session is found in the request
   */
  @Override
  public Authentication convert(HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    if (session == null) {
      throw new UnauthorizedException("No session found");
    }
    return new SessionAuthentication(Collections.emptyMap());
  }
}
