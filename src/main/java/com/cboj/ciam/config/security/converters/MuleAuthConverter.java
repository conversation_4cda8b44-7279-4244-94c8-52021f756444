package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.config.security.tokens.MuleAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.List;

/**
 * Convert HttpServletRequest object to Authentication object for MuleSoft API.
 */
public class MuleAuthConverter extends TokenAuthConverter {

  @Override
  protected Authentication createAuthentication(List<GrantedAuthority> authorities, String token) {
    return new MuleAuthenticationToken(authorities, token);
  }
}
