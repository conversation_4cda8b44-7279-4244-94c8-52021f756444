package com.cboj.ciam.config.security.tokens;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Represents a token for admin authentication.
 */
public class AdminAuthenticationToken extends TokenAuthenticationToken {

  public AdminAuthenticationToken(Collection<? extends GrantedAuthority> authorities,
                                  String token) {
    super(authorities, token);
  }
}
