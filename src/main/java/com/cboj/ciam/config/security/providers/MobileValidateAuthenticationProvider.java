package com.cboj.ciam.config.security.providers;

import com.cboj.ciam.config.security.tokens.MobileAuthenticationToken;
import com.cboj.ciam.service.keycloak.KeycloakTokenValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MobileValidateAuthenticationProvider extends ScopeValidateAuthenticationProvider {

  public MobileValidateAuthenticationProvider(@Value("${ciam.api.scope.mobile}")
                                              String scope,
                                              KeycloakTokenValidator tokenValidator) {
    super(scope, tokenValidator);
  }

  @Override
  public boolean supports(Class<?> authentication) {
    return MobileAuthenticationToken.class.isAssignableFrom(authentication);
  }
}
