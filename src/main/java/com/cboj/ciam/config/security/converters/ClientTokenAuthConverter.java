package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.config.security.tokens.ClientTokenAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.List;

/**
 * Converts the HttpServletRequest object to an Authentication object for client token authentication.
 */
public class ClientTokenAuthConverter extends TokenAuthConverter {

  @Override
  protected Authentication createAuthentication(List<GrantedAuthority> authorities, String token) {
    return new ClientTokenAuthenticationToken(authorities, token);
  }
}
