package com.cboj.ciam.config.security;

import com.cboj.ciam.ApplicationAccessDeniedHandler;
import com.cboj.ciam.config.security.converters.*;
import com.cboj.ciam.config.security.points.ClientAuthenticationEntryPoint;
import com.cboj.ciam.config.security.points.InternalApiAuthenticationEntryPoint;
import com.cboj.ciam.config.security.providers.*;
import com.cboj.ciam.consts.RestConstants;
import com.cboj.ciam.consts.SessionConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationConverter;
import org.springframework.security.web.authentication.AuthenticationEntryPointFailureHandler;
import org.springframework.security.web.authentication.AuthenticationFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.session.jdbc.config.annotation.web.http.EnableJdbcHttpSession;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableWebSecurity
@EnableJdbcHttpSession
@RequiredArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

  @Value("${keycloak.uri.base-uri}")
  private String keycloakUrl;

  private final MobileValidateAuthenticationProvider mobileAuthenticationProvider;
  private final ClientTokenAuthenticationProvider clientTokenAuthenticationProvider;
  private final AdminValidateAuthenticationProvider adminAuthenticationProvider;
  private final MuleValidateAuthenticationProvider muleAuthenticationProvider;
  private final SessionAuthenticationProvider sessionAuthenticationProvider;

  private final ApplicationAccessDeniedHandler accessDeniedHandler;

  private final String[] resources = new String[] {
      RestConstants.Paths.WEB.PREFIX + "/js/**",
      RestConstants.Paths.WEB.PREFIX + "/css/**",
      RestConstants.Paths.WEB.PREFIX + "/styles.css",
      RestConstants.Paths.WEB.PREFIX + "/images/**",
      "/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**"
  };

  private final String[] anonymousPaths = new String[] {
      RestConstants.Paths.WEB.LOGIN,
      RestConstants.Paths.WEB.LOGIN_OTP,
      RestConstants.Paths.WEB.SET_LOCALE,
      RestConstants.Paths.WEB.PUBLIC_KEYS,
      RestConstants.Paths.TOKEN,
      RestConstants.Paths.CERTS
  };

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
    AuthenticationManager authManager = authenticationManager();

    HttpSessionCsrfTokenRepository repository = new HttpSessionCsrfTokenRepository();
    repository.setSessionAttributeName(SessionConstants.CSRF_TOKEN_ATTR);

    httpSecurity
        .requiresChannel()
        .anyRequest()
        .requiresSecure()
        .and()
        .headers(headers -> headers
            .xssProtection()
            .and()
            .frameOptions().deny()
            .httpStrictTransportSecurity(hsts -> hsts
                .includeSubDomains(true)
                .maxAgeInSeconds(31536000)
            )
            .contentTypeOptions(withDefaults())
            .referrerPolicy(referrer -> referrer
                .policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
            )
        )
        .csrf(csrf -> csrf
            .requireCsrfProtectionMatcher(new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.PREFIX + "/**")))
            .ignoringAntMatchers(RestConstants.Paths.WEB.LOGIN, RestConstants.Paths.WEB.PUBLIC_KEYS,
                RestConstants.Paths.WEB.SET_LOCALE)
            .ignoringAntMatchers(resources)
            .csrfTokenRepository(repository)
        )
        .authorizeRequests(authorize -> authorize
            .antMatchers(anonymousPaths).permitAll()
            .antMatchers(resources).permitAll()
            .anyRequest().authenticated()
        )
        .addFilterAfter(getContentSecurityPolicyFilter(), CsrfFilter.class)
        .addFilterBefore(sessionAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(sessionOtpAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(mobileAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(mulesoftAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(adminAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .addFilterBefore(clientTokenAuthenticationFilter(authManager), UsernamePasswordAuthenticationFilter.class)
        .sessionManagement(session -> session
            .sessionCreationPolicy(SessionCreationPolicy.NEVER)
        )
        .exceptionHandling(exception -> exception
            .accessDeniedHandler(accessDeniedHandler)
        );

    return httpSecurity.build();
  }

  @Bean
  public AuthenticationManager authenticationManager() {
    return new ProviderManager(mobileAuthenticationProvider, clientTokenAuthenticationProvider,
        adminAuthenticationProvider, muleAuthenticationProvider, sessionAuthenticationProvider);
  }

  @Bean
  public AuthenticationFilter sessionAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new SessionAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.LOGIN_OTP)),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.LOGIN_REQUEST_OTP),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.LOGIN_SUBMIT_OTP),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.LOGIN_FAILED_OTP)));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new InternalApiAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public AuthenticationFilter sessionOtpAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new SessionOtpAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.PIS_TRANSACTION_INFO)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.AIS_UPDATE_CONSENT)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.PIS_UPDATE_CONSENT)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.WEB.CAF_UPDATE_CONSENT)),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.PIS_UPDATE_CONSENT),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.CAF_UPDATE_CONSENT),
            new AntPathRequestMatcher(RestConstants.Paths.WEB.AIS_UPDATE_CONSENT)));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new InternalApiAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public AuthenticationFilter clientTokenAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new ClientTokenAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.API_AIS_ACCOUNT_ACCESS_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.API_PIS_INTERNAL_PAYMENT_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.API_PIS_DOMESTIC_PAYMENT_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.API_CAF_FUNDS_CONFIRMATION_CONSENTS)),
            new AntPathRequestMatcher(RestConstants.Paths.API_AIS_ACCOUNT_ACCESS_CONSENTS),
            new AntPathRequestMatcher(RestConstants.Paths.API_PIS_INTERNAL_PAYMENT_CONSENTS),
            new AntPathRequestMatcher(RestConstants.Paths.API_PIS_DOMESTIC_PAYMENT_CONSENTS),
            new AntPathRequestMatcher(RestConstants.Paths.API_CAF_FUNDS_CONFIRMATION_CONSENTS)));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new ClientAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public AuthenticationFilter adminAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new AdminAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(RestConstants.Paths.ADMIN_AIS_CONSENTS),
            new AntPathRequestMatcher(RestConstants.Paths.ADMIN_CAF_CONSENTS),
            new AntPathRequestMatcher(RestConstants.Paths.ADMIN_PIS_CONSENTS),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.ADMIN_COMMON)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.ADMIN_AIS_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.ADMIN_CAF_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.ADMIN_PIS_CONSENTS))));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new InternalApiAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public AuthenticationFilter mobileAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new MobileAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MOBILE_COMMON)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MOBILE_AIS_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MOBILE_CAF_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MOBILE_PIS_CONSENTS))));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new InternalApiAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public AuthenticationFilter mulesoftAuthenticationFilter(AuthenticationManager authenticationManager) {
    AuthenticationFilter filter = getAuthenticationFilter(authenticationManager, new MuleAuthConverter());
    filter.setRequestMatcher(
        new OrRequestMatcher(
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MULE_AIS_CONSENTS)),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MULE_CAF_CONSENTS)),
            new AntPathRequestMatcher(RestConstants.Paths.MULE_PIS_CONSENTS),
            new AntPathRequestMatcher(withWildcard(RestConstants.Paths.MULE_PIS_CONSENTS))));
    filter.setFailureHandler(new AuthenticationEntryPointFailureHandler(new InternalApiAuthenticationEntryPoint()));
    return filter;
  }

  @Bean
  public CookieSerializer cookieSerializer() {
    DefaultCookieSerializer serializer = new DefaultCookieSerializer();
    serializer.setSameSite("Strict");
    return serializer;
  }

  private AuthenticationFilter getAuthenticationFilter(AuthenticationManager authenticationManager,
                                                       AuthenticationConverter authenticationConverter) {
    AuthenticationFilter filter = new AuthenticationFilter(authenticationManager, authenticationConverter);
    filter.setSuccessHandler((request, response, authentication) -> {});
    return filter;
  }

  private OncePerRequestFilter getContentSecurityPolicyFilter() {
    return new OncePerRequestFilter() {
      @Override
      protected void doFilterInternal(HttpServletRequest request,
                                      HttpServletResponse response,
                                      FilterChain filterChain) throws ServletException, IOException {
        CsrfToken csrfToken = (CsrfToken) request.getAttribute(CsrfToken.class.getName());
        if (csrfToken != null) {
          response.addHeader("Content-Security-Policy",
              "connect-src 'self' " + keycloakUrl
                  + "; default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; font-src 'self';");
        }
        filterChain.doFilter(request, response);
      }
    };
  }

  private String withWildcard(String path) {
    return path + "/*";
  }
}
