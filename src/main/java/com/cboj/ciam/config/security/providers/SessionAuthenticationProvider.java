package com.cboj.ciam.config.security.providers;

import com.cboj.ciam.config.security.tokens.SessionAuthentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SessionAuthenticationProvider implements AuthenticationProvider {

  @Override
  public Authentication authenticate(Authentication authentication) throws AuthenticationException {
    if (authentication.getDetails() != null) {
      authentication.setAuthenticated(true);
      return authentication;
    }
    return null;
  }

  @Override
  public boolean supports(Class<?> authentication) {
    return SessionAuthentication.class.isAssignableFrom(authentication);
  }
}
