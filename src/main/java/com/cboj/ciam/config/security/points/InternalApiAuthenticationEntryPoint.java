package com.cboj.ciam.config.security.points;

import com.cboj.ciam.api.InternalApiErrorDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Handles unauthorized requests and returns an appropriate error response in JSON format.
 */
public class InternalApiAuthenticationEntryPoint implements AuthenticationEntryPoint {

  private final ObjectMapper mapper = new ObjectMapper();

  @Override
  public void commence(HttpServletRequest request,
                       HttpServletResponse response,
                       AuthenticationException authException) throws IOException {
    response.setStatus(HttpStatus.UNAUTHORIZED.value());
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    InternalApiErrorDto apiErrorResponse = new InternalApiErrorDto(HttpStatus.UNAUTHORIZED, authException.getMessage());
    response.getWriter().write(mapper.writeValueAsString(apiErrorResponse));
  }
}
