package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.UnauthorizedException;
import com.cboj.ciam.config.security.tokens.SessionAuthentication;
import com.cboj.ciam.consts.SessionConstants;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Collections;

public class SessionOtpAuthConverter implements AuthenticationConverter {


  /**
   * Converts the HttpServletRequest into an Authentication object.
   *
   * @param request The HttpServletRequest to convert into an Authentication object.
   * @return The converted Authentication object.
   * @throws UnauthorizedException if no session is found or session attribute SUCCESSFUL_OTP_ATTR is null.
   */
  @Override
  public Authentication convert(HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    if (session == null || session.getAttribute(SessionConstants.SUCCESSFUL_OTP_ATTR) == null) {
      throw new UnauthorizedException("No session found");
    }
    return new SessionAuthentication(Collections.emptyMap());
  }
}
