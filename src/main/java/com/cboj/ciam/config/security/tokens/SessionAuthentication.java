package com.cboj.ciam.config.security.tokens;

import org.springframework.security.authentication.AbstractAuthenticationToken;

import java.util.Map;

/**
 * Represents session-based authentication.
 */
public class SessionAuthentication extends AbstractAuthenticationToken {

  public SessionAuthentication(Map<String, Object> sessionAttrs) {
    super(null);
    super.setDetails(sessionAttrs);
  }

  @Override
  public Object getCredentials() {
    return null;
  }

  @Override
  public Object getPrincipal() {
    return null;
  }
}
