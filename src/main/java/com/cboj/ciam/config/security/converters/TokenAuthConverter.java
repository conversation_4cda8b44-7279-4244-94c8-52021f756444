package com.cboj.ciam.config.security.converters;

import com.cboj.ciam.consts.SsoConstants;
import com.nimbusds.jwt.JWTParser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.authentication.AuthenticationConverter;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public abstract class TokenAuthConverter implements AuthenticationConverter {

  @Override
  public Authentication convert(HttpServletRequest request) {
    String bearerToken = request.getHeader(HttpHeaders.AUTHORIZATION);

    if (bearerToken == null || !bearerToken.startsWith(SsoConstants.TOKEN_TYPE_BEARER)) {
      throw new AuthenticationCredentialsNotFoundException("Token is not valid");
    }

    try {
      String token = bearerToken.substring(SsoConstants.TOKEN_TYPE_BEARER.length() + 1);
      String scopeString = JWTParser.parse(token).getJWTClaimsSet().getStringClaim(SsoConstants.OAuth2.SCOPE);

      List<GrantedAuthority> authorities = StringUtils.isEmpty(scopeString)
          ? null
          : Arrays.stream(scopeString.split(" "))
              .map(SimpleGrantedAuthority::new)
              .collect(Collectors.toList());

      return createAuthentication(authorities, token);
    } catch (ParseException e) {
      throw new AuthenticationCredentialsNotFoundException("Token is not valid. Parsing failed");
    }
  }

  protected abstract Authentication createAuthentication(List<GrantedAuthority> authorities, String token);
}
