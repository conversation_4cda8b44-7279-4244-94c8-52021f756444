package com.cboj.ciam.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "ciam.mulesoft")
public class MulesoftServiceProperties {

  private String otpValidateUrl;
  private String otpRequestUrl;
  private String accountsUrl;
  private String accountsInfoUrl;
  private String eligibilityUrl;
  private String ibanValidationUrl;
  private String customerAccountsUrl;
  private String bicUrl;
  private String idParameter;
  private String ibanParameter;
  private String customerIdParameter;
  private String aliasIdParameter;
  private String aliasTypeParameter;
  private String accountsPart;
  private String eligibilityPart;
  private String channel;
  private String bank;
  private String clientId;
  private String clientSecret;
  private String accountStatus;
  private String otpTemplate;
  private Integer submitOtpAttempts;
  private Integer requestOtpAttempts;
  private Integer otpSessionTime;
  private Integer renewOtpSessionTime;
  private Integer retryAttemptsCount;
  private String transactionInfoUrl;

}
