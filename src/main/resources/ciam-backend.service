# This is an example systemd file to run ciam-backend as a Linux service.
# Make sure that all the placeholder values have the right values before running it.

[Unit]
Description==CIAM Custom Backend Application
After=network.target

[Service]
User=<process-username>
WorkingDirectory=<app-path>
ExecStart=/usr/bin/java -jar <app-path>/ciam-backend.jar --spring.profiles.active=<env> --jasypt.encryptor.password=<encryption-passwd>
SyslogIdentifier=ciam-backend
Restart=always
StandardOutput=syslog
StandardError=syslog
SuccessExitStatus=143

[Install]
WantedBy=multi-user.target
