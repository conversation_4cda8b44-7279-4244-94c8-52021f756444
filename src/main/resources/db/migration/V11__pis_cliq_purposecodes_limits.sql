create table pis_cliq_purpose_codes_limits
(
    id                    bigint identity (1,1) not null primary key,
    create_date           datetime not null,
    update_date           datetime not null,
    payment_category_code varchar(255),
    payment_purpose_code  varchar(255),
    limit                 numeric(6),
    description           varchar(max)
);

INSERT INTO [dbo].[pis_cliq_purpose_codes_limits]
([create_date]
    ,[update_date]
    ,[payment_category_code]
    ,[payment_purpose_code]
    ,[limit]
    ,[description])
VALUES
    (GETDATE(), GETDATE(),'IBAN','*********',10000,'Transfer to Friend or Family via online/mobile banking'),
    (GETDATE(), GETDATE(),'IBAN','*********',10000,'Transfer to Own Account via online/mobile banking'),
    (GETDATE(), GETDATE(),'IBAN','*********',10000,'Goods/Services payment from an individual account via online/mobile banking'),
    (GETDATE(), GETDATE(),'IBAN','*********',10000,'Loan installment payment from individual account via online/mobile banking'),
    (GETDATE(), GETDATE(),'IBAN','*********',1000,'Transfer for Aid or Charity or Grant from individual account via online/mobile banking'),
    (GETDATE(), GETDATE(),'DFLT_Individual','*********',10000,'To Friend or Family at a bank'),
    (GETDATE(), GETDATE(),'DFLT_Individual','*********',10000,'Transfer to Own Account at a bank'),
    (GETDATE(), GETDATE(),'DFLT_Individual','*********',10000,'Transfer to Own Account via online/mobile banking for loan installment at another bank'),
    (GETDATE(), GETDATE(),'DFLT_Individual','*********',10000,'Transfer for Aid or Charity or Grant from an individual account to an individual account via online/mobile banking'),
    (GETDATE(), GETDATE(),'DFLT_Legal','*********',10000,'Transfer from my personal account at the bank to my business account'),
    (GETDATE(), GETDATE(),'DFLT_Legal','*********',10000,'Goods/Services purchases from an individual account to a legal account via online/mobile'),
    (GETDATE(), GETDATE(),'DFLT_Legal','*********',10000,'Loan installment payment from an individual to business entity (lending entity) (P2B) '),
    (GETDATE(), GETDATE(),'DFLT_Legal','*********',10000,'Transfer for Aid or Charity or Grant from an individual account to an individual account via online/mobile banking'),
    (GETDATE(), GETDATE(),'BWALLET','*********',10000,'Transfer from my personal account at the bank to my business wallet'),
    (GETDATE(), GETDATE(),'BWALLET','*********',10000,'Goods/Services payment from an individual account to a BWALLET (P2B) via online/mobile banking'),
    (GETDATE(), GETDATE(),'BWALLET','*********',10000,'Loan installment payment from an individual to a BWALLET (lending entity) via online/mobile banking (P2B)'),
    (GETDATE(), GETDATE(),'BWALLET','*********',10000,'Transfer for Aid or Charity or Grant to a business wallet via online/mobile banking'),
    (GETDATE(), GETDATE(),'PWALLET','*********',2000,'Wallet (PWALLET)'),
    (GETDATE(), GETDATE(),'PWALLET','*********',2000,'Transfer from my personal account at the bank to my Personal wallet'),
    (GETDATE(), GETDATE(),'PWALLET','*********',10000,'Goods/Services payment to a personal wallet via online/mobile banking'),
    (GETDATE(), GETDATE(),'PWALLET','*********',2000,'Transfer for Aid or Charity or Grant to a personal wallet via online/mobile banking'),
    (GETDATE(), GETDATE(),'MWALLET','*********',5000,'Transfer from my personal account at the bank to my merchant wallet'),
    (GETDATE(), GETDATE(),'MWALLET','*********',5000,'Goods/Services payment to a merchant wallet via online/mobile banking')
