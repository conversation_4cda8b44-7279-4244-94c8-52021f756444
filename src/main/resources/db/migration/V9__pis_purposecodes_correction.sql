truncate table pis_paymentpurpose_codes;

INSERT INTO [dbo].[pis_paymentpurpose_codes]
           ([create_date]
           ,[update_date]
           ,[payment_category_code]
           ,[description_payment_category]
           ,[payment_purpose_code]
           ,[description_payment_purpose])
VALUES
(GETDATE(), GETDATE(),'Personal','Personal','0101','Invoice Payment and Purchase'),
(GETDATE(), GETDATE(),'Personal','Personal','0102','Utility Bill Payment'),
(GETDATE(), GETDATE(),'Personal','Personal','0103','Prepaid Cards Recharging'),
(GETDATE(), GETDATE(),'Personal','Personal','0104','Standing Orders'),
(GETDATE(), GETDATE(),'Personal','Personal','0105','Personal Donations'),
(GETDATE(), GETDATE(),'Personal','Personal','0106','Family Assistance and Expenses'),
(GETDATE(), GETDATE(),'Personal','Personal','0107','Individual Social Security Subscription'),
(GETDATE(), GETDATE(),'Personal','Personal','0108','Associations Subscriptions'),
(GETDATE(), GETDATE(),'Personal','Personal','0109','Saving and Funding Account '),
(GETDATE(), GETDATE(),'Personal','Personal','0110','Heritance'),
(GETDATE(), GETDATE(),'Personal','Personal','0111','End of Service indemnity'),
(GETDATE(), GETDATE(),'Personal','Personal','0112','Trust '),
(GETDATE(), GETDATE(),'Personal','Personal','0113','Court Transfer'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0201','Public Sector Employees Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0202','Laborers Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0203','Private Sector Staff Salaries '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0204','Jordanian Diplomatic Staff Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0205','Foreign Diplomatic Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0206','Overseas Incoming Salaries  '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0207','Civil / Military Retirement Salaries '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0208','Social Security Retirement Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0209','Establishment Social Security Subscription'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0210','Public Sector Employees Bonus'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0211','Laborers Bonus'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0212','Private Sector Staff Bonus '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','0213','Income Tax Refunds'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0301','Investment Revenues '),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0302','Brokrage Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0303','Insurance '),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0304','Subscriptions to International Nonmonetary Organizations'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0305','Local Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0306','External Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','0307','Tender Bond Guarantee'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','0401','Air Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','0402','Land Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','0403','Sea Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','0404','Travel and Tourism'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','0501','Governmental Delegation Transfers'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','0502','Private Sector Delegation Transfers'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','0503','Governmental Education'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','0504','Private Sector Education'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','0601','Public Sector Exportation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','0602','Private Sector Exportation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','0603','Public Sector Importation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','0604','Private Sector Importation'),
(GETDATE(), GETDATE(),'External Aid','External Aid','0701','Religious Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','0702','International Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','0703','Arab Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','0704','UN Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','0705','Charity Communities Aid'),
(GETDATE(), GETDATE(),'Services','Services','0801','Telecommunication Services'),
(GETDATE(), GETDATE(),'Services','Services','0802','Financial Services'),
(GETDATE(), GETDATE(),'Services','Services','0803','Information Technology Services'),
(GETDATE(), GETDATE(),'Services','Services','0804','Consulting Services'),
(GETDATE(), GETDATE(),'Services','Services','0805','Construction Services'),
(GETDATE(), GETDATE(),'Services','Services','0806','Maintenance and Assembling Services'),
(GETDATE(), GETDATE(),'Services','Services','0807','Marketing and Media Services'),
(GETDATE(), GETDATE(),'Services','Services','0808','Mining Services'),
(GETDATE(), GETDATE(),'Services','Services','0809','Medical and Health Services'),
(GETDATE(), GETDATE(),'Services','Services','0810','Cultural, Educational and Entertainment  Services'),
(GETDATE(), GETDATE(),'Services','Services','0811','Rental Expenses'),
(GETDATE(), GETDATE(),'Services','Services','0812','Real Estate'),
(GETDATE(), GETDATE(),'Services','Services','0813','Taxes'),
(GETDATE(), GETDATE(),'Services','Services','0814','Fees'),
(GETDATE(), GETDATE(),'Services','Services','0815','Commissions'),
(GETDATE(), GETDATE(),'Services','Services','0816','Franchise and License Fees'),
(GETDATE(), GETDATE(),'Services','Services','0817','Cheque Collection'),
(GETDATE(), GETDATE(),'Services','Services','0818','Membership Fees'),
(GETDATE(), GETDATE(),'Funding','Funding','0901','Municipality Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','0902','Government Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','0903','Private Sector Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','0904','External Incoming Funds'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1001','International Communities and Embassies Remittances'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1002','Permanent Diplomatic Missions'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1003','Temporary Diplomatic Missions'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1004','Jordanian Embassies Income'),
(GETDATE(), GETDATE(),'Loans','Loans','1101','Long-Term Loans Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1102','Long-Term Loans interest Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1103','Short-Term Loans Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1104','Short-Term Loans interest Installments  / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1105','Long-Term Loans Installments / Private Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1106','Long-Term Loans Interest Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1107','Short-Term Loans Installments /Private  Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1108','Short-Term Loans Interest Installments / Private Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1109','Loans Installments Against Governmental Guarantee'),
(GETDATE(), GETDATE(),'Loans','Loans','1110','Loans Interest Installments  Against Governmental Guarantee'),
(GETDATE(), GETDATE(),'Loans','Loans','1111','Credit Card Payment '),
(GETDATE(), GETDATE(),'Loans','Loans','1112','Personal Loan Payment'),
(GETDATE(), GETDATE(),'General','General','1201','Rerouting'),
(GETDATE(), GETDATE(),'General','General','1202','Scientific Research Support')
