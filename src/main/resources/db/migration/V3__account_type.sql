insert into ais_consent_account_type (create_date, update_date, account_class, gb_code, description_en, description_ar)
values (GETDATE(), GETDATE(),
        '1010',
        'Overdraft',
        'Overdraft',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1022',
        'NBFI-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1023',
        'NBFI-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1051',
        'Select-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1076',
        'Financially Exc',
        'Basic Account',
        N'حساب أساسي'),
       (GETDATE(), GETDATE(),
        '1101',
        'Regular-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1102',
        'Regular-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1103',
        'Regular-Payroll',
        'Payroll',
        N'حساب رواتب'),
       (GETDATE(), GETDATE(),
        '1109',
        'Reg-Paybonus',
        'Salary Bonus Account',
        N'حساب مكافأة الراتب'),
       (GETDATE(), GETDATE(),
        '1110',
        'Makaseb-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1126',
        'Select-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1127',
        'Select-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1128',
        'Select-Payroll',
        'Payroll',
        N'حساب رواتب'),
       (GETDATE(), GETDATE(),
        '1134',
        'Slct-Paybonus',
        'Salary Bonus Account',
        N'حساب مكافأة الراتب'),
       (GETDATE(), GETDATE(),
        '1145',
        'Private-Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1176',
        'SME - Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1177',
        'SME - Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1226',
        'Corp - Current',
        'Current Account',
        N'حسابجاري'),
       (GETDATE(), GETDATE(),
        '1227',
        'Corp - Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1276',
        'Comm- Curr',
        'Current Interest Bearing',
        N'حساب جاري بفائدة'),
       (GETDATE(), GETDATE(),
        '1277',
        'Commercial - Cu',
        'Current Non Interest Bearing',
        N'حساب جاري بدون بفائدة'),
       (GETDATE(), GETDATE(),
        '1326',
        'Upper Reg-Curr',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1327',
        'UpReg-CurrNonIn',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1328',
        'UpReg-Payroll',
        'Current Payroll Account',
        N'حساب جاري رواتب'),
       (GETDATE(), GETDATE(),
        '1332',
        'Payroll Bonus',
        'Salary Bonus Account',
        N'حساب مكافأة الراتب'),
       (GETDATE(), GETDATE(),
        '1605',
        'SME Call',
        'SME Call Account',
        N'حساب توفير الشركات الصغيرة والمتوسطة'),
       (GETDATE(), GETDATE(),
        '1626',
        'Corporate Call',
        'Corporate Call Account',
        N'حساب توفير الشركات'),
       (GETDATE(), GETDATE(),
        '1651',
        'Commercial Call',
        'Commercial Call Account',
        N'حساب توفير الشركات'),
       (GETDATE(), GETDATE(),
        '1804',
        'Reg Overdraft',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1805',
        'Cap Overdraft',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1807',
        'VIP SME OD',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1831',
        'SME Overdraft',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1861',
        'Corp Overdraft',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1891',
        'NBFI Overdraft',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '1921',
        'Commercial-Over',
        'Overdraft Account',
        N'حساب جاري مدين'),
       (GETDATE(), GETDATE(),
        '2001',
        'Vostros',
        'Vostros',
        N'Vostros'),
       (GETDATE(), GETDATE(),
        '3135',
        'Ret-salary Loan',
        'Salary Loan Or (Salary in Advance)',
        N'حساب راتب مدفوع مقدما'),
       (GETDATE(), GETDATE(),
        '5001',
        'Nostros',
        'Nostros',
        N'Nostros'),
       (GETDATE(), GETDATE(),
        '6016',
        'R-Std gldSav-IB',
        'Gold Road Saving IB',
        N'حساب طريقك دهب مع فائدة'),
       (GETDATE(), GETDATE(),
        '6017',
        'R-St gldSav-NIB',
        'Gold Road Saving NIB',
        N'حساب طريقك دهب بدون فائدة'),
       (GETDATE(), GETDATE(),
        '6018',
        'Reg-Prm Sav-IB',
        'Premium Saving IB Account',
        N'حساب التوفير مع فائدة'),
       (GETDATE(), GETDATE(),
        '6019',
        'Reg-Prm Sav-NIB',
        'Premium Saving NIB Account',
        N'حساب التوفير بدون فائدة'),
       (GETDATE(), GETDATE(),
        '6021',
        'Reg -Gold Sav',
        'Gold Saving Account',
        N'حساب توفير الذهب'),
       (GETDATE(), GETDATE(),
        '6025',
        'Reg -XAG Saving',
        'Silver Saving',
        N'حساب توفير الفضة'),
       (GETDATE(), GETDATE(),
        '6051',
        'Cap S Gold Road',
        'Gold Road Saving IB Select',
        N'حساب طريقك دهب مع فائدة'),
       (GETDATE(), GETDATE(),
        '6052',
        'C S Gold Road N',
        'Gold Road Saving NIB Select',
        N'حساب طريقك دهب بدون فائدة'),
       (GETDATE(), GETDATE(),
        '6053',
        'Cap-Prm Sav-IB',
        'Premium Saving IB Account',
        N'حساب التوفير مع فائدة'),
       (GETDATE(), GETDATE(),
        '6054',
        'Cap-Prm Sav-NIB',
        'Premium Saving NIB Account',
        N'حساب التوفير بدون فائدة'),
       (GETDATE(), GETDATE(),
        '6056',
        'Cap S Gold Sav',
        'Gold Saving Account',
        N'حساب توفير الذهب'),
       (GETDATE(), GETDATE(),
        '6060',
        'Cap S -XAG Sav',
        'Silver Saving Account',
        N'حساب توفير الفضة'),
       (GETDATE(), GETDATE(),
        '6080',
        'Pvt S gldSav-IB',
        'Gold Road Saving Private Banking',
        N'حساب طريقك دهب'),
       (GETDATE(), GETDATE(),
        '6101',
        'Staff-Saving-IB',
        'Staff-Saving-Int Bearing',
        N'حساب توفير الموظفين مع فائدة'),
       (GETDATE(), GETDATE(),
        '6103',
        'Staff -Gold Sav',
        'Gold Saving Account',
        N'حساب توفير الذهب'),
       (GETDATE(), GETDATE(),
        '6107',
        'Staff -XAG Sav',
        'Silver Saving Account',
        N'حساب توفير الفضة'),
       (GETDATE(), GETDATE(),
        '6126',
        'UpReg StdSaving',
        'Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6127',
        'Std Saving-NIB',
        'Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6128',
        'UpRgPrem SavInt',
        'Premium Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6129',
        'Prem SavNIB',
        'Premium Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6131',
        'UpRg Gold Sav',
        'Gold Saving Account',
        N'حساب توفير الذهب'),
       (GETDATE(), GETDATE(),
        '6133',
        'Platinum Saving',
        'Platinum Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6135',
        'UpRg SilverSav',
        'Silver Saving Account',
        N'حساب توفير الفضة'),
       (GETDATE(), GETDATE(),
        '6675',
        'UpRg TimeDeposi',
        'Term Deposit',
        N'حساب وديعة لاجل'),
       (GETDATE(), GETDATE(),
        '6676',
        'UpRg Dep PRInt',
        'Term Deposit',
        N'حساب وديعة لاجل'),
       (GETDATE(), GETDATE(),
        '6677',
        'UpRg Gold Depos',
        'Gold Road Saving Account',
        N'حساب طريقك دهب'),
       (GETDATE(), GETDATE(),
        '1001',
        'Current Account',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1002',
        'Current',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1003',
        'Current-Payroll',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1004',
        'Current-Payroll',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '1015',
        'Current-SMEs',
        'Current Account',
        N'حساب جاري'),
       (GETDATE(), GETDATE(),
        '6001',
        'Savings Account',
        'Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6002',
        'Saving',
        'Saving Account',
        N'حساب توفير'),
       (GETDATE(), GETDATE(),
        '6003',
        'Premium Saving',
        'Saving Account',
        N'حساب توفير')
