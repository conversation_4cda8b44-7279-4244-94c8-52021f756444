create table ais_consent
(
    id                    bigint identity (1,1) not null primary key,
    create_date           datetime              not null,
    update_date           datetime              not null,
    consent_ref           varchar(255)          not null,
    auth_code             varchar(max),
    redirect_url          varchar(max),
    refresh_token         varchar(max),
    status                varchar(65)           not null,
    client_app_id         varchar(255)          not null,
    client_app_name       nvarchar(255),
    customer_ref          varchar(255),
    sso_customer_ref      varchar(255),
    expiry_date           datetime              not null,
    transaction_from_date datetime,
    transaction_to_date   datetime
);

create table ais_consent_account
(
    id             bigint identity (1,1) not null primary key,
    create_date    datetime,
    update_date    datetime,
    consent_id     varchar(255)          not null,
    account_ref    varchar(255),
    account_number varchar(255),
    currency       varchar(65),
    category       varchar(225)
);

create table ais_permission
(
    id             bigint identity (1,1) not null primary key,
    create_date    datetime              not null,
    update_date    datetime              not null,
    handle         varchar(max)          not null,
    description_en varchar(max),
    description_ar nvarchar(MAX),
    active         bit
);

create table ais_consent_permission
(
    consent_id    varchar(255) not null,
    permission_id bigint       not null,
    primary key (consent_id, permission_id)
);

create table ais_consent_account_type
(
    id             bigint identity (1,1) not null primary key,
    create_date    datetime              not null,
    update_date    datetime              not null,
    account_class  varchar(255),
    gb_code        varchar(255),
    description_en varchar(max),
    description_ar nvarchar(max)
);

create index ais_consent_consent_refx
    on ais_consent (consent_ref);

create index ais_consent_accounts_consent_id_refx
    on ais_consent_account (consent_id);


