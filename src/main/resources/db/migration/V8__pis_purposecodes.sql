create table pis_paymentpurpose_codes
(
    id             bigint identity (1,1) not null primary key,
	create_date    datetime              not null,
    update_date    datetime              not null,
    payment_category_code  varchar(255),
    description_payment_category varchar(max),
    payment_purpose_code        varchar(255),
    description_payment_purpose nvarchar(max)
);

INSERT INTO pis_paymentpurpose_codes (create_date,update_date,payment_category_code,description_payment_category,payment_purpose_code,description_payment_purpose)
VALUES
(GETDATE(), GETDATE(),'Personal','Personal','101','Invoice Payment and Purchase'),
(GETDATE(), GETDATE(),'Personal','Personal','102','Utility Bill Payment'),
(GETDATE(), GETDATE(),'Personal','Personal','103','Prepaid Cards Recharging'),
(GETDATE(), GETDATE(),'Personal','Personal','104','Standing Orders'),
(GETDATE(), GETDATE(),'Personal','Personal','105','Personal Donations'),
(GETDATE(), GETDATE(),'Personal','Personal','106','Family Assistance and Expenses'),
(GETDATE(), GETDATE(),'Personal','Personal','107','Individual Social Security Subscription'),
(GETDATE(), GETDATE(),'Personal','Personal','108','Associations Subscriptions'),
(GETDATE(), GETDATE(),'Personal','Personal','109','Saving and Funding Account '),
(GETDATE(), GETDATE(),'Personal','Personal','110','Heritance'),
(GETDATE(), GETDATE(),'Personal','Personal','111','End of Service indemnity'),
(GETDATE(), GETDATE(),'Personal','Personal','112','Trust '),
(GETDATE(), GETDATE(),'Personal','Personal','113','Court Transfer'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','201','Public Sector Employees Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','202','Laborers Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','203','Private Sector Staff Salaries '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','204','Jordanian Diplomatic Staff Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','205','Foreign Diplomatic Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','206','Overseas Incoming Salaries  '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','207','Civil / Military Retirement Salaries '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','208','Social Security Retirement Salaries'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','209','Establishment Social Security Subscription'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','210','Public Sector Employees Bonus'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','211','Laborers Bonus'),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','212','Private Sector Staff Bonus '),
(GETDATE(), GETDATE(),'Salaries and Wages','Salaries and Wages','213','Income Tax Refunds'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','301','Investment Revenues '),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','302','Brokrage Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','303','Insurance '),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','304','Subscriptions to International Nonmonetary Organizations'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','305','Local Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','306','External Investment'),
(GETDATE(), GETDATE(),'Investment Remittances','Investment Remittances','307','Tender Bond Guarantee'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','401','Air Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','402','Land Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','403','Sea Freight'),
(GETDATE(), GETDATE(),'Transportation and Tourism','Transportation and Tourism','404','Travel and Tourism'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','501','Governmental Delegation Transfers'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','502','Private Sector Delegation Transfers'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','503','Governmental Education'),
(GETDATE(), GETDATE(),'Training and Delegation','Training and Delegation','504','Private Sector Education'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','601','Public Sector Exportation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','602','Private Sector Exportation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','603','Public Sector Importation'),
(GETDATE(), GETDATE(),'Import and Export','Import and Export','604','Private Sector Importation'),
(GETDATE(), GETDATE(),'External Aid','External Aid','701','Religious Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','702','International Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','703','Arab Communities Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','704','UN Aid'),
(GETDATE(), GETDATE(),'External Aid','External Aid','705','Charity Communities Aid'),
(GETDATE(), GETDATE(),'Services','Services','801','Telecommunication Services'),
(GETDATE(), GETDATE(),'Services','Services','802','Financial Services'),
(GETDATE(), GETDATE(),'Services','Services','803','Information Technology Services'),
(GETDATE(), GETDATE(),'Services','Services','804','Consulting Services'),
(GETDATE(), GETDATE(),'Services','Services','805','Construction Services'),
(GETDATE(), GETDATE(),'Services','Services','806','Maintenance and Assembling Services'),
(GETDATE(), GETDATE(),'Services','Services','807','Marketing and Media Services'),
(GETDATE(), GETDATE(),'Services','Services','808','Mining Services'),
(GETDATE(), GETDATE(),'Services','Services','809','Medical and Health Services'),
(GETDATE(), GETDATE(),'Services','Services','810','Cultural, Educational and Entertainment  Services'),
(GETDATE(), GETDATE(),'Services','Services','811','Rental Expenses'),
(GETDATE(), GETDATE(),'Services','Services','812','Real Estate'),
(GETDATE(), GETDATE(),'Services','Services','813','Taxes'),
(GETDATE(), GETDATE(),'Services','Services','814','Fees'),
(GETDATE(), GETDATE(),'Services','Services','815','Commissions'),
(GETDATE(), GETDATE(),'Services','Services','816','Franchise and License Fees'),
(GETDATE(), GETDATE(),'Services','Services','817','Cheque Collection'),
(GETDATE(), GETDATE(),'Services','Services','818','Membership Fees'),
(GETDATE(), GETDATE(),'Funding','Funding','901','Municipality Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','902','Government Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','903','Private Sector Funds'),
(GETDATE(), GETDATE(),'Funding','Funding','904','External Incoming Funds'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1001','International Communities and Embassies Remittances'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1002','Permanent Diplomatic Missions'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1003','Temporary Diplomatic Missions'),
(GETDATE(), GETDATE(),'Diplomacy','Diplomacy','1004','Jordanian Embassies Income'),
(GETDATE(), GETDATE(),'Loans','Loans','1101','Long-Term Loans Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1102','Long-Term Loans interest Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1103','Short-Term Loans Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1104','Short-Term Loans interest Installments  / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1105','Long-Term Loans Installments / Private Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1106','Long-Term Loans Interest Installments / Public Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1107','Short-Term Loans Installments /Private  Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1108','Short-Term Loans Interest Installments / Private Sector'),
(GETDATE(), GETDATE(),'Loans','Loans','1109','Loans Installments Against Governmental Guarantee'),
(GETDATE(), GETDATE(),'Loans','Loans','1110','Loans Interest Installments  Against Governmental Guarantee'),
(GETDATE(), GETDATE(),'Loans','Loans','1111','Credit Card Payment '),
(GETDATE(), GETDATE(),'Loans','Loans','1112','Personal Loan Payment'),
(GETDATE(), GETDATE(),'General','General','1201','Rerouting'),
(GETDATE(), GETDATE(),'General','General','1202','Scientific Research Support')
