create table pis_consent
(
    id                         bigint identity (1,1) not null primary key,
    create_date                datetime              not null,
    update_date                datetime              not null,
    consent_ref                varchar(255)          not null,
    auth_code                  varchar(max),
    redirect_url               varchar(max),
    refresh_token              varchar(max),
    status                     varchar(65)           not null,
    client_app_id              varchar(255)          not null,
    client_app_name            nvarchar(255),
    customer_ref               varchar(255),
    sso_customer_ref           varchar(255),
    payment_ref                varchar(255),
    payment_type               varchar(65)           not null,
    instruction_identification varchar(255)          not null,
    endtoend_identification    varchar(255)          not null,
    local_instrument           varchar(65)           not null,
    amount                     numeric(6, 2)         not null,
    currency                   varchar(65)           not null,
    creditor_acc_scheme        varchar(65)           not null,
    creditor_acc_ref           varchar(255)          not null,
    debtor_acc_scheme          varchar(65),
    debtor_acc_ref             varchar(255),
    payment_purpose_code       varchar(255),
    payment_reason             varchar(max),
    creditor_name_en           varchar(255),
    creditor_name_ar           nvarchar(255),
    creditor_address_line1     varchar(255),
    creditor_address_line2     varchar(255),
    creditor_address_line3     varchar(255),
    creditor_city              varchar(255),
    creditor_state             varchar(255),
    creditor_postcode          varchar(65),
    creditor_country_code      varchar(65),
    creditor_country_name      varchar(255),
    charge_type                varchar(255)
);

create table caf_consent
(
    id                 bigint identity (1,1) not null primary key,
    create_date        datetime              not null,
    update_date        datetime              not null,
    consent_ref        varchar(255)          not null,
    auth_code          varchar(max),
    redirect_url       varchar(max),
    refresh_token      varchar(max),
    status             varchar(65)           not null,
    client_app_id      varchar(255)          not null,
    client_app_name    nvarchar(255),
    customer_ref       varchar(255),
    sso_customer_ref   varchar(255),
    expiry_date        datetime,
    debtor_acc_scheme  varchar(65),
    debtor_acc_ref     varchar(255),
);

create index pis_consent_consent_refx
    on pis_consent (consent_ref);

create index caf_consent_consent_refx
    on caf_consent (consent_ref);

