create table spring_session
(
    primary_id            char(36) not null primary key,
    SESSION_ID            char(36) not null unique,
    CREATION_TIME         bigint   not null,
    LAST_ACCESS_TIME      bigint   not null,
    MAX_INACTIVE_INTERVAL int      not null,
    EXPIRY_TIME           bigint   not null,
    PRINCIPAL_NAME        varchar(100),
    constraint SPRING_SESSION_IX1 unique (SESSION_ID)
);

create table spring_session_attributes
(
    session_primary_id char(36)     not null,
    ATTRIBUTE_NAME     varchar(200) not null,
    ATTRIBUTE_BYTES    varbinary( max) not null,
    constraint SPRING_SESSION_ATTRIBUTES_PK primary key (session_primary_id, ATTRIBUTE_NAME),
    constraint SPRING_SESSION_ATTRIBUTES_FK foreign key (session_primary_id) references SPRING_SESSION (primary_id) on delete cascade
);

create index spring_session_exp_time_idx ON spring_session (EXPIRY_TIME);
create index spring_session_id_idx ON spring_session (primary_id);
