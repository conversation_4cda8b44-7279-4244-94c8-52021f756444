language=en
user_name=Username
user_password=Password
login_button=Authorize me
otp_button=Verify OTP
generic_error.title=Error occured
generic_error.message=Sorry, unexpected error occured during consent flow. Please, start again or contact bank support.
error.login=Invalid login attempt. Please make sure to enter the correct username/password or to register in Capital Bank Mobile App by clicking \u2018Register' button
otp_error=Invalid OTP code. Please try again or request new OTP
lockout_error=Your account has been temporarily locked for 15 mins due to multiple invalid login attempts. Please, try again later.
eligibility.error=Eligibility error
eligibility.message=Your account is not eligible. Please, contact bank support.
session.error=Incorrect auth parameters. Please, start again or contact bank support.
session.timeout.title=Session Timeout
session.timeout.message=Your session is expired. Please, start again.
session.restart_button=Restart
application.title=Customer Consent Management Portal
application.subtitle=Enter your Capital Bank Mobile App credentials
application.otpSubtitle=Enter your One-time password
mobile.user.require=You need to be registered at Capital Bankâs mobile application in order to use this portal
mobile.user.register=Register in Capital Bank Mobile Application
arabic.locale=\u0639\u0631\u0628\u064A

otp.resend.timer=Resend code in: 
otp.resend.action=Resend OTP code

consent.greeting=Dear {0}!
consent.alert.terms=I have read and agreed to the 
consent.alert.terms_bold=terms & conditions
consent.not_found=No accounts have been found
consent.account.ownership=The account you submitted does not belong to your accounts, please verify the account number and try again.
consent.action.select.account=Please, select account
consent.action.consent=Consent
consent.action.reject=Reject
consent.action.back=Back

consent.ais.request=We received your consent request sent by {0}.
consent.ais.access=By consenting on this page, you authorize {0} to access the following:
consent.ais.duration=The consent is valid for 12 months.
consent.ais.action.select=Select all
consent.ais.action.deselect=Deselect all

consent.pis.request=We received your {0} payment consent request sent by {1}.
consent.pis.debit=The payment will be debited from your account {0} at Capital Bank.
consent.pis.payment.details=Payment details:
consent.pis.payment.details.error=Something went wrong technically. Please try again or contact support
consent.pis.payment.beneficiary=Beneficiary: {0}
consent.pis.payment.purpose=Purpose of Payment: {0}
consent.pis.payment.amount=Amount and currency: {0} {1}
consent.pis.payment.fee=Fee: {0} {1}
consent.pis.payment.fxrate=FX rate: {0}
consent.pis.payment.fxamount=FX amount: {0} {1}
consent.pis.payment.total_amount=Total amount: {0} {1}
consent.pis.access=By giving your consent on this page, you authorize {0} to proceed with the transaction.

consent.caf.request={0} has requested your consent to check a balance availability in your account {1} at Capital Bank.
consent.caf.duration=The consent is valid for 12 months.
consent.caf.approve=Please, approve before proceeding.

error.login.cliq_consent_required=Cliq consent required. To provide Cliq consent, please grant it via the Capital Bank Mobile App.