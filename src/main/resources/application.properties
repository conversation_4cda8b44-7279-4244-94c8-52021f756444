# This is a configuration file for environment independent properties.
# JDBC Configuration
#===============================
# JDBC driver class for MSSQL to connect to the database, e.g. com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
# Specifies that the session data should be stored in the database
spring.session.store-type=jdbc
# The table name in the database where the sessions will be stored
spring.session.jdbc.table-name=spring_session
# The type of datasource
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
# Connection timeout
spring.datasource.hikari.connection-timeout=20000
# Minimum number of inactive connections
spring.datasource.hikari.minimum-idle=5
# Maximum pool size
spring.datasource.hikari.maximum-pool-size=15
# Maximum connection idle timeout
spring.datasource.hikari.idle-timeout=30000
# Maximum connection lifetime
spring.datasource.hikari.max-lifetime=1200000
#===============================
# JPA/Hibernate Configuration
#===============================
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.use-new-id-generator-mappings=true
spring.jpa.show-sql=false
#===============================
# SSL certificate
#===============================
# File path of the key store that contains SSL certificate, e.g. classpath:certs/springboot.p12
server.ssl.key-store=classpath:certs/springboot.p12
# The type of the key store, e.g. pkcs12
server.ssl.key-store-type=pkcs12
# Alias that identifies the key in the key store, e.g. ciamservice
server.ssl.key-alias=ciamservice
# Enable ssl protocol
server.ssl.enabled=true
# The type of ssl protocol
server.ssl.protocol=TLS
server.ssl.ciphers=TLS_AES_128_GCM_SHA256,TLS_AES_256_GCM_SHA384,
server.ssl.enabled-protocols=TLSv1.2,TLSv1.3
#===============================
# Cache
#===============================
# Cache TTL (Time To Live) for accounts lifetime in minutes
ciam.cache.ttl.accounts=10
# Cache TTL (Time To Live) for keycloak client representation lifetime in minutes
ciam.cache.ttl.clients=10
# Cache TTL (Time To Live) for MuleSoft access token lifetime in hours
ciam.cache.ttl.mule-token=23
# Cache TTL (Time To Live) for keycloak admin access token lifetime in minutes
ciam.cache.ttl.admin-token=4
# Cache TTL (Time To Live) for external public key lifetime in minutes
ciam.cache.ttl.external-public-key=30
#===============================
# RedHat SSO
#===============================
# Keycloak Endpoint Settings
#-------------------------------
keycloak.uri.admin-users-uri=${keycloak.uri.base-uri}/admin/realms/${keycloak.uri.realm}/users
keycloak.uri.admin-clients-uri=${keycloak.uri.base-uri}/admin/realms/${keycloak.uri.realm}/clients
keycloak.uri.admin-sessions-uri=${keycloak.uri.base-uri}/admin/realms/${keycloak.uri.realm}/sessions
keycloak.uri.token-uri=${keycloak.uri.base-uri}/realms/${keycloak.uri.realm}/protocol/openid-connect/token
keycloak.uri.token-introspect=${keycloak.uri.base-uri}/realms/${keycloak.uri.realm}/protocol/openid-connect/token/introspect
keycloak.uri.auth-uri=${keycloak.uri.base-uri}/realms/${keycloak.uri.realm}/protocol/openid-connect/auth
keycloak.uri.revoke=${keycloak.uri.base-uri}/realms/${keycloak.uri.realm}/protocol/openid-connect/revoke
keycloak.uri.jwk-set-uri=${keycloak.uri.base-uri}/realms/${keycloak.uri.realm}/protocol/openid-connect/certs
#-------------------------------
# Custom attributes for Keycloak users
#-------------------------------
# Attribute name for user mobile number, e.g. mobile
keycloak.user.custom.attr.mobile=mobile
# Attribute name for user customer id, e.g. customerId
keycloak.user.custom.attr.customer=customerId
# Attribute name for user cliq consent, e.g. cliqConsent
keycloak.user.custom.attr.cliq.consent=cliqConsent
#-------------------------------
#Client id and administrator credentials for Keycloak
#-------------------------------
# Client id for admin user
keycloak.admin.client-id=admin-cli
#===============================
# MuleSoft
#===============================
# Endpoints
#-------------------------------
# MuleSoft token endpoint
keycloak.uri.mulesoft.token-uri=${keycloak.uri.base-uri}/realms/${keycloak.uri.mulesoft.realm}/protocol/openid-connect/token
# URL for OTP (One-Time Password) validation, e.g. ${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/validate
ciam.mulesoft.otp-validate-url=${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/validate
# URL for OTP (One-Time Password) request, e.g. ${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/request
ciam.mulesoft.otp-request-url=${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/request
# URL for account related operations, e.g. ${ciam.mulesoft.url}/customers-api/v1/customers
ciam.mulesoft.accounts-url=${ciam.mulesoft.url}/customers-api/v1/customers
# URL to get the account information by passing the accountID
ciam.mulesoft.accounts-info-url=${ciam.mulesoft.url}/accounts-api/v1/accounts
# URL for validating IBAN
ciam.mulesoft.iban-validation-url=${ciam.mulesoft.url}/swiftref-api/v1/ibans/{${ciam.mulesoft.iban-parameter}}/validity
# URL for getting BIC
ciam.mulesoft.bic-url=${ciam.mulesoft.url}/swiftref-api/v1/ibans/{${ciam.mulesoft.iban-parameter}}/bic
# URL for user eligibility operation, e.g. ${ciam.mulesoft.url}/customers-api/v1/ob/customers
ciam.mulesoft.eligibility-url=${ciam.mulesoft.url}/customers-api/v1/ob/customers
# URL for getting information about payment transaction operation, e.g. ${ciam.mulesoft.url}/pis/ciam/v1/transaction-info
ciam.mulesoft.transaction-info-url=${ciam.mulesoft.url}/pis/ciam/v1/transaction-info
# URL for getting cliq account by alias, e.g. ${ciam.mulesoft.url}/pis/ciam/v1/transaction-info
ciam.mulesoft.customer-accounts-url=${ciam.mulesoft.url}/jopacc-api/v1/cliq/customers/{${ciam.mulesoft.customer-id-parameter}}/aliases/{${ciam.mulesoft.alias-id-parameter}}/accounts
# URL for getting exchange rates
ciam.mulesoft.exchange-rate-url=${ciam.mulesoft.url}/utilities-api/v1/utilities/exchange-rate/calculate

#-------------------------------
# Parameter with user identifier for account related operations, e.g. id
ciam.mulesoft.id-parameter=id
# Parameter with iban for iban validation related operations, e.g. iban
ciam.mulesoft.iban-parameter=iban
# Parameter with customer ID for iban jopacc API related operations, e.g. iban
ciam.mulesoft.customer-id-parameter=custId
# Parameter with alias ID for jopacc API related operations, e.g. iban
ciam.mulesoft.alias-id-parameter=aliasId
# Parameter with alias type for jopacc API related operations, e.g. iban
ciam.mulesoft.alias-type-parameter=aliasType
# URL part for account related operations, e.g. accounts
ciam.mulesoft.accounts-part=accounts
# URL part for eligibility operation, e.g. eligibility
ciam.mulesoft.eligibility-part=eligibility
# Active account status, e.g. ACTIVE
ciam.mulesoft.account-status=ACTIVE
# Enable mocked versions of MuleSoft services except OTP service
ciam.mulesoft.mock=false
# Enable mocked versions of OTP MuleSoft service
ciam.mulesoft.otp.mock=true

# limit for internal transfer if currency is JOD
ciam.payment.limit-jod-transfer=25000
# limit for internal transfer if currency is not JOD
ciam.payment.limit-fxtransfer=10000
# Currencies accepted for internal transfer. If nothing is configured, all currencies will be accepted
ciam.payment.internal-currencies=JOD,USD,GBP,CHF,CAD,SAR,AED,AUD,EUR,EGP,BHD,KWD,QAR,OMR,IQD,JPY
# Currencies accepted for domestic transfer. If nothing is configured, all currencies will be accepted
ciam.payment.domestic-currencies=JOD,USD,GBP,EUR
# Creditor Account categories accepted for internal transfer. If nothing is configured, all categories will be accepted
ciam.payment.internal-creditor-account-type=1051,1101,1102,1103,1109,1126,1127,1128,1134,1145,1804,1805,6016,6017,6018,6019,6051,6052,6053,6054,6101,1326,1327,1328,1332,6126,6127,6128,6129,6080,1226,1227,1228,1176,1177,1178,1276,1277,1278,1022,1023,1024,1605,1626,1651,1831,1861,1921,1891
# Debtor Account categories accepted for internal transfer. If nothing is configured, all categories will be accepted
ciam.payment.internal-debtor-account-type=1051,1101,1102,1103,1109,1126,1127,1128,1134,1145,1804,1805,6016,6017,6018,6019,6051,6052,6053,6054,6101,1326,1327,1328,1332,6126,6127,6128,6129,6080,1226,1227,1228,1176,1177,1178,1276,1277,1278,1022,1023,1024,1605,1626,1651,1831,1861,1921,1891
#===============================
# API scopes
#===============================
# Admin
#-------------------------------
ciam.api.scope.admin=admin_api
#-------------------------------
# Mobile
#-------------------------------
ciam.api.scope.mobile=mobile_api
#-------------------------------
# Mulesoft
#-------------------------------
ciam.api.scope.mulesoft=mule_api
#-------------------------------
# Codebase
#-------------------------------
# URL for receiving current app version includes public key, e.g. ${ciam.codebase.url}/api/AppVersion/GetCurrentAppVersion
ciam.codebase.key_url=${ciam.codebase.url}/api/AppVersion/GetCurrentAppVersion
#===============================
# Other properties
#===============================
# Message Source
spring.messages.basename=message
# Resources path pattern
spring.mvc.static-path-pattern=/ob/web/**
# Enable main banner
spring.main.banner-mode=off
# Disable security auto configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
# Enable HTTP2 protocol
server.http2.enabled=true
server.servlet.cache-control=no-cache, no-store, must-revalidate
spring.mvc.async.request-timeout=40000
management.info.git.mode=none
server.max-http-header-size=8192
#-------------------------------
# Session
#-------------------------------
# Session timeout in seconds
server.servlet.session.timeout=600
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
# same as server.servlet.session.timeout
server.servlet.session.cookie.max-age=600
server.servlet.session.cookie.same-site=strict
#-------------------------------
# Logging
#-------------------------------
logging.level.org.springframework.security=INFO
logging.level.org.springframework.web=INFO
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=OFF
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
#-------------------------------
# Error
#-------------------------------
server.error.include-stacktrace=never
server.error.include-message=never
server.error.include-exception=false
server.error.whitelabel.enabled=false
#-------------------------------
# Cors
#-------------------------------
spring.web.cors.allow-credentials=true
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE
#-------------------------------
# Endpoints
#-------------------------------
management.endpoints.web.exposure.exclude=*
management.endpoint.health.show-details=never
management.endpoints.web.exposure.include=health,info
management.endpoint.health.probes.enabled=true
management.endpoint.health.livenessstate.enabled=true
management.endpoint.health.readinessstate.enabled=true
management.endpoints.web.cors.allowed-origins=*
management.endpoints.web.cors.allowed-methods=GET,POST
#-------------------------------
# Application
#-------------------------------
spring.application.name=MySecureApp
spring.application.version=1.0.0
#-------------------------------
# Tomcat
#-------------------------------
# depends on embedded server. Default is 20s
server.tomcat.connection-timeout=20s
server.tomcat.protocol-header=x-forwarded-proto
server.tomcat.remote-ip-header=x-forwarded-for
server.tomcat.use-trace=false
server.tomcat.redirect-context-root=true
server.tomcat.additional-tld-skip-patterns=*.jar
server.tomcat.server-header=YourCustomServerHeader
server.tomcat.max-http-form-post-size=2097152
server.tomcat.max-swallow-size=2097152
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=logs
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.suffix=.log
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b "%{Referer}i" "%{User-Agent}i" "%{x-forwarded-for}i"
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=20
#-------------------------------
# Audit & metrics
#-------------------------------
spring.security.audit.enabled=true
management.metrics.export.prometheus.enabled=true
management.metrics.export.prometheus.rsocket.enabled=false
#===============================







