<!DOCTYPE html>
<html lang="en-US" dir="ltr" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org" th:attr="style=(#{language} == ('ar') ? 'font-family: Alexandria' : 'font-family: SeravekBasic')"
      style="font-family: Alexandria">
<head>
    <title>Login</title>
    <link rel="icon" type="image/png" th:href="@{~/ob/web/images/favicon.png}">
    <link rel="stylesheet" type="text/css" th:href="@{~/ob/web/styles.css}">
    <meta http-equiv="Content-type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="encryption_enable" th:content="${encryptionEnabled}"/>
</head>
<header class="cboj__header">
    <nav class="cboj__nav">
        <form th:action="@{/ob/web/set_locale}" method="post" th:object="${data}">

            <ul class="cboj__nav">
                <li  th:if="#{language} != ('en')">
                    <button class="cboj__langswitcher cboj__langswitcher__en"  aria-live="off" type="submit" name="lang" value="en">
                        <span dir="ltr" th:text="#{arabic.locale}"></span>
                    </button>
                </li>
                <li th:if="#{language} == ('en')">
                    <button class="cboj__langswitcher cboj__langswitcher__ar" aria-live="off" type="submit" name="lang" value="ar">
                        <span dir="ltr" class="label bBody" th:text="#{arabic.locale}"></span>
                    </button>
                </li>
            </ul>

            <input type="hidden" th:field="*{consentRef}">
            <input type="hidden" th:field="*{response_type}">
            <input type="hidden" th:field="*{client_id}">
            <input type="hidden" th:field="*{redirect_uri}">
            <input type="hidden" th:field="*{scope}">
            <input type="hidden" th:field="*{state}">
        </form>
    
    
    </nav>
</header>
<body class="wrapper">
<div  class="cboj__section cboj__section__left">
    <img th:attr="src=(#{language} == ('ar') ? @{~/ob/web/images/capital_logo_ar.png} : @{~/ob/web/images/capital_logo.png})" class="cboj__logo" alt="CBoJ Developer Portal">
</div>
<div th:with="dir=(#{language} == ('ar') ? 'rtl' : 'ltr')" class="cboj__section cboj__section__right">
    <div class="cboj__content-wrapper">
        <div class="cboj__content-text">
            <p th:dir="${dir}" th:text="#{application.title}" class="cboj__content__title text-xl text-bold"></p>
            <p th:dir="${dir}" th:text="#{application.subtitle}" class="cboj__content__subtitle"></p>
        </div>
		
		<p th:dir="${dir}" th:text="#{${data.errorMessageKey}}"  th:if="${data.consentError != null}" class="cboj__form__error">
		</p>
        
        <form action="#" onsubmit="this.inert=true" class="cboj__form" th:if="${data.consentError == null}" th:action="@{/ob/web/login/otp}" th:name="lang" th:value="#{language}" th:object="${data}"
              method="POST">
            <input type="hidden" th:field="*{consentRef}">
            <input type="hidden" th:field="*{response_type}">
            <input type="hidden" th:field="*{client_id}">
            <input type="hidden" th:field="*{redirect_uri}">
            <input type="hidden" th:field="*{scope}">
            <input type="hidden" th:field="*{state}">
            <input type="hidden" th:field="*{userName}">
            <input type="hidden" th:field="*{userNameKey}">
            <input type="hidden" th:field="*{password}">
            <input type="hidden" th:field="*{passwordKey}">

            <p th:dir="${dir}" th:text="#{error}"  th:if="${data.error}" class="cboj__form__error">
            </p>
            
            <div class="cboj__form__wrapper">
                <div class="cboj__input__wrapper">
                    <input class="cboj__input cboj__input__username"
                        id="userNameInput"
                        th:dir="${dir}"
                        th:placeholder="#{user_name}"
                        type="text"
                        required="" />
                </div>
                
                <div class="cboj__input__wrapper">
                    <input class="cboj__input cboj__input__password"
                        id="passwordInput"
                        th:dir="${dir}"
                        th:placeholder="#{user_password}"
                        type="password"
                        required="" />
                </div>
                
                <div>
                    <button
                        type="button"
                        id="submitButton"
                        class="cboj__button cboj__button__submit">
                        <span th:dir="${dir}" th:text="#{login_button}"></span>
                    </button>
                </div>
            </div>
        </form>
        <div class="cboj__message-wrapper">
            <p th:dir="${dir}" th:text="#{mobile.user.require}"></p>
        </div>
        
        <div>
            <a href="https://www.capitalbank.jo/en/personal/get-the-app" class="cboj__link"
               th:text="#{mobile.user.register}"></a>
        </div>
    </div>
</div>
<script type="module" th:src="@{/ob/web/js/forge.min.js}"></script>
<script type="module" th:src="@{/ob/web/js/util.js}"></script>
<script type="module" th:src="@{/ob/web/js/login.js}"></script>
</body>
</html>
