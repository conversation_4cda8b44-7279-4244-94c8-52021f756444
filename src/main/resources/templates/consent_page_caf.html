<!DOCTYPE html>
<html lang="en-US" dir="ltr" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org" th:attr="style=(#{language} == ('ar') ? 'font-family: Alexandria' : 'font-family: SeravekBasic'),
      data-lang=(#{language} == ('ar') ? 'ar' : 'en')">
<head>
    <title>Consent</title>
    <link rel="icon" type="image/png" th:href="@{~/ob/web/images/favicon.png}">
    <link rel="stylesheet" type="text/css" th:href="@{~/ob/web/styles.css}">
    <meta http-equiv="Content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self';">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
</head>
<header id="header" class="cboj__header">
    <nav class="cboj__nav">
    </nav>
</header>
<body class="wrapper">

<div id="data-container"
     th:attr="data-select-all-placeholder=#{consent.ais.action.select},
              data-deselect-all-placeholder=#{consent.ais.action.deselect}">
</div>

<div class="cboj__section cboj__section__left">
    <img th:attr="src=(#{language} == ('ar') ? @{~/ob/web/images/capital_logo_ar.png} : @{~/ob/web/images/capital_logo.png})"
         class="cboj__logo" alt="CBoJ Developer Portal">
</div>
<div th:with="dir=(#{language} == ('ar') ? 'rtl' : 'ltr')" class="cboj__section cboj__section__right cboj__section__right__consent">
    <div class="cboj__consent-wrapper">
        <div class="cboj__consent">
            <p class="cboj__consent__title" th:dir="${dir}"
               th:text="${#messages.msg('consent.greeting', data.customerName)}">
            </p>
            <p th:dir="${dir}" th:text="${#messages.msg('consent.caf.request', data.clientName, data.accounts[0].listViewEn)}">
            </p>
        </div>

        <div class="cboj__permission-form">
            <form th:action="@{/ob/web/caf/update_consent}" th:object="${data}" id="consentForm" method="POST"
                  class="cboj__permission__form">
                <input type="hidden" th:field="*{consentRef}">

                <div th:if="${!#lists.isEmpty(data.accounts)}">
                    <p class="cboj__terms__title" th:if="${#lists.size(data.accounts) > 1}" th:dir="${dir}" th:text="${#messages.msg('consent.action.select.account')}">
                    </p>
                    <ul class="cboj__permission-form__actions-item"
                        th:attr="style=(#{language} == ('ar') ? 'flex-direction: row-reverse; margin-right: 28px;' : 'flex-direction: row')">
                    </ul>
                    <div th:if="${#lists.size(data.accounts) > 1}" class="cboj__permission-form__actions">
                        <ul th:each="account, stat : *{accounts}" class="cboj__permission-form__actions-item"
                            th:attr="style=(#{language} == ('ar') ? 'flex-direction: row-reverse; margin-right: 12px;' : 'flex-direction: row')">
                            <label class="cboj__permission-form__checkbox">
                                <input type="radio" th:text="${account.listViewEn}" th:value="${account.accountRef}" th:field="*{accounts}"/>
                                <label th:for="${#ids.prev('accounts')}"></label>
                            </label>
                        </ul>
                    </div>
                    <div th:if="${#lists.size(data.accounts) == 1}">
                        <input type="hidden" th:value="${data.accounts[0].accountRef}" id="accountRef" name="accountRef"/>
                    </div>
                    
                    <p th:dir="${dir}"
                        th:text="${#messages.msg('consent.caf.duration')}"
                        class="cboj__permission__title">
                    </p>
                    
                    <p th:dir="${dir}"
                        th:text="${#messages.msg('consent.caf.approve')}"
                        class="cboj__permission__title">
                    </p>

                    <div class="cboj__permission-form__buttons">
                        <button aria-live="off" type="submit"
                            id="cboj_consent"
                            class="cboj__button cboj__button__submit"
                            th:name="action"
                            th:value="${T( com.cboj.ciam.web.ConsentAction).CONSENT}">
                                <span th:dir="${dir}"
                                    th:text="#{consent.action.consent}"/>
                        </button>
                        <button aria-live="off" type="submit"
                            class="cboj__button cboj__button__reject"
                            th:name="action"
                            th:value="${T( com.cboj.ciam.web.ConsentAction).REJECT}"
                            aria-label="" style="float: right;">
                                <span th:dir="${dir}"
                                    th:text="#{consent.action.reject}"/>
                        </button>
                    </div>
                </div>
                <div th:if="${#lists.isEmpty(data.accounts)}">
                    <span th:dir="${dir}" th:text="#{consent.not_found}"></span>
                    <br>
                    <button aria-live="off" type="submit"
                        class="cboj__button cboj__button__reject"
                        th:name="action"
                        th:value="${T( com.cboj.ciam.web.ConsentAction).REJECT}">
                        <span th:dir="${dir}" th:text="#{consent.action.back}"/>
                    </button>
                </div>
            </form>
            <ul class="cboj__permission-form__actions-item">
                <label class="cboj__permission-form__checkbox cboj__permission-form__checkbox-all">
                    <input type="checkbox" id="confirm_terms"/>
                </label>
                <div class="cboj__terms-checkbox__container">
                    <label th:dir="${dir}" th:text="#{consent.alert.terms}"
                    ></label>
                    <button class="cboj__button-link cboj__consent__terms"
                            th:dir="${dir}"
                            th:text="#{consent.alert.terms_bold}">
                    </button>
                </div>
            </ul>
            
            <div th:replace="consent_common :: consent-dialog"></div>
        </div>
    </div>
</div>
</body>
</html>
<script type="text/javascript" th:src="@{~/ob/web/js/consent_caf.js}"></script>
