<!DOCTYPE html>
<html lang="en-US" dir="ltr" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org" th:attr="style=(#{language} == ('ar') ? 'font-family: Alexandria' : 'font-family: SeravekBasic'),
      data-lang=(#{language} == ('ar') ? 'ar' : 'en')">
<head>
    <title>Error</title>
    <link rel="icon" type="image/png" th:href="@{~/ob/web/images/favicon.png}">
    <link rel="stylesheet" type="text/css" th:href="@{~/ob/web/styles.css}">
    <meta http-equiv="Content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self';">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
</head>
<header id="header" class="cboj__header">
    <nav class="cboj__nav">
    </nav>
</header>
<body class="wrapper">

<div id="data-container"
     th:attr="data-select-all-placeholder=#{consent.ais.action.select},
              data-deselect-all-placeholder=#{consent.ais.action.deselect}">
</div>

<div class="cboj__section cboj__section__left">
    <img th:attr="src=(#{language} == ('ar') ? @{~/ob/web/images/capital_logo_ar.png} : @{~/ob/web/images/capital_logo.png})"
         class="cboj__logo" alt="CBoJ Developer Portal">
</div>
<div th:with="dir=(#{language} == ('ar') ? 'rtl' : 'ltr')" class="cboj__section cboj__section__right cboj__section__right__consent">
    <div class="cboj__consent-wrapper">
        <div class="cboj__consent">
            <p class="cboj__consent__title" th:dir="${dir}"
               th:text="${#messages.msg('generic_error.title')}">
            </p>
            <div th:if="${accountOwnershipEx}">
                <p th:dir="${dir}" th:text="${#messages.msg('consent.account.ownership')}">
                </p>
            </div>
            <div th:if="${!accountOwnershipEx}">
                <p th:dir="${dir}" th:text="${#messages.msg('generic_error.message')}">
                </p>
            </div>

        </div>
    </div>
</div>
</body>
</html>
