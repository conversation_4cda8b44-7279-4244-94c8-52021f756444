<!DOCTYPE html>
<html lang="en-US" dir="ltr" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org" th:attr="style=(#{language} == ('ar') ? 'font-family: Alexandria' : 'font-family: SeravekBasic'),
data-lang=(#{language} == ('ar') ? 'ar' : 'en')"
>
<head>
    <title>OTP</title>
    <link rel="icon" type="image/png" th:href="@{~/ob/web/images/favicon.png}">
    <link rel="stylesheet" type="text/css" th:href="@{~/ob/web/styles.css}">
    <meta http-equiv="Content-type" content="text/html; charset=UTF-8">
	<meta http-equiv="Content-Security-Policy" content="script-src 'self';">
    <meta name="viewport" content="width=device-width, initial-scale=1">

	<meta name="_csrf" th:content="${_csrf.token}"/>
	<meta name="_csrf_header" th:content="${_csrf.headerName}"/>
</head>
<header class="cboj__header">

</header>
<body class="wrapper">
<div  class="cboj__section cboj__section__left">
    <img th:attr="src=(#{language} == ('ar') ? @{~/ob/web/images/capital_logo_ar.png} : @{~/ob/web/images/capital_logo.png})" class="cboj__logo" alt="CBoJ Developer Portal">
</div>
<div id="data-container"
	 th:attr="data-otp-resend-placeholder=#{otp.resend.timer},
                  data-otp-resend-action=#{otp.resend.action},
                  data-session-timeout-title=#{session.timeout.title},
                  data-session-timeout-message=#{session.timeout.message},
                  data-session-restart-button=#{session.restart_button}">

</div>
<div th:with="dir=(#{language} == ('ar') ? 'rtl' : 'ltr')" class="cboj__section cboj__section__right">
    <div class="cboj__content-wrapper cboj__otp__wrapper">
        <div class="cboj__content-text">
            <p th:dir="${dir}" th:text="#{application.title}" class="cboj__content__title text-xl text-bold"></p>
            <p th:dir="${dir}" th:text="#{application.otpSubtitle}" class="cboj__content__subtitle"></p>
        </div>

        <p id="validationOtpError" th:dir="${dir}" th:text="#{otp_error}"  th:if="${data.validationOtpError}" class="cboj__form__error">
        </p>
        <p th:dir="${dir}" th:text="#{lockout_error}"  th:if="${data.renewalOtpError}" class="cboj__form__error">
        </p>

        <form action="#" id="otpForm" class="cboj__form" th:action="@{/ob/web/login/submit_otp}" th:name="lang" th:value="#{language}" th:object="${data}"
              method="POST">
            <input type="hidden" th:field="*{consentRef}">
			<input type="hidden" th:field="*{otp}"/>
			<input type="hidden" th:field="*{otpRenewTime}"/>

			<div id="hiddenData" th:data-object="${data}"></div>
            </p>

            <div class="cboj__form__wrapper">
				<div id="digits" class="cboj__otp__container">
					<input type="password" class="cboj__otp__input" name="digit1" id="digit1" pattern="[0-9]" maxlength="1" required>
					<input type="password" class="cboj__otp__input" name="digit2" id="digit2" pattern="[0-9]" maxlength="1" required>
					<input type="password" class="cboj__otp__input" name="digit3" id="digit3" pattern="[0-9]" maxlength="1" required>
					<input type="password" class="cboj__otp__input" name="digit4" id="digit4" pattern="[0-9]" maxlength="1" required>
					<input type="password" class="cboj__otp__input" name="digit5" id="digit5" pattern="[0-9]" maxlength="1" required>
					<input type="password" class="cboj__otp__input" name="digit6" id="digit6" pattern="[0-9]" maxlength="1" required>
				</div>
            </div>
			<div class="cboj__otp__container">
				<a id="timer" class="cboj__otp__timer text-bold"></a><span id="timeLeft" class="cboj__otp__time"></span>
				<button
						type="submit"
						disabled
						class="cboj__button cboj__button__submit"
						id="submitOTP">
					<span th:dir="${dir}" th:text="#{otp_button}"></span>
				</button>
			</div>
        </form>

		<div id="timeout" class="cboj__form__error cboj__hidden">
			<h2 th:dir="${dir}" th:text="#{session.timeout.title}"></h2>
			<p th:dir="${dir}" th:text="#{session.timeout.message}"></p>
		</div>

		<div id="eligibility" class="cboj__form__error cboj__hidden">
			<h2 th:dir="${dir}" th:text="#{eligibility.error}"></h2>
			<p th:dir="${dir}" th:text="#{eligibility.message}"></p>
		</div>

        <!--div>
            <a href="https://www.capitalbank.jo/en/personal/get-the-app" class="cboj__link"
               th:text="#{mobile.user.register}"></a>
        </div-->
    </div>
</div>
<script type="text/javascript" th:src="@{/ob/web/js/otp.js}"></script>
</body>
</html>
