:root {
    --cl-primary-black: #243746;
    --cl-primary-red: #DB0032;
    --cl-primary-blue: #0093C9;
    --cl-primary-grey: #E5E7E9;
    --cl-primary-dark-grey: #696969;
    --cl-primary-white: #FFFFFF;
}

@font-face {
    font-family: 'Alexandria';
    src: url('../web/css/Alexandria/static/Alexandria-Regular.ttf');
}

@font-face {
    font-family: 'SeravekBasic';
    src: url('../web/css/SeravekBasicRegular/SeravekAppBasic-Regular.ttf');
}

* {
    margin: 0;
    padding: 0;
    background-color: transparent;
    font-size: inherit;
    box-sizing: border-box;
    border: 0;
    outline: 0;
}

*:not(input) {
    font-family: inherit;
    text-align: center;
}

html {
    font-size: 16px;
    line-height: 1.8;
    color: var(--cl-primary-black);
}

li {
    list-style-type: none
}

a {
    text-decoration: none;
    color: inherit;
    text-transform: none;
}

p:dir(ltr),
label:dir(ltr){
    text-align: left
}
p:dir(rtl),
label:dir(rtl){
    text-align: right
}

span:dir(ltr){
    font-family: 'SeravekBasic'
}
span:dir(rtl){
    font-family: 'Alexandria'
}

.text-xl {
    font-size: 32px;
    line-height: 48px;
}

.text-bold {
    font-weight: 700;
}

.cboj__header {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    display: flex;
    padding: 12px 20px;
    height: 50px;
    justify-content: flex-end;
}

.cboj__nav {
    display: flex;
}

.cboj__langswitcher {
    cursor: pointer;
    font-weight: 100;
    color: var(--cl-primary-black);
    text-decoration: none;
    border: none;
    outline: none;
    text-align: center;
    transition: color 0.2s;
}

.cboj__langswitcher:hover {
    color: var(--cl-primary-blue);
}

.cboj__hidden{
	display: none !important;
}

.cboj__not_visible{
    visibility: hidden;
}

.wrapper {
    display: flex;
    height: 100dvh;
    align-items: stretch;
}

.cboj__section {
    padding: 0 20px;
}

.cboj__section__right {
    flex: 1 0 40%;
    min-width: 387px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--cl-primary-grey);
}

.cboj__section__left {
    flex: 1 0 60%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cboj__section__right__consent {
    align-items: start;
    padding: 50px;
    overflow-y: auto;
}

.cboj__logo {
    display: block;
    width: 387px;
    aspect-ratio: auto;
}

.cboj__content-wrapper {
    max-width: 387px;
    width: 100%;
}

.cboj__content-text {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.cboj__content__title {
    text-align: center !important;
    margin-bottom: 8px;
}

.cboj__content__subtitle{
    text-align: center !important;
}

.cboj__form {
    margin-bottom: 16px;
}

.cboj__form__error {
    text-align: center !important;
    margin-bottom: 20px;
    color: var(--cl-primary-red);
}

.cboj__form__wrapper {
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.cboj__input__wrapper {
    margin-bottom: 10px;
    padding: 12px;
    background-color: #ffffff;
    border: 1px solid #ccc;
    border-radius: 2px;
}

.cboj__input__wrapper input {
    width: 100%;
    border: 0 solid transparent;
    -webkit-appearance: none;
    font-size: 16px;
    padding: 4px 10px 2px;
    line-height: normal;
    min-height: unset;
    color: var(--cl-primary-dark-grey);
}

.cboj__button {
    width: 100%;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    border: 1px solid transparent;
    padding: 10px 12px;
    font-weight: 700;
    border-radius: 30px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
    transition: color 0.2s;
}

.cboj__button__submit, .cboj__button__reject {
    background-color: var(--cl-primary-blue);
    color: var(--cl-primary-white);
}
.cboj__button__submit:disabled{
    cursor: none;
    pointer-events: none;
    user-select: none;
    background-color: var(--cl-primary-black);
    opacity: 0.4;
}

.cboj__button__submit__disabled {
    cursor: none;
    pointer-events: none;
    user-select: none;
    background-color: var(--cl-primary-black);
    opacity: 0.4;
}

.cboj__button__submit:hover {
    background-color: var(--cl-primary-black);
}

.cboj__button__reject:hover {
    background-color: var(--cl-primary-red);
 }

.cboj__message-wrapper {
    margin-bottom: 8px;
}

.cboj__link {
    color: var(--cl-primary-blue);
}



@media (max-width: 768px) {
    .cboj__section__left {
        display: none;
    }
}


/* Consent page */
.cboj__consent-wrapper {
    max-width: 450px;
    width: 100%;
}

.cboj__consent, .cboj__consent__title {
    margin-bottom: 16px;
}

.cboj__terms-checkbox__container {
    gap: 3px;
    align-items: center;
}

.cboj__consent__terms {
    font-weight: 700;
}

.cboj__customer__label:dir(ltr){
    text-align: right
}
.cboj__customer__label:dir(rtl){
    text-align: left
}

.cboj__permissions {
    margin-bottom: 32px;
}

.cboj__permission__title {
    margin-bottom: 12px;
}

.cboj__permission__list {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
}

.cboj__permission__item {
    position: relative;
}

.cboj__permission__item__en {
    padding-left: 15px;
    text-align: left;
}

.cboj__permission__item__ar {
    padding-right: 15px;
    text-align: right;
}

.cboj__permission__item__en::before {
    content: '';
    display: block;
    width: 5px;
    height: 5px;
    transform: translate(-15px, 16px);
    border-radius: 100%;
    background-color: var(--cl-primary-black);
}

.cboj__permission__item__ar::before {
    content: '';
    display: block;
    width: 5px;
    height: 5px;
    position: absolute;
    top: 15px;
    right: 0;
    border-radius: 100%;
    background-color: var(--cl-primary-black);
}

.cboj__permission__form {
    margin-bottom: 16px;
}

.cboj__permission-form__actions {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-gutter: stable;
    text-align: left;
    margin-bottom: 32px;
}

.cboj__permission-form__actions-item {
    display: flex;
    gap: 12px;
}

.cboj__permission-form__buttons {
    display: flex;
    gap: 12px;
}

.cboj__permission-form__checkbox-all + label {
    font-weight: 700;
}

.cboj__permission-form__buttons.cboj__permission-form__button__ar {
    flex-direction: row-reverse;
}

.cboj__button-link {
    color: var(--cl-primary-blue);
    line-height: 16px;
    cursor: pointer;
}

.cboj__dialog__close {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cboj__dialog {
    max-height: calc(100dvh - 40px);
    min-height: 200px;
    max-width: 900px;
    width: calc(100% - 40px);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 8px;
    background-color: var(--cl-primary-white);
    padding: 24px;
}

.cboj__dialog__close {
    position: absolute;
    top: 8px;
    right: 8px;
    color: var(--cl-primary-grey);
}

.cboj__terms__en, .cboj__terms__en * {
    text-align: left;
}

.cboj__terms__ar, .cboj__terms__ar * {
    text-align: right;
}

.cboj__terms__title {
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.cboj__terms__paragraph, .cboj__terms__ul {
    margin-bottom: 12px;
}

.cboj__terms__paragraph-a, .cboj__terms__paragraph-b {
    padding-left: 12px;
}
.cboj__terms__paragraph-a::before {
    content: '(a)';
}

.cboj__terms__paragraph-b::before {
    content: '(b)';
}

.cboj__terms__ul {
    padding-left: 28px;
}

/* OTP page */
.cboj__otp__wrapper{
	background-color: white;
	border: 1px;
	border-color: grey;
	padding: 20px;
}
.cboj__otp__container {
    display: flex;
    justify-content: center;
    align-items: center;
	margin-top: 20px;
}
.cboj__otp__title{
	text-align: left;
}
#digits .cboj__otp__input {
    width: 40px;
    margin: 0 10px;
    text-align: center;
    font-size: 36px;
    border-bottom: 3px solid grey;
}
 
#digits .cboj__otp__input:focus {
    border-bottom: 3px solid black;
    outline: none;
	transition: border 500ms ease-out;
}
#digits input:not(disabled){
	background: transparent;
}
#digits input:disabled{
	background: lightgrey;
    cursor: auto;
}
input[type=radio]{
    margin-right: 5px;
}
.cboj__otp__timer{
	width: 100%;
	font-size: 1rem;
	color: rgb(0, 147, 201);
	text-align: left;
}
.cboj__otp__time{
	display: flex;
    margin-right: 20px;
}
.cboj__session__timeout{
	text-align: left;
    margin-bottom: 20px;
    color: var(--cl-primary-red);
}
.cboj__session__timeout p{
    font-style: bold;
}

.cboj__loader {
    width: 80px;
    height: 80px;
    border: 8px solid #f3f3f3;
    border-top: 8px solid rgb(0, 147, 201);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}
  
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cboj__permission-form_payment_details:dif(rtl), 
.cboj__permission-form__buttons:dif(rtl),
.cboj__permission-form__actions-item:dir(rtl),
.cboj__terms-checkbox__container:dir(rtl){
    flex-direction: row-reverse
}
.cboj__permission-form_payment_details:dif(ltr), 
.cboj__permission-form__buttons:dif(ltr),
.cboj__permission-form__actions-item:dir(ltr),
.cboj__terms-checkbox__container:dir(ltr){
    flex-direction: row
}

#cboj__payment_details_container{
    height: auto;
    min-height: 250px;
}

#cboj__payment_details_container_loader{
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    min-height: 250px;
    position: relative;
}
