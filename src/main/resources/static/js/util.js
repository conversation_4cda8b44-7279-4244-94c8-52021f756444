let userEncKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu5zNB9sZTQykBgDEzpFm/zUkecQZ+CPybe6mKStS5pXdhRxS2tA9DF7V4WkXmVJkwxRUhXmGksnA6Sm9cEdA3VfRxaYBLhVklKy6Un8k8lHtXHqG7ZRQKPuo030c6P+Oud8qNXc2XefjKbkOTxfqOTqo+J0uYeFvGD7AO/kMDGFJfbzUPfT13aYW4iSgITu/Brm5pcv07yQozdyvLy1SZRrQBMqcTE0P9Tm5bWZxk2ecT2oM4yqDfcvdgOvmuQv1EplDAWB6IJxcEZ960DJCnFEhtbmJjM+Aqy8UQX17/3ejLwNBzrNy92lsH9mf1oXzNtPIvnCYUZdU0Ap27CszxwIDAQAB';
let passEncKey = 'MIICCgKCAgEAzhyueSVNaIUxxct7ukSrsIyP607imDkiD4yCFmEhj3eUZR5/R0aWpoc7ggSKbhx/mxKjsPdr8MOAft5x04V09eSKWHO4/t0TmN9y2FYG5OOGgu22DuXxVNj2CgR7Gdr+cYQKt9EvKZUsZPX3qpGqLrOOJUikz4D7nTi2pOJXsLt+HrFjz8xt4HHzzET3izRg2LQBzzFPucYafseOt5TqrGKsUF1KQZ2l3wUMRgSTzJMDVqGQCF01JNRQiksmqAradrHQPiyzz1HfzYNwe7QiZ4SjhUnPmpSu9R9Lpf03tQhsEvuU7rLAz3veEwDCLavYO7yRwYjjFPzFp5Xa2TyZYPgsGe45Hmp5NWU3XU34Uemi/0peIq72ed20s8GScGRrmG2szB0FZ7h4gBGvaSJCQPUdh5ObPHFVWxs6A+GYag8Nk6bxOeNVQ+p1R1FQxjyBy4OJSzZ6gAoD2gsOaREepnrpUjHHqy4qYP6y25RvlDNK9WUmgOGaCrib/U96mIt41RhXsaCqSrT280OPRBSSnF1ycrjY1hN8iYhRT4DV31IvCU26MljgwfqR/056j5VUpWWyw0cL+LwyJn0YXPhRDeyy081ulLAEcknZgSVl3qcQSdlNCvM8m+OQAMlvU+6oM6ktQ0F+X/cVLR2vNQ6+ACrjk89MdtRQecRpKSWMYsECAwEAAQ==';
let iv = new Uint8Array(16);

export async function encryptRequest(request, userKey, passKey) {
    if (userKey)
        userEncKey = userKey;
    if (passKey)
        passEncKey = passKey;
    // Generate AES key
    const userNamekey = await generateAESGCMKey();
    const passwordkey = await generateId(32);
    const passwordAESKey = await importKey(passwordkey);

    // Encrypt the request body
    const encryptedUsername = await encryptAESGCM(userNamekey, request.username);
    let usernameBuffer = new Int8Array(encryptedUsername);
    const encryptedPassword = await encryptAESGCM(passwordAESKey, request.password);

    const usernameKeyBase64 = await encode(await exportAESKey(userNamekey));
    const passwordKeyBase64 = await encode(await exportAESKey(passwordAESKey));

    const encryptedUsernameKey = await encryptUsernameKey(usernameKeyBase64, userEncKey);
    const encryptedPasswordKey = await encryptPasswordKey(passwordkey, passEncKey);

    return {
        username: await encode(usernameBuffer),
        usernameKey: encryptedUsernameKey,
        password: await bytesToBase64(encryptedPassword),
        passwordKey: encryptedPasswordKey
    };
}

//GCM
async function encode(ab) {
    return await _arrayBufferToBase64(ab);
}

async function _arrayBufferToBase64(buffer) {
    var binary = '';
    var bytes = new Uint8Array(buffer);
    var len = bytes.byteLength;
    for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
}

async function bytesToBase64(bytes) {
    if (!bytes)
        return null;

    bytes = new Uint8Array(bytes);
    const ctArray = Array.from(bytes)
    const ctStr = ctArray.map((byte) => String.fromCharCode(byte)).join('')
    return btoa(ctStr)
}

async function generateId(len) {
    var arr = new Uint8Array((len || 40) / 2);
    window.crypto.getRandomValues(arr);
    return Array.from(arr, dec2hex).join("");
}

function dec2hex(v) {
    return ('0' + v.toString(16)).slice(-2);
}

function importKey(key) {
    key = new TextEncoder().encode(key);
    return crypto.subtle.importKey('raw', key, {
        "name": "AES-GCM"
    }, true, ['encrypt', 'decrypt']);
}

async function generateAESGCMKey() {
    try {
        return await crypto.subtle.generateKey(
            {
                name: "AES-GCM",
                length: 256, // Key length: 128, 192, or 256 bits
            },
            true,   // Extractable: whether the key can be extracted
            ["encrypt", "decrypt"] // Key usages: what the key can be used for
        );
    } catch (error) {
        console.error("Error generating AES key", error);
    }
}

async function exportAESKey(key) {
    try {
        // Export the key in raw format
        return await crypto.subtle.exportKey("raw", key);
    } catch (error) {
        console.error("Error exporting AES key", error);
    }
}

async function encryptAESGCM(key, data) {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(data);
    return await crypto.subtle.encrypt({
        name: "aes-gcm",
        iv: iv,
        tagLength: 128 //can be 32, 64, 96, 104, 112, 120 or 128 (default)
    }, key, encodedData)
}

//RSA
async function encryptUsernameKey(key, publicKeyBase64) {
    const publicKeyDer = await forge.util.decode64(publicKeyBase64);
    const publicKey = await forge.pki.publicKeyFromAsn1(forge.asn1.fromDer(publicKeyDer));
    const encryptedAesKey = await publicKey.encrypt(key, 'RSA-OAEP', {
        md: forge.md.sha256.create()
    });
    return await forge.util.encode64(encryptedAesKey);
}

async function encryptPasswordKey(key, publicKey) {
    publicKey = '-----BEGIN PUBLIC KEY-----' + publicKey + '-----END PUBLIC KEY-----'
    var publicKeyObject = await forge.pki.publicKeyFromPem(publicKey);
    var encrypted = await publicKeyObject.encrypt(key, "RSA-OAEP");
    return await forge.util.encode64(encrypted);
}

