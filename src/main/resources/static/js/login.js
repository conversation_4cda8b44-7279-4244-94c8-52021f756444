import * as encryption from "./util.js";

let d = document, [formSubmit, loginForm,
    userNameInput, userNameForm<PERSON>ield, userName<PERSON>eyFormField,
    passwordInput, passwordFormField, passwordKeyFormField] = [
    d.getElementById("submitButton"),
    d.querySelector(".cboj__form"),
    d.getElementById("userNameInput"),
    d.getElementById("userName"),
    d.getElement<PERSON>y<PERSON>d("userNameKey"),
    d.getElementById("passwordInput"),
    d.getElementById("password"),
    d.getElementById("passwordKey"),
];
let self = this;

const encryptionEnabled = document.querySelector('meta[name="encryption_enable"]').content;

console.log(encryptionEnabled)

document.addEventListener('DOMContentLoaded', () => {
    const formSubmit = document.getElementById('submitButton');

    if (encryptionEnabled !== "true") {
        formSubmit.addEventListener('click', submitForm);
    } else {
        //Add event listener to submit button
        formSubmit.addEventListener('click', submitEncryptedForm);

        // Check local storage for userKey and passKey
        const userKey = localStorage.getItem('userKey');
        const passKey = localStorage.getItem('passKey');

        // If either key doesn't exist, fetch them from the server
        if (!userKey || !passKey) {
            fetch('/ob/web/public_keys')
                .then(response => response.json())
                .then(data => {
                    if (data.content) {
                        const {userKey, passKey} = data.content;
                        // Set userKey and passKey in local storage
                        localStorage.setItem('userKey', userKey);
                        localStorage.setItem('passKey', passKey);
                    } else {
                        console.error('Invalid response format:', data);
                    }
                })
                .catch(error => {
                    console.error('Error fetching keys:', error);
                });
        } else {
            console.log('Keys already exist in localStorage');
        }
    }
});

function submitEncryptedForm() {
    formSubmit.removeEventListener('click', submitEncryptedForm);
    const userName = userNameInput.value;
    const password = passwordInput.value;
    const userKey = localStorage.getItem('userKey');
    const passKey = localStorage.getItem('passKey');
    encryption.encryptRequest({"username": userName, "password": password}, userKey, passKey).then((result) => {
        userNameFormField.value = result.username;
        userNameKeyFormField.value = result.usernameKey;
        passwordFormField.value = result.password;
        passwordKeyFormField.value = result.passwordKey;
        loginForm.submit();
    });
}

function submitForm() {
    userNameFormField.value = userNameInput.value;
    passwordFormField.value = passwordInput.value;
    loginForm.submit();
}
