document.addEventListener('DOMContentLoaded', function() {
    var dataValue = document.getElementById("hiddenData").getAttribute("data-object");
    var dataObject = JSON.parse(dataValue);

    var dataElement = document.getElementById('data-container');
    var otpResendPlaceholder = dataElement.getAttribute('data-otp-resend-placeholder');
    var otpResendAction = dataElement.getAttribute('data-otp-resend-action');

    var otpExpirationTime = dataObject.otpExpirationTime;
    var otpRenewTime = dataObject.otpRenewTime;
    var renewalOtpError = dataObject.renewalOtpError;
    var submitOtpError = dataObject.submitOtpError;
    var eligibleError = dataObject.eligibleError;
    var otpRequestAttemptsLeft = dataObject.otpRequestAttemptsLeft;

    let otpNamespace = function () {
        // Set current lang
        const currentLang = document.documentElement.getAttribute('data-lang');
        let timerId;
        let d = document, [digitsContainer, digits, timer, timeLeft, submit, otp, consentRef, otpForm, sessionTimeout, eligibility, otpError] = [
            d.getElementById("digits"),
            d.querySelectorAll('[type="password"]'),
            d.getElementById('timer'),
            document.getElementById('timeLeft'),
            d.querySelector('#submitOTP'),
            d.getElementById("otp"),
            d.getElementById("consentRef"),
            d.getElementById("otpForm"),
            d.getElementById('timeout'),
            d.getElementById('eligibility'),
            d.getElementById('validationOtpError')];

        //Check errors and init session and OTP on start
        if (!!eligibleError) {
            blockOtp(eligibility);
            return;
        }
        //Disable submit button onload
        submit.disabled = true;
        initSession();
    initOtpTimer();
    otpForm.addEventListener('submit', () => {
        const otpCsrfInput = otpForm.querySelector('input[name="_csrf"]');
        const actualCsrf = document.querySelector('meta[name="_csrf"]').content;
        if (otpCsrfInput.value !== actualCsrf) {
            otpCsrfInput.value = actualCsrf;
        }
        this.inert = true
    });

        //Init session to 5 minute an blockOTP after that
        function initSession() {
            const timestamp = Date.now();
            setTimeout(function () {
                clearInterval(this.timerId);
                blockOtp(sessionTimeout);
            }, (new Date(otpExpirationTime) - new Date(timestamp)));
        }

        //Init OTP timer to 2 minutes and allow to resend OTP after that
        function initOtpTimer() {
            timer.innerHTML = otpResendPlaceholder;
            timer.classList.remove('cboj__button-link');
            const timestamp = Date.now();
            for (i = 0; i < digits.length; i++) {
                digits[i].disabled = false;
                digits[i].value = '';
            }
            if (timestamp >= otpRenewTime) {
                disableOtp();
                return;
            }
            if (submitOtpError) {
                disableOtp();
                return;
            }
            if (renewalOtpError) {
                blockOtp();
                return;
            }
            timer.removeEventListener("click", resendOtp);
            if (otpRequestAttemptsLeft == 0) {
                timeLeft.innerHTML = '';
                timeLeft.classList.add('cboj__hidden');
                timer.classList.add('cboj__hidden');
                clearInterval(this.timerId);
            } else {
                this.timerId = setInterval(function () {
                    const timestamp = Date.now();
                    if (timestamp >= otpRenewTime) {
                        clearInterval(this.timerId);
                        disableOtp();
                    } else {
                        const diff = Math.ceil((new Date(otpRenewTime) - new Date(timestamp)) / 1000);
                        timeLeft.innerHTML = Math.floor(diff / 60) + ':' + (diff % 60 > 9 ? diff % 60 : '0' + diff % 60);
                    }
                }, 1000);
            }
        }

        //Resend OTP code to user and unlock OTP again
        function resendOtp() {
            timer.removeEventListener("click", resendOtp);
            let csrfToken = document.querySelector('meta[name="_csrf"]').content;
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]').content;

            if (otpRequestAttemptsLeft > 0) {
                fetch(`/ob/web/login/request_otp?consentRef=${consentRef.value}`, {
                    method: "POST",
                    headers: {
                        [csrfHeader]: csrfToken,
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            renewalOtpError = response.status !== 200;
                            return; // Exit early if the response is not OK
                        }
                        const newCsrfToken = response.headers.get(csrfHeader);
                        if (newCsrfToken) {
                            csrfToken = newCsrfToken;
                            document.querySelector('meta[name="_csrf"]').content = newCsrfToken;
                        }
                        return response.json(); // Parse the response as JSON
                    })
                    .then(data => {
                        if (!data) return; // Exit early if there is no data

                        otpRenewTime = data.otpRenewTime;
                        otpRequestAttemptsLeft = data.otpRequestAttemptsLeft;
                        submitOtpError = data.submitOtpError;

                        if (otpError) {
                            otpError.classList.add('cboj__hidden');
                        }

                        initOtpTimer();
                    })
                    .catch(error => {
                        console.error('Error fetching OTP:', error);
                        // Handle the error appropriately here (e.g., set an error flag, show a message, etc.)
                    });
            }
        }

        //Disable OTP inputs and button and allow to resend OTP
        function disableOtp() {
            submit.disabled = true;
            timeLeft.innerHTML = '';
            timer.innerHTML = otpResendAction;
            timer.classList.add('cboj__button-link');
            timer.addEventListener("click", resendOtp);
            for (i = 0; i < digits.length; i++) {
                digits[i].disabled = true;
                digits[i].value = '';
            }
        }

        //Final blockOTP when session is over or invalid
        function blockOtp(reason) {
            timeLeft.innerHTML = '';
            if (reason)
                reason.classList.remove('cboj__hidden');
            timer.classList.add('cboj__hidden');
            timeLeft.classList.add('cboj__hidden');
            submit.disabled = false;
            submit.innerHTML = "Restart";
            otpForm.action = "/ob/web/login/failed_otp";
            for (i = 0; i < digits.length; i++) {
                digits[i].disabled = true;
                digits[i].value = '';
            }
        }

        //#region Form logic
        //Concat digit to full OTP code and check if ready to submit
        for (i = 0; i < digits.length; i++) {
            digits[i].addEventListener('input', () => {
                let values = []
                digits.forEach(v => values.push(v.value))
                otp.value = values.join('');
                submit.disabled = values.includes('')
            })
        }

        //check input and move focus to next one if valid
        digitsContainer.addEventListener("input", function (e) {
            const target = e.target;
            const val = target.value;
            if (isNaN(val)) {
                target.value = "";
                return;
            }

            if (val !== "") {
                const next = target.nextElementSibling;
                if (next) {
                    next.focus();
                }
            }
        });

        //Move focus back if digit is deleted
        digitsContainer.addEventListener("keyup", function (e) {
            const target = e.target;
            const key = e.key.toLowerCase();

            if (key === "backspace" || key === "delete") {
                target.value = "";
                const prev = target.previousElementSibling;
                if (prev) {
                    prev.focus();
                }
            }
        });
        //#endregion
    }();

});
