document.addEventListener('DOMContentLoaded', function () {

// Set current lang
    const currentLang = document.documentElement.getAttribute('data-lang');

    const paymentDetailsContainer = document.getElementById('cboj__payment_details_container');
    const paymentDetailsContainerLoader = document.getElementById('cboj__payment_details_container_loader');
    const paymentDetailsErrorContainer = document.getElementById('cboj__payment_details_error_container');

//add inert true on form submit
    const consentForm = document.querySelector('#consentForm');
    consentForm.addEventListener('submit', () => {
        const otpCsrfInput = consentForm.querySelector('input[name="_csrf"]');
        const actualCsrf = document.querySelector('meta[name="_csrf"]').content;
        if (otpCsrfInput.value !== actualCsrf) {
            otpCsrfInput.value = actualCsrf;
        }
        consentForm.inert = true
    });

// Disable button
    const consentButton = document.querySelector('#cboj_consent');
    const rejectButton = document.querySelector('#cboj_reject');
    const termsSelect = document.querySelector('#confirm_terms');
    const termsSelectBlock = document.querySelector('#confirm_terms_form');
    const selectedCheckboxes = document.querySelectorAll('input[type=radio]:checked');
    if (!selectedCheckboxes.length || termsSelect.checked) {
        consentButton.classList.add('cboj__button__submit__disabled')
    }

// Radio buttons change
    const radiobuttons = document.querySelectorAll('input[type=radio]');
    [...radiobuttons].forEach(radio => radio.addEventListener('change', (event) => {
        consentForm.inert = true
        if (termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
        }
        const accountRef = document.getElementById(event.target.id).value;
        getPaymentDetails(accountRef).then((fragment) => {
            const paymentDetails = document.querySelector(".cboj__permission-form_payment_details");
            paymentDetails.outerHTML = fragment;
            consentForm.inert = false;
        }).catch((error) => {
            console.log(error);
            consentForm.inert = false;
        })
    }))

    const errorBackup = paymentDetailsErrorContainer.innerHTML;

    async function getPaymentDetails(accountRef) {
        paymentDetailsContainer.classList.add("cboj__hidden");
        paymentDetailsErrorContainer.classList.add("cboj__hidden");
        paymentDetailsContainerLoader.classList.remove("cboj__hidden");

        consentButton.classList.add('cboj__button__submit__disabled')
        rejectButton.classList.add('cboj__button__submit__disabled')
        termsSelectBlock.classList.add("cboj__hidden");

        const csrfToken = document.querySelector('meta[name="_csrf"]').content;
        const csrfHeader = document.querySelector('meta[name="_csrf_header"]').content;
        const consentRef = document.getElementById('consentRef').value;
        fetch(`/ob/web/pis/transaction_info?consentRef=${consentRef}&accountRef=${accountRef}`, {
            method: "POST",
            headers: {
                [csrfHeader]: csrfToken,
            }
        }).then(fragment => {
            if (fragment.ok) {
                fragment.text().then(html => {
                    paymentDetailsContainerLoader.classList.add("cboj__hidden");

                    paymentDetailsContainer.innerHTML = html;

                    paymentDetailsContainer.classList.remove("cboj__hidden");
                    rejectButton.classList.remove('cboj__button__submit__disabled');
                    termsSelectBlock.classList.remove("cboj__hidden");
                });
            } else {
                console.log('Error in fetch payment details')

                paymentDetailsContainerLoader.classList.add("cboj__hidden");

                paymentDetailsErrorContainer.innerHTML = errorBackup;
                paymentDetailsErrorContainer.classList.remove("cboj__hidden");
                consentButton.classList.add('cboj__button__submit__disabled');
                rejectButton.classList.remove('cboj__button__submit__disabled');
            }
        });
    }

    if (radiobuttons.length === 0) {
        const accountRef = document.getElementById("accountRef").value;
        getPaymentDetails(accountRef);
    }

    termsSelect.addEventListener('change', (event) => {
        const selectedRadioCount = [...document.querySelectorAll('input[type=radio]:checked')].length;
        if ((selectedRadioCount || radiobuttons.length === 0) && termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
        }
    })


// Modal logic
    const dialog = document.querySelector('dialog');
    const showButton = document.querySelector('.cboj__consent__terms');
    const closeButton = document.querySelector('dialog button');

    showButton.addEventListener("click", () => {
        dialog.showModal();
    });

    closeButton.addEventListener("click", () => {
        dialog.close();
    });
});
