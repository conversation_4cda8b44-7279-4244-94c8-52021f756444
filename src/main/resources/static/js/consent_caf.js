document.addEventListener('DOMContentLoaded', function () {

// Set current lang
    const currentLang = document.documentElement.getAttribute('data-lang');

//add inert true on form submit
    const consentForm = document.querySelector('#consentForm');
    consentForm.addEventListener('submit', () => {
        const otpCsrfInput = consentForm.querySelector('input[name="_csrf"]');
        const actualCsrf = document.querySelector('meta[name="_csrf"]').content;
        if (otpCsrfInput.value !== actualCsrf) {
            otpCsrfInput.value = actualCsrf;
        }
        consentForm.inert = true
    });

// Disable button
    const consentButton = document.querySelector('#cboj_consent');
    const termsSelect = document.querySelector('#confirm_terms');
    const selectedCheckboxes = document.querySelectorAll('input[type=radio]:checked');
    if (!selectedCheckboxes.length || termsSelect.checked) {
        consentButton.classList.add('cboj__button__submit__disabled')
    }

    const radiobuttons = document.querySelectorAll('input[type=radio]');
    [...radiobuttons].forEach(radio => radio.addEventListener('change', (event) => {
        if (termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
        }
    }))

    termsSelect.addEventListener('change', (event) => {
        const selectedRadioCount = [...document.querySelectorAll('input[type=radio]:checked')].length;
        if ((selectedRadioCount || radiobuttons.length == 0) && termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
        }
    })


// Modal logic
    const dialog = document.querySelector('dialog');
    const showButton = document.querySelector('.cboj__consent__terms');
    const closeButton = document.querySelector('dialog button');

    showButton.addEventListener("click", () => {
        dialog.showModal();
    });

    closeButton.addEventListener("click", () => {
        dialog.close();
    });
});
