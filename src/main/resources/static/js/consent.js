document.addEventListener('DOMContentLoaded', function () {
    var dataElement = document.getElementById('data-container');
    var selectAllText = dataElement.getAttribute('data-select-all-placeholder');
    var deselectAllText = dataElement.getAttribute('data-deselect-all-placeholder');

// Set current lang
    const currentLang = document.documentElement.getAttribute('data-lang');

//add inert true on form submit
    const consentForm = document.querySelector('#consentForm');
    consentForm.addEventListener('submit', () => {
        const otpCsrfInput = consentForm.querySelector('input[name="_csrf"]');
        const actualCsrf = document.querySelector('meta[name="_csrf"]').content;
        if (otpCsrfInput.value !== actualCsrf) {
            otpCsrfInput.value = actualCsrf;
        }
        consentForm.inert = true
    });

// Disable button
    const consentButton = document.querySelector('#cboj_consent');
    const termsSelect = document.querySelector('#confirm_terms');
    const selectedCheckboxes = document.querySelectorAll('input[type=checkbox]:not(#select_all):not(#confirm_terms):checked');
    if (!selectedCheckboxes.length || termsSelect.checked) {
        consentButton.classList.add('cboj__button__submit__disabled')
    }

// Add disable off logic
    const selectAllLabel = document.querySelector('#select_all_label');
    const checkboxes = document.querySelectorAll('input[type=checkbox]:not(#select_all):not(#confirm_terms)');
    [...checkboxes].forEach(checkbox => checkbox.addEventListener('change', () => {
        const selectedCheckboxesCount = [...document.querySelectorAll('input[type=checkbox]:not(#select_all):not(#confirm_terms):checked')].length
        if (selectedCheckboxesCount && termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            selectAllLabel.textContent = selectAllText;
            consentButton.classList.add('cboj__button__submit__disabled')
        }
    }))


// Select all checkboxes
    function selectAllCheckboxes(selectAll) {
        [...checkboxes].forEach(item => {
            item.checked = !!selectAll;
        });
        if (!!selectAll) {
            if (termsSelect.checked)
                consentButton.classList.remove('cboj__button__submit__disabled');
            selectAllLabel.textContent = deselectAllText;
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
            selectAllLabel.textContent = selectAllText;
        }
    }

    const selectAll = document.querySelector('#select_all');
    selectAll.addEventListener('change', (event) => {
        selectAllCheckboxes(event.target.checked)
    })
    termsSelect.addEventListener('change', (event) => {
        const selectedCheckboxesCount = [...document.querySelectorAll('input[type=checkbox]:not(#select_all):not(#confirm_terms):checked')].length;
        if (selectedCheckboxesCount && termsSelect.checked) {
            consentButton.classList.remove('cboj__button__submit__disabled');
        } else {
            consentButton.classList.add('cboj__button__submit__disabled')
        }
    })


// Modal logic
    const dialog = document.querySelector('dialog');
    const showButton = document.querySelector('.cboj__consent__terms');
    const closeButton = document.querySelector('dialog button');


    showButton.addEventListener("click", () => {
        dialog.showModal();
    });

    closeButton.addEventListener("click", () => {
        dialog.close();
    });
});
