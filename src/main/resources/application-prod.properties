server.port=8444
server.address=0.0.0.0

# JDBC Configuration
#===============================
spring.datasource.url=**********************************************************************
spring.datasource.username=ENC(RwUh0Xbe0Ku0Dr10m45XaqRqgnfEYD0I)
spring.datasource.password=ENC(eZuKuvAj0zdBVDHyPKAiUTBhflqZbqwY)
spring.flyway.enabled=true
#===============================

# SSL certificate
#===============================
server.ssl.key-store-password=ENC(qR6QJ3h6U9a+FBkSBvArDPW5sebySnYf)
server.ssl.key-password=ENC(qR6QJ3h6U9a+FBkSBvArDPW5sebySnYf)
#===============================

# Encryption
#===============================
ciam.private.key.password=ENC(xJV2sPFvQPS6HeJ9GfBG5Z4M3PY7z77Z)
ciam.encryption.enable=true
#===============================

# RedHat SSO
#===============================
# Keycloak Endpoint Settings
#-------------------------------
keycloak.uri.base-uri=https://ssoprodcboj.capitalbank.jo:8443/auth
#-------------------------------
# Realms names
#-------------------------------
keycloak.uri.realm=cboj-ob
keycloak.uri.mulesoft.realm=cboj-mai
#-------------------------------
# Administrator user credentials
#-------------------------------
keycloak.admin.name=ENC(wtY0P8KN/03mBEn8tXYGvw==)
keycloak.admin.password=ENC(7VzrU53gEzW3dT7IxYL5Zg==)
#-------------------------------
# Client credentials for introspecting access tokens
#-------------------------------
keycloak.introspect.client_id=ENC(CDhl6RiBpYrFQWGKc+BjVDRPmkxP4xGl)
keycloak.introspect.client_secret=ENC(Zu+Zm1aiqqOUsod4wJAiLC6S5gpK9Nw27y7dUw2BeBTpFFcq/9xyWpufx4CfDTQp)
#===============================

# MuleSoft
#===============================
# Headers and client credentials for Mulesoft
#-------------------------------
ciam.mulesoft.channel=CIAMCHL
ciam.mulesoft.bank=CBOJ
ciam.mulesoft.client-id=ENC(pG4sT2f5hEnZ/jmanzSAlWQu+93W5bW7S5ZT8IeEbE8V0Mf6n2nMoxRZWHS80ktW)
ciam.mulesoft.client-secret=ENC(Rlvtdok/yzBfwWoQ9AGcfJlaEw21sc0486gUp6BYTeWC6Z+5CcCQXJri8zX+kD7J)
#-------------------------------
# Endpoints
#-------------------------------
ciam.mulesoft.url=https://MAICTRLBCKPROD.capitalbank.jo
#-------------------------------
ciam.mulesoft.submit-otp-attempts=3
ciam.mulesoft.request-otp-attempts=3
ciam.mulesoft.otp-session-time=5
ciam.mulesoft.renew-otp-session-time=1
ciam.mulesoft.retry-attempts-count=2
#===============================

# Codebase
#===============================
# Headers
#-------------------------------
ciam.codebase.channel-id=01
ciam.codebase.bank-id=01
ciam.codebase.country-code=01
ciam.codebase.ip=***********
ciam.codebase.device-id=DE1B13CC-0DEC-4723-BD41-968D1B4A76BA
ciam.codebase.latitude=24.9038567
ciam.codebase.longitude=67.0804718
ciam.codebase.platform=A
ciam.codebase.mobile-model=Samsung
ciam.codebase.app-version=1.4.3
ciam.codebase.is-refresh-token=True
#-------------------------------
# Payload
#-------------------------------
ciam.codebase.parent-version=1.0
ciam.codebase.version=1.0.2
ciam.codebase.platform-payload=W
#-------------------------------
# Endpoints
#-------------------------------
ciam.codebase.url=https://hq-mobbnku-app.capitalbank.jo:2443
#===============================

# API scopes
#===============================
# Admin
#-------------------------------
ciam.api.scope.admin=admin_api
#-------------------------------
# Mobile
#-------------------------------
ciam.api.scope.mobile=mobile_api
#-------------------------------
# Mulesoft
#-------------------------------
ciam.api.scope.mulesoft=mule_api
#===============================

# Other properties
#===============================
ciam.base.url=https://connect.api.capitalbank.jo:8447
ciam.mulesoft.otp-template=Your OTP Number is [OTP]
ciam.scheduler.expired.crone=0 * * * * *
ciam.consent.expired=365
ciam.consent.login.ttl=5
ciam.auth.userless.controller.enabled=true
#===============================

logging.level.org.springframework.web.client=DEBUG
logging.level.com.cboj.ciam.service.auth=DEBUG

country.codes=AF,AX,AL,DZ,AS,AD,AO,AI,AQ,AG,AR,AM,AW,AU,AT,AZ,BS,BH,BD,BB,BY,BE,BZ,BJ,BM,BT,BO,BQ,BA,BW,BV,BR,IO,BN,BG,BF,BI,KH,CM,CA,CV,KY,CF,TD,CL,CN,CX,CC,CO,KM,CG,CD,CK,CR,CI,HR,CU,CW,CY,CZ,DK,DJ,DM,DO,EC,EG,SV,GQ,ER,EE,ET,FK,FO,FJ,FI,FR,GF,PF,TF,GA,GM,GE,DE,GH,GI,GR,GL,GD,GP,GU,GT,GG,GN,GW,GY,HT,HM,VA,HN,HK,HU,IS,IN,ID,IR,IQ,IE,IM,IL,IT,JM,JP,JE,JO,KZ,KE,KI,KP,KR,XK,KW,KG,LA,LV,LB,LS,LR,LY,LI,LT,LU,MO,MK,MG,MW,MY,MV,ML,MT,MH,MQ,MR,MU,YT,MX,FM,MD,MC,MN,ME,MS,MA,MZ,MM,NA,NR,NP,NL,AN,NC,NZ,NI,NE,NG,NU,NF,MP,NO,OM,PK,PW,PS,PA,PG,PY,PE,PH,PN,PL,PT,PR,QA,RS,RE,RO,RU,RW,BL,SH,KN,LC,MF,PM,VC,WS,SM,ST,SA,SN,CS,SC,SL,SG,SX,SK,SI,SB,SO,ZA,GS,SS,ES,LK,SD,SR,SJ,SZ,SE,CH,SY,TW,TJ,TZ,TH,TL,TG,TK,TO,TT,TN,TR,XT,TM,TC,TV,UG,UA,AE,GB,US,UM,UY,UZ,VU,VE,VN,VG,VI,WF,EH,YE,ZM,ZW
