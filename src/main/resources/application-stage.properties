server.port=8444
server.address=0.0.0.0

# JDBC Configuration
#===============================
spring.datasource.url=***************************************************************************************
spring.datasource.username=app_admin
spring.datasource.password=ENC(txSx3jtIu4HNrEs/JFtWbquhqqQfqMKa)
spring.flyway.enabled=true
#===============================

# SSL certificate
#===============================
server.ssl.key-store-password=ENC(41HeV970iNzZRGMjB13jvIPZIV+W1Jvb)
server.ssl.key-password=ENC(4h2fKbr99ryz+tbTXFapTAJ/iE9lCMRt)

server.ssl.key-store-type=PKCS12
server.ssl.key-alias=ciamservice
server.ssl.protocol=TLS
server.ssl.ciphers=TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
#===============================

# Encryption
#===============================
ciam.private.key.password=ENC(0qxLh+FEFTWuzHMxod4l+yDTMm/btJ4R)
ciam.encryption.enable=true
#===============================

# RedHat SSO
#===============================
# Keycloak Endpoint Settings
#-------------------------------
keycloak.uri.base-uri=https://sso-stg-cboj.capitalbank.jo:8443/auth
#-------------------------------
# Realms names
#-------------------------------
keycloak.uri.realm=cboj-ob
keycloak.uri.mulesoft.realm=cboj-mai
#-------------------------------
# Administrator user credentials
#-------------------------------
keycloak.admin.name=ENC(c6PhSqpH3V3HDq34yeiObA==)
keycloak.admin.password=ENC(oonAISapHn4VTr+Eb6SIIA==)
#-------------------------------
# Client credentials for introspecting access tokens
#-------------------------------
keycloak.introspect.client_id=ENC(nii+R1OkPW4pWPzHjmz90fiuhGgoR1ee)
keycloak.introspect.client_secret=ENC(oxX98ncFjllmuzovkDhYMMUUqg2VnGeNmt/6Zy3iDNOZjvW487nvNTp6WXF4lDax)
#===============================

# MuleSoft
#===============================
# Headers and client credentials for Mulesoft
#-------------------------------
ciam.mulesoft.channel=OBCHL
ciam.mulesoft.bank=CBOJ
ciam.mulesoft.client-id=ENC(c81G4AKz8+exV0H+TslZdS+MWNB0rg4yt31N7jkDkde7z/pr19l5QdywG8thmLl5)
ciam.mulesoft.client-secret=ENC(rA2yk9GEmwjnpuU6VVGr0qGBTO4qkwnividi7oQPmrSYPJXTBT8CBDuk1w0cAfL6)
#-------------------------------
# Endpoints
#-------------------------------
ciam.mulesoft.url=https://maictrlstg.capitalbank.jo
#-------------------------------
ciam.mulesoft.submit-otp-attempts=3
ciam.mulesoft.request-otp-attempts=3
ciam.mulesoft.otp-session-time=5
ciam.mulesoft.renew-otp-session-time=1
ciam.mulesoft.retry-attempts-count=2
#===============================

# Codebase
#===============================
# Headers
#-------------------------------
ciam.codebase.channel-id=01
ciam.codebase.bank-id=01
ciam.codebase.country-code=01
ciam.codebase.ip=***********
ciam.codebase.device-id=DE1B13CC-0DEC-4723-BD41-968D1B4A76BA
ciam.codebase.latitude=24.9038567
ciam.codebase.longitude=67.0804718
ciam.codebase.platform=A
ciam.codebase.mobile-model=Samsung
ciam.codebase.app-version=1.4.3
ciam.codebase.is-refresh-token=True
#-------------------------------
# Payload
#-------------------------------
ciam.codebase.parent-version=1.0
ciam.codebase.version=1.0.1
ciam.codebase.platform-payload=OB
#-------------------------------
# Endpoints
#-------------------------------
ciam.codebase.url=https://hq-mobbnku-app.capitalbank.jo:2443
#===============================

# API scopes
#===============================
# Admin
#-------------------------------
ciam.api.scope.admin=admin_api
#-------------------------------
# Mobile
#-------------------------------
ciam.api.scope.mobile=mobile_api
#-------------------------------
# Mulesoft
#-------------------------------
ciam.api.scope.mulesoft=mule_api
#===============================

# Other properties
#===============================
ciam.base.url=https://TestConnect.api.capitalbank.jo:8447
ciam.mulesoft.otp-template=Your OTP Number is [OTP]
ciam.scheduler.expired.crone=0 * * * * *
ciam.consent.expired=365
ciam.consent.login.ttl=5
ciam.auth.userless.controller.enabled=true
#===============================
# In application.properties
country.codes=AF,AX,AL,DZ,AS,AD,AO,AI,AQ,AG,AR,AM,AW,AU,AT,AZ,BS,BH,BD,BB,BY,BE,BZ,BJ,BM,BT,BO,BQ,BA,BW,BV,BR,IO,BN,BG,BF,BI,KH,CM,CA,CV,KY,CF,TD,CL,CN,CX,CC,CO,KM,CG,CD,CK,CR,CI,HR,CU,CW,CY,CZ,DK,DJ,DM,DO,EC,EG,SV,GQ,ER,EE,ET,FK,FO,FJ,FI,FR,GF,PF,TF,GA,GM,GE,DE,GH,GI,GR,GL,GD,GP,GU,GT,GG,GN,GW,GY,HT,HM,VA,HN,HK,HU,IS,IN,ID,IR,IQ,IE,IM,IL,IT,JM,JP,JE,JO,KZ,KE,KI,KP,KR,XK,KW,KG,LA,LV,LB,LS,LR,LY,LI,LT,LU,MO,MK,MG,MW,MY,MV,ML,MT,MH,MQ,MR,MU,YT,MX,FM,MD,MC,MN,ME,MS,MA,MZ,MM,NA,NR,NP,NL,AN,NC,NZ,NI,NE,NG,NU,NF,MP,NO,OM,PK,PW,PS,PA,PG,PY,PE,PH,PN,PL,PT,PR,QA,RS,RE,RO,RU,RW,BL,SH,KN,LC,MF,PM,VC,WS,SM,ST,SA,SN,CS,SC,SL,SG,SX,SK,SI,SB,SO,ZA,GS,SS,ES,LK,SD,SR,SJ,SZ,SE,CH,SY,TW,TJ,TZ,TH,TL,TG,TK,TO,TT,TN,TR,XT,TM,TC,TV,UG,UA,AE,GB,US,UM,UY,UZ,VU,VE,VN,VG,VI,WF,EH,YE,ZM,ZW
