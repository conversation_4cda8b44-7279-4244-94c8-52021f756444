server.port=8444
server.address=0.0.0.0

# JDBC Configuration
#===============================
spring.datasource.url=*****************************************************************************
spring.datasource.username=ENC(t1YBgyXLUuBsinCkM2vC2cgecRjT9P+/)
spring.datasource.password=ENC(vkjPTsoyy8qc4ATbWWxyXX4j4VpZuGb9)
spring.flyway.enabled=true
#===============================

# SSL certificate
#===============================
server.ssl.key-store-password=ENC(41HeV970iNzZRGMjB13jvIPZIV+W1Jvb)
server.ssl.key-password=ENC(4h2fKbr99ryz+tbTXFapTAJ/iE9lCMRt)
#===============================

# Encryption
#===============================
ciam.private.key.password=doesnt_matter
ciam.encryption.enable=false
#===============================

# RedHat SSO
#===============================
# Keycloak Endpoint Settings
#-------------------------------
keycloak.uri.base-uri=https://sandbox.capitalbank.jo:8443/auth
#-------------------------------
# Realms names
#-------------------------------
keycloak.uri.realm=cboj-ob
keycloak.uri.mulesoft.realm=cboj-ob
#-------------------------------
# Administrator user credentials
#-------------------------------
keycloak.admin.name=ENC(JK6J1N01tml08wYtIeRG6NVhJKBW7DYv)
keycloak.admin.password=ENC(x/0zbT1I+oOLcdHmCj+zzCnrTNsn8z5z)
#-------------------------------
# Client credentials for introspecting access tokens
#-------------------------------
keycloak.introspect.client_id=ENC(b9KYejVMv8sxIYprtv/FGd1r4WrF/1ti)
keycloak.introspect.client_secret=ENC(608tEnBvF7fvmnzuV6kr28RgAEVLvO9dIticYuWDLMFxr3Z0LoNX7hEBfbk0LBH/)
#===============================

# MuleSoft
#===============================
# Headers and client credentials for Mulesoft
#-------------------------------
ciam.mulesoft.channel=doesnt_matter
ciam.mulesoft.bank=doesnt_matter
ciam.mulesoft.client-id=ENC(tzrLfDDXJqPURQNRYiPedR9bgp6SqBDVXSa0hTBSInxq+TV/d4hOw9c42INsu2Am)
ciam.mulesoft.client-secret=ENC(YCzRTixZMC55CtWyKpzIRiY+NuJER+b+DQLiJYcq5xooJpegrb1w7Uc47pwcUja8)
#-------------------------------
# Endpoints
#-------------------------------
ciam.mulesoft.url=https://maictrlsandbox.capitalbank.jo
# MuleSoft
#===============================
# Endpoints
#-------------------------------
# URL for OTP (One-Time Password) validation, e.g. ${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/validate
ciam.mulesoft.otp-validate-url=${ciam.mulesoft.url}/sandbox/ciam/v1/dtech/otp/validate
# URL for OTP (One-Time Password) request, e.g. ${ciam.mulesoft.url}/utilities-api/v1/dtech/otp/request
ciam.mulesoft.otp-request-url=${ciam.mulesoft.url}/sandbox/ciam/v1/dtech/otp/request
# URL for account related operations, e.g. ${ciam.mulesoft.url}/customers-api/v1/customers
ciam.mulesoft.accounts-url=${ciam.mulesoft.url}/sandbox/ciam/v1/customers
# URL for user eligibility operation, e.g. ${ciam.mulesoft.url}/customers-api/v1/ob/customers
ciam.mulesoft.eligibility-url=${ciam.mulesoft.url}/sandbox/ciam/v1/ob/customers
# URL for getting information about payment transaction operation, e.g. ${ciam.mulesoft.url}/pis/ciam/v1/transaction-info
ciam.mulesoft.transaction-info-url=${ciam.mulesoft.url}/sandbox/pis/ciam/v1/transaction-info
#-------------------------------
#-------------------------------
ciam.mulesoft.submit-otp-attempts=3
ciam.mulesoft.request-otp-attempts=3
ciam.mulesoft.otp-session-time=5
ciam.mulesoft.renew-otp-session-time=1
ciam.mulesoft.retry-attempts-count=2
#===============================

# Codebase
#===============================
# Headers
#-------------------------------
ciam.codebase.channel-id=doesnt_matter
ciam.codebase.bank-id=doesnt_matter
ciam.codebase.country-code=doesnt_matter
ciam.codebase.ip=doesnt_matter
ciam.codebase.device-id=doesnt_matter
ciam.codebase.latitude=doesnt_matter
ciam.codebase.longitude=doesnt_matter
ciam.codebase.platform=doesnt_matter
ciam.codebase.mobile-model=doesnt_matter
ciam.codebase.app-version=doesnt_matter
ciam.codebase.is-refresh-token=doesnt_matter
#-------------------------------
# Payload
#-------------------------------
ciam.codebase.parent-version=doesnt_matter
ciam.codebase.version=doesnt_matter
ciam.codebase.platform-payload=doesnt_matter
#-------------------------------
# Endpoints
#-------------------------------
ciam.codebase.url=doesnt_matter
#===============================

# API scopes
#===============================
# Admin
#-------------------------------
ciam.api.scope.admin=admin_api
#-------------------------------
# Mobile
#-------------------------------
ciam.api.scope.mobile=mobile_api
#-------------------------------
# Mulesoft
#-------------------------------
ciam.api.scope.mulesoft=mule_api
#===============================

# Other properties
#===============================
ciam.base.url=https://sandboxauth.api.capitalbank.jo:8447
ciam.mulesoft.otp-template=Your OTP Number is [OTP]
ciam.scheduler.expired.crone=0 * * * * *
ciam.consent.expired=365
ciam.consent.login.ttl=5
ciam.auth.userless.controller.enabled=true
#===============================
country.codes=AF,AX,AL,DZ,AS,AD,AO,AI,AQ,AG,AR,AM,AW,AU,AT,AZ,BS,BH,BD,BB,BY,BE,BZ,BJ,BM,BT,BO,BQ,BA,BW,BV,BR,IO,BN,BG,BF,BI,KH,CM,CA,CV,KY,CF,TD,CL,CN,CX,CC,CO,KM,CG,CD,CK,CR,CI,HR,CU,CW,CY,CZ,DK,DJ,DM,DO,EC,EG,SV,GQ,ER,EE,ET,FK,FO,FJ,FI,FR,GF,PF,TF,GA,GM,GE,DE,GH,GI,GR,GL,GD,GP,GU,GT,GG,GN,GW,GY,HT,HM,VA,HN,HK,HU,IS,IN,ID,IR,IQ,IE,IM,IL,IT,JM,JP,JE,JO,KZ,KE,KI,KP,KR,XK,KW,KG,LA,LV,LB,LS,LR,LY,LI,LT,LU,MO,MK,MG,MW,MY,MV,ML,MT,MH,MQ,MR,MU,YT,MX,FM,MD,MC,MN,ME,MS,MA,MZ,MM,NA,NR,NP,NL,AN,NC,NZ,NI,NE,NG,NU,NF,MP,NO,OM,PK,PW,PS,PA,PG,PY,PE,PH,PN,PL,PT,PR,QA,RS,RE,RO,RU,RW,BL,SH,KN,LC,MF,PM,VC,WS,SM,ST,SA,SN,CS,SC,SL,SG,SX,SK,SI,SB,SO,ZA,GS,SS,ES,LK,SD,SR,SJ,SZ,SE,CH,SY,TW,TJ,TZ,TH,TL,TG,TK,TO,TT,TN,TR,XT,TM,TC,TV,UG,UA,AE,GB,US,UM,UY,UZ,VU,VE,VN,VG,VI,WF,EH,YE,ZM,ZW
