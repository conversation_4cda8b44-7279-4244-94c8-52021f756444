# This is a template for the environment configuration properties file.
# Rename it to application-<env>.properties and fill out the properties below.

# HTTP listener host network interface, e.g. 0.0.0.0.
server.address=
# HTTP listener port on the host machine, e.g. 8444.
server.port=

# JDBC Configuration
#===============================
# JDBC URL for MSSQL to connect to the database, e.g. jdbc:sqlserver://************;databaseName=ciam-stg;encrypt=false.
spring.datasource.url=
# MSSQL database user name. Jasypt encrypted, e.g. ENC(value)
spring.datasource.username=
# MSSQL database user password. Jasypt encrypted, e.g. ENC(value)
spring.datasource.password=
# Enable automatic DB migrations, e.g. true/false
spring.flyway.enabled=
#===============================

# SSL certificate
#===============================
# Password for the key store. Jasypt encrypted, e.g. ENC(value)
server.ssl.key-store-password=
# Password for the key in the key store. Jasypt encrypted, e.g. ENC(value)
server.ssl.key-password=
#===============================

# Encryption
#===============================
# Password for the private key which used for username encryption. Jasypt encrypted, e.g. ENC(value)
ciam.private.key.password=
# Encryption process sign. true / false
ciam.encryption.enable=
#===============================

# RedHat SSO
#===============================
# Keycloak Endpoint Settings
#-------------------------------
# The base URL of the Keycloak endpoints, excluding "/auth"
keycloak.uri.base-uri=
#-------------------------------
# Realms names
#-------------------------------
# Main realm to store TPP client applications
keycloak.uri.realm=
# MuleSoft client applications realm
keycloak.uri.mulesoft.realm=
#-------------------------------
# Administrator user credentials
#-------------------------------
# Admin username Jasypt encrypted, e.g. ENC(value)
keycloak.admin.name=
# Admin password Jasypt encrypted, e.g. ENC(value)
keycloak.admin.password=
#-------------------------------
# Client credentials for introspecting access tokens
#-------------------------------
# Client id Jasypt encrypted, e.g. ENC(value)
keycloak.introspect.client_id=
# Client secret Jasypt encrypted, e.g. ENC(value)
keycloak.introspect.client_secret=
#===============================

# MuleSoft
#===============================
# Headers and client credentials for Mulesoft
#-------------------------------
# 'Channel' header, e.g. DEVCHL
ciam.mulesoft.channel=
# 'Bank' header, e.g. CBOJ
ciam.mulesoft.bank=
# Client Id for mulesoft operations Jasypt encrypted, e.g. ENC(value)
ciam.mulesoft.client-id=
# Client secret for mulesoft operations Jasypt encrypted, e.g. ENC(value)
ciam.mulesoft.client-secret=
#-------------------------------
# Endpoints
#-------------------------------
# Base URL for MuleSoft APIs
ciam.mulesoft.url=
#-------------------------------
# Maximum attempts for validating OTP, e.g. 3
ciam.mulesoft.submit-otp-attempts=
# Maximum attempts for requesting OTP, e.g. 3
ciam.mulesoft.request-otp-attempts=
# Session time for OTP (in minutes)
ciam.mulesoft.otp-session-time=
# Session time for renewing OTP (in minutes)
ciam.mulesoft.renew-otp-session-time=
# Count attempts for retry request, e.g. 1
ciam.mulesoft.retry-attempts-count=
#===============================

# Codebase
#===============================
# Headers
#-------------------------------
# 'ChannelId' header, e.g. 01
ciam.codebase.channel-id=
# 'BankId' header, e.g. 01
ciam.codebase.bank-id=
# 'CountryCode' header, e.g. 01
ciam.codebase.country-code=
# 'IP' header, e.g. ***********
ciam.codebase.ip=
# 'DeviceID' header, e.g. DE1B13CC-0DEC-4723-BD41-968D1B4A76BA
ciam.codebase.device-id=
# 'Latitude' header, e.g. 24.9038567
ciam.codebase.latitude=
# 'Longitude' header, e.g. 67.0804718
ciam.codebase.longitude=
# 'Platform' header, e.g. A
ciam.codebase.platform=
# 'MobileModel' header, e.g. Samsung
ciam.codebase.mobile-model=
# 'AppVersion' header, e.g. 1.4.3
ciam.codebase.app-version=
# 'IsRefreshToken' header, e.g. True
ciam.codebase.is-refresh-token=
#-------------------------------
# Payload
#-------------------------------
# 'parentVersion' payload attribute, e.g. 1.4.3
ciam.codebase.parent-version=
# 'version' payload attribute, e.g. 1.4.3
ciam.codebase.version=
# 'Platform' header, e.g. W
ciam.codebase.platform-payload=
#-------------------------------
# Endpoints
#-------------------------------
# Base URL for Codebase APIs
ciam.codebase.url=
#===============================

# API scopes
#===============================
# Admin
#-------------------------------
# Scope for admin API
ciam.api.credentials.admin.name=
#-------------------------------
# Mobile
#-------------------------------
# Scope for mobile API
ciam.api.credentials.mobile.name=
#-------------------------------
# Mulesoft
#-------------------------------
# Scope for mulesoft API
ciam.api.credentials.mulesoft.name=
#===============================

# Other properties
#===============================
# Ciam service base url
ciam.base.url=
# OTP message template
ciam.mulesoft.otp-template=
# Crone expression for scheduling expired tasks, e.g. 0 * * * * *
ciam.scheduler.expired.crone=
# Expiry of Consent (in days)
ciam.consent.expired=
#TTL (Time To Live) of login consent (in minutes)
ciam.consent.login.ttl=
# Attribute for enable or disable controllers for emulation user login process, e.g. true/false
ciam.auth.userless.controller.enabled=
#===============================
